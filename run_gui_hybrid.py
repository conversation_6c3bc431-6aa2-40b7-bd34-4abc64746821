#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Run GUI using system Python with venv libraries
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Add venv libraries to path (for other dependencies)
venv_path = project_root / "venv_py38" / "lib" / "python3.8" / "site-packages"
if venv_path.exists():
    sys.path.insert(0, str(venv_path))

def test_imports():
    """Test if we can import required modules"""
    print("🔍 اختبار المكتبات...")
    
    # Test tkinter (system)
    try:
        import tkinter as tk
        print("✅ tkinter متوفر")
    except ImportError as e:
        print(f"❌ tkinter غير متوفر: {e}")
        return False
    
    # Test other required modules
    required_modules = ['requests', 'json', 'threading']
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} متوفر")
        except ImportError as e:
            print(f"❌ {module} غير متوفر: {e}")
            return False
    
    return True

def create_simple_gui():
    """Create a simple tkinter GUI for the application"""
    import tkinter as tk
    from tkinter import ttk, messagebox, scrolledtext
    import threading
    import requests
    import json
    
    class NoMusicGUI:
        def __init__(self, root):
            self.root = root
            self.root.title("No Music Desktop - إزالة الموسيقى من الفيديوهات")
            self.root.geometry("900x700")
            self.root.configure(bg='#f0f0f0')
            
            # Server URL
            self.server_url = "http://127.0.0.1:3333"
            
            self.create_widgets()
            self.check_server_status()
        
        def create_widgets(self):
            # Main frame
            main_frame = tk.Frame(self.root, bg='#f0f0f0', padx=20, pady=20)
            main_frame.pack(fill='both', expand=True)
            
            # Title
            title_label = tk.Label(
                main_frame, 
                text="🎵 No Music Desktop", 
                font=("Arial", 18, "bold"),
                bg='#f0f0f0',
                fg='#2c3e50'
            )
            title_label.pack(pady=(0, 10))
            
            subtitle_label = tk.Label(
                main_frame, 
                text="إزالة الموسيقى من فيديوهات YouTube", 
                font=("Arial", 12),
                bg='#f0f0f0',
                fg='#7f8c8d'
            )
            subtitle_label.pack(pady=(0, 20))
            
            # URL input frame
            url_frame = tk.LabelFrame(main_frame, text="رابط الفيديو", bg='#f0f0f0', font=("Arial", 12, "bold"))
            url_frame.pack(fill='x', pady=(0, 15))
            
            url_inner_frame = tk.Frame(url_frame, bg='#f0f0f0')
            url_inner_frame.pack(fill='x', padx=10, pady=10)
            
            self.url_entry = tk.Entry(
                url_inner_frame, 
                font=("Arial", 12),
                relief='solid',
                borderwidth=1
            )
            self.url_entry.pack(fill='x', pady=(0, 10))
            
            # Buttons frame
            buttons_frame = tk.Frame(url_inner_frame, bg='#f0f0f0')
            buttons_frame.pack(fill='x')
            
            self.process_btn = tk.Button(
                buttons_frame,
                text="🚀 معالجة الفيديو",
                command=self.process_video,
                bg='#27ae60',
                fg='white',
                font=("Arial", 12, "bold"),
                padx=20,
                pady=8,
                relief='flat',
                cursor='hand2'
            )
            self.process_btn.pack(side='left', padx=(0, 10))
            
            self.refresh_btn = tk.Button(
                buttons_frame,
                text="🔄 تحديث الحالة",
                command=self.refresh_status,
                bg='#3498db',
                fg='white',
                font=("Arial", 12, "bold"),
                padx=20,
                pady=8,
                relief='flat',
                cursor='hand2'
            )
            self.refresh_btn.pack(side='left', padx=(0, 10))
            
            self.clear_btn = tk.Button(
                buttons_frame,
                text="🗑️ مسح",
                command=self.clear_url,
                bg='#e74c3c',
                fg='white',
                font=("Arial", 12, "bold"),
                padx=20,
                pady=8,
                relief='flat',
                cursor='hand2'
            )
            self.clear_btn.pack(side='left')
            
            # Status frame
            status_frame = tk.LabelFrame(main_frame, text="حالة الخادم والمهام", bg='#f0f0f0', font=("Arial", 12, "bold"))
            status_frame.pack(fill='both', expand=True, pady=(15, 0))
            
            status_inner_frame = tk.Frame(status_frame, bg='#f0f0f0')
            status_inner_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            self.status_text = scrolledtext.ScrolledText(
                status_inner_frame,
                height=15,
                font=("Consolas", 10),
                bg='#2c3e50',
                fg='#ecf0f1',
                insertbackground='#ecf0f1',
                relief='flat',
                borderwidth=0
            )
            self.status_text.pack(fill='both', expand=True)
            
            # Add welcome message
            self.log_message("🎉 مرحباً بك في No Music Desktop!")
            self.log_message("📋 هذا التطبيق يساعدك على إزالة الموسيقى من فيديوهات YouTube")
            self.log_message("=" * 60)
        
        def clear_url(self):
            """Clear URL entry"""
            self.url_entry.delete(0, tk.END)
            self.log_message("🗑️ تم مسح الرابط")
        
        def check_server_status(self):
            """Check if server is running"""
            try:
                response = requests.get(f"{self.server_url}/health", timeout=3)
                if response.status_code == 200:
                    self.log_message("✅ الخادم يعمل بشكل طبيعي")
                    self.process_btn.config(state='normal')
                    return True
                else:
                    self.log_message("⚠️ الخادم يعمل لكن هناك مشكلة")
                    return False
            except Exception as e:
                self.log_message("❌ الخادم غير متصل")
                self.log_message("💡 يرجى تشغيل الخادم أولاً:")
                self.log_message("   python run_server_only.py")
                self.process_btn.config(state='disabled')
                return False
        
        def log_message(self, message):
            """Add message to status text"""
            import datetime
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            self.status_text.insert(tk.END, f"[{timestamp}] {message}\n")
            self.status_text.see(tk.END)
            self.root.update()
        
        def process_video(self):
            """Process video URL"""
            url = self.url_entry.get().strip()
            if not url:
                messagebox.showerror("خطأ", "يرجى إدخال رابط YouTube")
                return
            
            if not url.startswith(('http://', 'https://')):
                messagebox.showerror("خطأ", "يرجى إدخال رابط صحيح يبدأ بـ http:// أو https://")
                return
            
            self.log_message(f"🚀 بدء معالجة: {url}")
            self.process_btn.config(state='disabled', text='⏳ جاري المعالجة...')
            
            # Run in thread to avoid blocking UI
            threading.Thread(target=self._process_video_thread, args=(url,), daemon=True).start()
        
        def _process_video_thread(self, url):
            """Process video in background thread"""
            try:
                data = {"url": url}
                self.log_message("📡 إرسال طلب المعالجة...")
                response = requests.post(f"{self.server_url}/process", json=data, timeout=60)
                
                if response.status_code == 200:
                    result = response.json()
                    task_id = result.get('task_id', 'غير محدد')
                    output_dir = result.get('output_dir', 'غير محدد')
                    
                    self.log_message(f"✅ تم إنشاء المهمة بنجاح!")
                    self.log_message(f"🆔 معرف المهمة: {task_id}")
                    self.log_message(f"📁 مجلد الحفظ: {output_dir}")
                    self.log_message("⏰ يمكنك متابعة التقدم من خلال 'تحديث الحالة'")
                else:
                    error_msg = response.text
                    self.log_message(f"❌ خطأ في المعالجة: {error_msg}")
                    
            except requests.exceptions.Timeout:
                self.log_message("⏰ انتهت مهلة الاتصال - قد تكون المعالجة قيد التنفيذ")
            except Exception as e:
                self.log_message(f"❌ خطأ في الاتصال: {str(e)}")
            
            finally:
                # Re-enable button
                self.root.after(0, lambda: self.process_btn.config(state='normal', text='🚀 معالجة الفيديو'))
        
        def refresh_status(self):
            """Refresh server and tasks status"""
            self.log_message("🔄 تحديث الحالة...")
            
            # Check server
            if not self.check_server_status():
                return
            
            # Get tasks
            try:
                response = requests.get(f"{self.server_url}/tasks", timeout=5)
                if response.status_code == 200:
                    tasks = response.json()
                    self.log_message(f"📋 إجمالي المهام: {len(tasks)}")
                    
                    if tasks:
                        self.log_message("📝 آخر 5 مهام:")
                        for task in tasks[-5:]:  # Show last 5 tasks
                            task_id = task.get('id', 'غير محدد')[:8]
                            status = task.get('status', 'غير محدد')
                            title = task.get('title', 'غير محدد')[:50]
                            self.log_message(f"   🔹 [{task_id}] {title}: {status}")
                    else:
                        self.log_message("📝 لا توجد مهام حالياً")
                else:
                    self.log_message("⚠️ لا يمكن الحصول على قائمة المهام")
            except Exception as e:
                self.log_message(f"❌ خطأ في الحصول على المهام: {str(e)}")
    
    # Create and run GUI
    root = tk.Tk()
    app = NoMusicGUI(root)
    
    try:
        root.mainloop()
        return 0
    except KeyboardInterrupt:
        return 0
    except Exception as e:
        print(f"❌ خطأ في الواجهة الرسومية: {e}")
        return 1

def main():
    """Main function"""
    print("=" * 50)
    print("No Music Desktop - Hybrid GUI (System Python + Venv)")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("❌ بعض المكتبات المطلوبة غير متوفرة")
        return 1
    
    print("🚀 تشغيل الواجهة الرسومية...")
    return create_simple_gui()

if __name__ == "__main__":
    sys.exit(main())
