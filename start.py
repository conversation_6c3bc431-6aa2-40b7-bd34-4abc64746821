#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
No Music Desktop - Unified Startup Script
تشغيل موحد لبرنامج إزالة الموسيقى من الفيديوهات
"""

import sys
import os
import subprocess
import time
import webbrowser
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """Check if all required dependencies are installed"""
    print("🔍 فحص المكتبات المطلوبة...")
    
    # Core packages (required)
    core_packages = ['fastapi', 'uvicorn', 'yt_dlp', 'requests', 'aiofiles']
    missing_core = []

    # Check core packages
    for package in core_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_core.append(package)

    if missing_core:
        print("❌ المكتبات الأساسية التالية مفقودة:")
        for package in missing_core:
            print(f"   - {package}")
        print("\nيرجى تثبيت المكتبات الأساسية:")
        print("pip install fastapi uvicorn yt-dlp requests aiofiles python-multipart pydantic")
        return False

    print("✅ جميع المكتبات الأساسية متوفرة")
    return True

def create_web_interface():
    """Create the web interface HTML file"""
    html_content = """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>No Music Desktop</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; padding: 20px;
        }
        .container {
            max-width: 800px; margin: 0 auto; background: white;
            border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white; padding: 30px; text-align: center;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { font-size: 1.2em; opacity: 0.9; }
        .content { padding: 40px; }
        .form-group { margin-bottom: 25px; }
        .form-group label {
            display: block; margin-bottom: 8px; font-weight: bold;
            color: #2c3e50; font-size: 1.1em;
        }
        .form-group input {
            width: 100%; padding: 15px; border: 2px solid #ecf0f1;
            border-radius: 8px; font-size: 16px; transition: border-color 0.3s;
        }
        .form-group input:focus { outline: none; border-color: #3498db; }
        .buttons { display: flex; gap: 15px; flex-wrap: wrap; }
        .btn {
            padding: 15px 30px; border: none; border-radius: 8px;
            font-size: 16px; font-weight: bold; cursor: pointer;
            transition: all 0.3s; text-decoration: none;
            display: inline-block; text-align: center;
        }
        .btn-primary { background: #27ae60; color: white; }
        .btn-primary:hover { background: #229954; transform: translateY(-2px); }
        .btn-secondary { background: #3498db; color: white; }
        .btn-secondary:hover { background: #2980b9; transform: translateY(-2px); }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-danger:hover { background: #c0392b; transform: translateY(-2px); }
        .status {
            margin-top: 30px; padding: 20px; background: #f8f9fa;
            border-radius: 8px; border-right: 4px solid #3498db;
        }
        .status h3 { color: #2c3e50; margin-bottom: 15px; }
        .status-item {
            padding: 10px; margin: 5px 0; background: white;
            border-radius: 5px; border-right: 3px solid #27ae60;
        }
        .error { border-right-color: #e74c3c !important; background: #fdf2f2 !important; }
        .warning { border-right-color: #f39c12 !important; background: #fef9e7 !important; }
        .loading { display: none; text-align: center; padding: 20px; }
        .spinner {
            border: 4px solid #f3f3f3; border-top: 4px solid #3498db;
            border-radius: 50%; width: 40px; height: 40px;
            animation: spin 1s linear infinite; margin: 0 auto 10px;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .footer { background: #ecf0f1; padding: 20px; text-align: center; color: #7f8c8d; }
        @media (max-width: 600px) {
            .buttons { flex-direction: column; }
            .btn { width: 100%; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 No Music Desktop</h1>
            <p>إزالة الموسيقى من فيديوهات YouTube</p>
        </div>
        <div class="content">
            <div class="form-group">
                <label for="videoUrl">رابط فيديو YouTube:</label>
                <input type="url" id="videoUrl" placeholder="https://youtube.com/watch?v=..." />
            </div>
            <div class="buttons">
                <button class="btn btn-primary" onclick="processVideo()">🚀 معالجة الفيديو</button>
                <button class="btn btn-secondary" onclick="checkStatus()">🔄 تحديث الحالة</button>
                <button class="btn btn-danger" onclick="clearUrl()">🗑️ مسح</button>
                <a href="http://127.0.0.1:3333" target="_blank" class="btn btn-secondary">🔗 فتح API</a>
            </div>
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>جاري المعالجة...</p>
            </div>
            <div class="status">
                <h3>📊 حالة النظام</h3>
                <div id="statusContainer">
                    <div class="status-item">🔄 جاري التحقق من حالة الخادم...</div>
                </div>
            </div>
        </div>
        <div class="footer">
            <p>No Music Desktop | استخدم الخادم على المنفذ 3333</p>
        </div>
    </div>
    <script>
        const API_BASE = 'http://127.0.0.1:3333';
        function addStatus(message, type = 'info') {
            const container = document.getElementById('statusContainer');
            const item = document.createElement('div');
            item.className = 'status-item';
            if (type === 'error') item.classList.add('error');
            if (type === 'warning') item.classList.add('warning');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            item.innerHTML = `[${timestamp}] ${message}`;
            container.appendChild(item);
            container.scrollTop = container.scrollHeight;
            while (container.children.length > 10) {
                container.removeChild(container.firstChild);
            }
        }
        function clearUrl() {
            document.getElementById('videoUrl').value = '';
            addStatus('🗑️ تم مسح الرابط');
        }
        async function checkServerStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    addStatus('✅ الخادم يعمل بشكل طبيعي');
                    return true;
                } else {
                    addStatus('⚠️ الخادم يعمل لكن هناك مشكلة', 'warning');
                    return false;
                }
            } catch (error) {
                addStatus('❌ الخادم غير متصل', 'error');
                return false;
            }
        }
        async function checkStatus() {
            addStatus('🔄 تحديث الحالة...');
            if (!await checkServerStatus()) return;
            try {
                const response = await fetch(`${API_BASE}/tasks`);
                if (response.ok) {
                    const tasks = await response.json();
                    addStatus(`📋 إجمالي المهام: ${tasks.length}`);
                    if (tasks.length > 0) {
                        addStatus('📝 آخر 3 مهام:');
                        tasks.slice(-3).forEach(task => {
                            const taskId = task.id ? task.id.substring(0, 8) : 'غير محدد';
                            const title = task.title ? task.title.substring(0, 40) : 'غير محدد';
                            const status = task.status || 'غير محدد';
                            addStatus(`   🔹 [${taskId}] ${title}: ${status}`);
                        });
                    } else {
                        addStatus('📝 لا توجد مهام حالياً');
                    }
                } else {
                    addStatus('⚠️ لا يمكن الحصول على قائمة المهام', 'warning');
                }
            } catch (error) {
                addStatus(`❌ خطأ في الحصول على المهام: ${error.message}`, 'error');
            }
        }
        async function processVideo() {
            const url = document.getElementById('videoUrl').value.trim();
            if (!url) { alert('يرجى إدخال رابط YouTube'); return; }
            if (!url.startsWith('http')) { alert('يرجى إدخال رابط صحيح'); return; }
            addStatus(`🚀 بدء معالجة: ${url}`);
            const loading = document.getElementById('loading');
            loading.style.display = 'block';
            try {
                const response = await fetch(`${API_BASE}/process`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ url: url })
                });
                if (response.ok) {
                    const result = await response.json();
                    addStatus('✅ تم إنشاء المهمة بنجاح!');
                    addStatus(`🆔 معرف المهمة: ${result.task_id || 'غير محدد'}`);
                    addStatus(`📁 مجلد الحفظ: ${result.output_dir || 'غير محدد'}`);
                } else {
                    const errorText = await response.text();
                    addStatus(`❌ خطأ في المعالجة: ${errorText}`, 'error');
                }
            } catch (error) {
                addStatus(`❌ خطأ في الاتصال: ${error.message}`, 'error');
            } finally {
                loading.style.display = 'none';
            }
        }
        window.addEventListener('load', () => {
            addStatus('🎉 مرحباً بك في No Music Desktop!');
            checkStatus();
        });
        setInterval(checkStatus, 30000);
    </script>
</body>
</html>"""
    
    html_file = project_root / "index.html"
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return str(html_file)

def start_server():
    """Start the API server"""
    print("🚀 بدء تشغيل الخادم...")
    
    try:
        # Check if server is already running
        import requests
        try:
            response = requests.get("http://127.0.0.1:3333/health", timeout=2)
            if response.status_code == 200:
                print("✅ الخادم يعمل بالفعل!")
                return True
        except:
            pass
        
        # Start server
        server_process = subprocess.Popen([
            sys.executable, "run_server_only.py"
        ], cwd=str(project_root))
        
        # Wait for server to start
        for i in range(15):
            time.sleep(1)
            try:
                response = requests.get("http://127.0.0.1:3333/health", timeout=1)
                if response.status_code == 200:
                    print("✅ تم تشغيل الخادم بنجاح!")
                    return True
            except:
                continue
        
        print("⚠️ فشل في تشغيل الخادم تلقائياً")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        return False

def main():
    """Main startup function"""
    print("=" * 60)
    print("🎵 No Music Desktop - Startup")
    print("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    # Create web interface
    print("🌐 إنشاء واجهة الويب...")
    html_file = create_web_interface()
    print(f"✅ تم إنشاء الواجهة: {html_file}")
    
    # Start server
    server_started = start_server()
    
    # Open web interface
    try:
        webbrowser.open(f"file://{html_file}")
        print("🌐 تم فتح الواجهة في المتصفح")
    except Exception as e:
        print(f"⚠️ لم يتم فتح المتصفح تلقائياً: {e}")
        print(f"🌐 يمكنك فتح الملف يدوياً: {html_file}")
    
    print("🔗 رابط الخادم: http://127.0.0.1:3333")
    
    if server_started:
        print("🎯 النظام جاهز للاستخدام!")
    else:
        print("⚠️ يرجى تشغيل الخادم يدوياً:")
        print("   python run_server_only.py")
    
    print("💡 لإيقاف النظام اضغط Ctrl+C")
    return 0

if __name__ == "__main__":
    sys.exit(main())
