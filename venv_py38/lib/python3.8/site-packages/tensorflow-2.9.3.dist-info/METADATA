Metadata-Version: 2.1
Name: tensorflow
Version: 2.9.3
Summary: TensorFlow is an open source machine learning framework for everyone.
Home-page: https://www.tensorflow.org/
Author: Google Inc.
Author-email: <EMAIL>
License: Apache 2.0
Download-URL: https://github.com/tensorflow/tensorflow/tags
Keywords: tensorflow tensor machine learning
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: GPU :: NVIDIA CUDA :: 11.2
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Topic :: Scientific/Engineering :: Mathematics
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.7
Description-Content-Type: text/markdown
Requires-Dist: absl-py (>=1.0.0)
Requires-Dist: astunparse (>=1.6.0)
Requires-Dist: flatbuffers (<2,>=1.12)
Requires-Dist: gast (<=0.4.0,>=0.2.1)
Requires-Dist: google-pasta (>=0.1.1)
Requires-Dist: grpcio (<2.0,>=1.24.3)
Requires-Dist: h5py (>=2.9.0)
Requires-Dist: keras (<2.10.0,>=2.9.0rc0)
Requires-Dist: keras-preprocessing (>=1.1.1)
Requires-Dist: libclang (>=13.0.0)
Requires-Dist: numpy (>=1.20)
Requires-Dist: opt-einsum (>=2.3.2)
Requires-Dist: packaging
Requires-Dist: protobuf (<3.20,>=3.9.2)
Requires-Dist: setuptools
Requires-Dist: six (>=1.12.0)
Requires-Dist: tensorboard (<2.10,>=2.9)
Requires-Dist: tensorflow-io-gcs-filesystem (>=0.23.1)
Requires-Dist: tensorflow-estimator (<2.10.0,>=2.9.0rc0)
Requires-Dist: termcolor (>=1.1.0)
Requires-Dist: typing-extensions (>=3.6.6)
Requires-Dist: wrapt (>=1.11.0)

[![Python](https://img.shields.io/pypi/pyversions/tensorflow.svg?style=plastic)](https://badge.fury.io/py/tensorflow)
[![PyPI](https://badge.fury.io/py/tensorflow.svg)](https://badge.fury.io/py/tensorflow)

TensorFlow is an open source software library for high performance numerical
computation. Its flexible architecture allows easy deployment of computation
across a variety of platforms (CPUs, GPUs, TPUs), and from desktops to clusters
of servers to mobile and edge devices.

Originally developed by researchers and engineers from the Google Brain team
within Google's AI organization, it comes with strong support for machine
learning and deep learning and the flexible numerical computation core is used
across many other scientific domains.


