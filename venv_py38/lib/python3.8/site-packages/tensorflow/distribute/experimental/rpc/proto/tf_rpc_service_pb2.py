# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/distribute/experimental/rpc/proto/tf_rpc_service.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorflow.core.framework import tensor_pb2 as tensorflow_dot_core_dot_framework_dot_tensor__pb2
from tensorflow.core.protobuf import struct_pb2 as tensorflow_dot_core_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorflow/distribute/experimental/rpc/proto/tf_rpc_service.proto',
  package='tensorflow.rpc',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\nAtensorflow/distribute/experimental/rpc/proto/tf_rpc_service.proto\x12\x0etensorflow.rpc\x1a&tensorflow/core/framework/tensor.proto\x1a%tensorflow/core/protobuf/struct.proto\"M\n\x0b\x43\x61llRequest\x12\x0e\n\x06method\x18\x01 \x01(\t\x12.\n\rinput_tensors\x18\x02 \x03(\x0b\x32\x17.tensorflow.TensorProto\"?\n\x0c\x43\x61llResponse\x12/\n\x0eoutput_tensors\x18\x01 \x03(\x0b\x32\x17.tensorflow.TensorProto\"\r\n\x0bListRequest\"\x87\x01\n\x10RegisteredMethod\x12\x0e\n\x06method\x18\x01 \x01(\t\x12\x30\n\x0binput_specs\x18\x02 \x01(\x0b\x32\x1b.tensorflow.StructuredValue\x12\x31\n\x0coutput_specs\x18\x03 \x01(\x0b\x32\x1b.tensorflow.StructuredValue\"L\n\x0cListResponse\x12<\n\x12registered_methods\x18\x01 \x03(\x0b\x32 .tensorflow.rpc.RegisteredMethod2\x96\x01\n\nRpcService\x12\x43\n\x04\x43\x61ll\x12\x1b.tensorflow.rpc.CallRequest\x1a\x1c.tensorflow.rpc.CallResponse\"\x00\x12\x43\n\x04List\x12\x1b.tensorflow.rpc.ListRequest\x1a\x1c.tensorflow.rpc.ListResponse\"\x00\x62\x06proto3')
  ,
  dependencies=[tensorflow_dot_core_dot_framework_dot_tensor__pb2.DESCRIPTOR,tensorflow_dot_core_dot_protobuf_dot_struct__pb2.DESCRIPTOR,])




_CALLREQUEST = _descriptor.Descriptor(
  name='CallRequest',
  full_name='tensorflow.rpc.CallRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='method', full_name='tensorflow.rpc.CallRequest.method', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='input_tensors', full_name='tensorflow.rpc.CallRequest.input_tensors', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=164,
  serialized_end=241,
)


_CALLRESPONSE = _descriptor.Descriptor(
  name='CallResponse',
  full_name='tensorflow.rpc.CallResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='output_tensors', full_name='tensorflow.rpc.CallResponse.output_tensors', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=243,
  serialized_end=306,
)


_LISTREQUEST = _descriptor.Descriptor(
  name='ListRequest',
  full_name='tensorflow.rpc.ListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=308,
  serialized_end=321,
)


_REGISTEREDMETHOD = _descriptor.Descriptor(
  name='RegisteredMethod',
  full_name='tensorflow.rpc.RegisteredMethod',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='method', full_name='tensorflow.rpc.RegisteredMethod.method', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='input_specs', full_name='tensorflow.rpc.RegisteredMethod.input_specs', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='output_specs', full_name='tensorflow.rpc.RegisteredMethod.output_specs', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=324,
  serialized_end=459,
)


_LISTRESPONSE = _descriptor.Descriptor(
  name='ListResponse',
  full_name='tensorflow.rpc.ListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='registered_methods', full_name='tensorflow.rpc.ListResponse.registered_methods', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=461,
  serialized_end=537,
)

_CALLREQUEST.fields_by_name['input_tensors'].message_type = tensorflow_dot_core_dot_framework_dot_tensor__pb2._TENSORPROTO
_CALLRESPONSE.fields_by_name['output_tensors'].message_type = tensorflow_dot_core_dot_framework_dot_tensor__pb2._TENSORPROTO
_REGISTEREDMETHOD.fields_by_name['input_specs'].message_type = tensorflow_dot_core_dot_protobuf_dot_struct__pb2._STRUCTUREDVALUE
_REGISTEREDMETHOD.fields_by_name['output_specs'].message_type = tensorflow_dot_core_dot_protobuf_dot_struct__pb2._STRUCTUREDVALUE
_LISTRESPONSE.fields_by_name['registered_methods'].message_type = _REGISTEREDMETHOD
DESCRIPTOR.message_types_by_name['CallRequest'] = _CALLREQUEST
DESCRIPTOR.message_types_by_name['CallResponse'] = _CALLRESPONSE
DESCRIPTOR.message_types_by_name['ListRequest'] = _LISTREQUEST
DESCRIPTOR.message_types_by_name['RegisteredMethod'] = _REGISTEREDMETHOD
DESCRIPTOR.message_types_by_name['ListResponse'] = _LISTRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CallRequest = _reflection.GeneratedProtocolMessageType('CallRequest', (_message.Message,), {
  'DESCRIPTOR' : _CALLREQUEST,
  '__module__' : 'tensorflow.distribute.experimental.rpc.proto.tf_rpc_service_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.rpc.CallRequest)
  })
_sym_db.RegisterMessage(CallRequest)

CallResponse = _reflection.GeneratedProtocolMessageType('CallResponse', (_message.Message,), {
  'DESCRIPTOR' : _CALLRESPONSE,
  '__module__' : 'tensorflow.distribute.experimental.rpc.proto.tf_rpc_service_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.rpc.CallResponse)
  })
_sym_db.RegisterMessage(CallResponse)

ListRequest = _reflection.GeneratedProtocolMessageType('ListRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTREQUEST,
  '__module__' : 'tensorflow.distribute.experimental.rpc.proto.tf_rpc_service_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.rpc.ListRequest)
  })
_sym_db.RegisterMessage(ListRequest)

RegisteredMethod = _reflection.GeneratedProtocolMessageType('RegisteredMethod', (_message.Message,), {
  'DESCRIPTOR' : _REGISTEREDMETHOD,
  '__module__' : 'tensorflow.distribute.experimental.rpc.proto.tf_rpc_service_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.rpc.RegisteredMethod)
  })
_sym_db.RegisterMessage(RegisteredMethod)

ListResponse = _reflection.GeneratedProtocolMessageType('ListResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTRESPONSE,
  '__module__' : 'tensorflow.distribute.experimental.rpc.proto.tf_rpc_service_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.rpc.ListResponse)
  })
_sym_db.RegisterMessage(ListResponse)



_RPCSERVICE = _descriptor.ServiceDescriptor(
  name='RpcService',
  full_name='tensorflow.rpc.RpcService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=540,
  serialized_end=690,
  methods=[
  _descriptor.MethodDescriptor(
    name='Call',
    full_name='tensorflow.rpc.RpcService.Call',
    index=0,
    containing_service=None,
    input_type=_CALLREQUEST,
    output_type=_CALLRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='List',
    full_name='tensorflow.rpc.RpcService.List',
    index=1,
    containing_service=None,
    input_type=_LISTREQUEST,
    output_type=_LISTRESPONSE,
    serialized_options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_RPCSERVICE)

DESCRIPTOR.services_by_name['RpcService'] = _RPCSERVICE

# @@protoc_insertion_point(module_scope)
