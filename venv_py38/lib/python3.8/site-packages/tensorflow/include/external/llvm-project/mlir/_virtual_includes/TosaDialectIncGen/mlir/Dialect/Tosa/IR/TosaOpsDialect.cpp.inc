/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

DEFINE_EXPLICIT_TYPE_ID(mlir::tosa::TosaDialect)
namespace mlir {
namespace tosa {

TosaDialect::~TosaDialect() = default;

} // namespace tosa
} // namespace mlir
