/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

DEFINE_EXPLICIT_TYPE_ID(::mlir::omp::OpenMPDialect)
namespace mlir {
namespace omp {

OpenMPDialect::~OpenMPDialect() = default;

} // namespace omp
} // namespace mlir
