/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// ConvertAffineForToGPU
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertAffineForToGPUBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = ConvertAffineForToGPUBase;

  ConvertAffineForToGPUBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAffineForToGPUBase(const ConvertAffineForToGPUBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-affine-for-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-affine-for-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Convert top-level AffineFor Ops to GPU kernels"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAffineForToGPU");
  }
  ::llvm::StringRef getName() const override { return "ConvertAffineForToGPU"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<gpu::GPUDialect>();

  }

protected:
  ::mlir::Pass::Option<unsigned> numBlockDims{*this, "gpu-block-dims", ::llvm::cl::desc("Number of GPU block dimensions for mapping"), ::llvm::cl::init(1u)};
  ::mlir::Pass::Option<unsigned> numThreadDims{*this, "gpu-thread-dims", ::llvm::cl::desc("Number of GPU thread dimensions for mapping"), ::llvm::cl::init(1u)};
};

//===----------------------------------------------------------------------===//
// ConvertAffineToStandard
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertAffineToStandardBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertAffineToStandardBase;

  ConvertAffineToStandardBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAffineToStandardBase(const ConvertAffineToStandardBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-affine");
  }
  ::llvm::StringRef getArgument() const override { return "lower-affine"; }

  ::llvm::StringRef getDescription() const override { return "Lower Affine operations to a combination of Standard and SCF operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAffineToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertAffineToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<vector::VectorDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertArithmeticToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertArithmeticToLLVMBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertArithmeticToLLVMBase;

  ConvertArithmeticToLLVMBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArithmeticToLLVMBase(const ConvertArithmeticToLLVMBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arith-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arith-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arithmetic dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArithmeticToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertArithmeticToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
};

//===----------------------------------------------------------------------===//
// ConvertArithmeticToSPIRV
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertArithmeticToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertArithmeticToSPIRVBase;

  ConvertArithmeticToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArithmeticToSPIRVBase(const ConvertArithmeticToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-arith-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-arith-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arithmetic dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArithmeticToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertArithmeticToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> emulateNon32BitScalarTypes{*this, "emulate-non-32-bit-scalar-types", ::llvm::cl::desc("Emulate non-32-bit scalar types with 32-bit ones if missing native support"), ::llvm::cl::init(true)};
};

//===----------------------------------------------------------------------===//
// ConvertArmNeon2dToIntr
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertArmNeon2dToIntrBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertArmNeon2dToIntrBase;

  ConvertArmNeon2dToIntrBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertArmNeon2dToIntrBase(const ConvertArmNeon2dToIntrBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("arm-neon-2d-to-intr");
  }
  ::llvm::StringRef getArgument() const override { return "arm-neon-2d-to-intr"; }

  ::llvm::StringRef getDescription() const override { return "Convert Arm NEON structured ops to intrinsics"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertArmNeon2dToIntr");
  }
  ::llvm::StringRef getName() const override { return "ConvertArmNeon2dToIntr"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arm_neon::ArmNeonDialect>();

  registry.insert<vector::VectorDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertAsyncToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertAsyncToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertAsyncToLLVMBase;

  ConvertAsyncToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertAsyncToLLVMBase(const ConvertAsyncToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-async-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-async-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the operations from the async dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertAsyncToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertAsyncToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithmeticDialect>();

  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertBufferizationToMemRef
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertBufferizationToMemRefBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertBufferizationToMemRefBase;

  ConvertBufferizationToMemRefBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertBufferizationToMemRefBase(const ConvertBufferizationToMemRefBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-bufferization-to-memref");
  }
  ::llvm::StringRef getArgument() const override { return "convert-bufferization-to-memref"; }

  ::llvm::StringRef getDescription() const override { return "Convert operations from the Bufferization dialect to the MemRef dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertBufferizationToMemRef");
  }
  ::llvm::StringRef getName() const override { return "ConvertBufferizationToMemRef"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithmeticDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertComplexToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertComplexToLLVMBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertComplexToLLVMBase;

  ConvertComplexToLLVMBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToLLVMBase(const ConvertComplexToLLVMBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertComplexToStandard
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertComplexToStandardBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertComplexToStandardBase;

  ConvertComplexToStandardBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertComplexToStandardBase(const ConvertComplexToStandardBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-complex-to-standard");
  }
  ::llvm::StringRef getArgument() const override { return "convert-complex-to-standard"; }

  ::llvm::StringRef getDescription() const override { return "Convert Complex dialect to standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertComplexToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertComplexToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<math::MathDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertControlFlowToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertControlFlowToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertControlFlowToLLVMBase;

  ConvertControlFlowToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertControlFlowToLLVMBase(const ConvertControlFlowToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-cf-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-cf-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert ControlFlow operations to the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertControlFlowToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertControlFlowToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
};

//===----------------------------------------------------------------------===//
// ConvertControlFlowToSPIRV
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertControlFlowToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertControlFlowToSPIRVBase;

  ConvertControlFlowToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertControlFlowToSPIRVBase(const ConvertControlFlowToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-cf-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-cf-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert ControlFlow dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertControlFlowToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertControlFlowToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> emulateNon32BitScalarTypes{*this, "emulate-non-32-bit-scalar-types", ::llvm::cl::desc("Emulate non-32-bit scalar types with 32-bit ones if missing native support"), ::llvm::cl::init(true)};
};

//===----------------------------------------------------------------------===//
// ConvertFuncToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertFuncToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertFuncToLLVMBase;

  ConvertFuncToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertFuncToLLVMBase(const ConvertFuncToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-func-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-func-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert from the Func dialect to the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertFuncToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertFuncToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> useBarePtrCallConv{*this, "use-bare-ptr-memref-call-conv", ::llvm::cl::desc("Replace FuncOp's MemRef arguments with bare pointers to the MemRef element types"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> emitCWrappers{*this, "emit-c-wrappers", ::llvm::cl::desc("Emit wrappers for C-compatible pointer-to-struct memref descriptors"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<std::string> dataLayout{*this, "data-layout", ::llvm::cl::desc("String description (LLVM format) of the data layout that is expected on the produced module"), ::llvm::cl::init("")};
};

//===----------------------------------------------------------------------===//
// ConvertFuncToSPIRV
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertFuncToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertFuncToSPIRVBase;

  ConvertFuncToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertFuncToSPIRVBase(const ConvertFuncToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-func-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-func-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Func dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertFuncToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertFuncToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> emulateNon32BitScalarTypes{*this, "emulate-non-32-bit-scalar-types", ::llvm::cl::desc("Emulate non-32-bit scalar types with 32-bit ones if missing native support"), ::llvm::cl::init(true)};
};

//===----------------------------------------------------------------------===//
// ConvertGPUToSPIRV
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertGPUToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertGPUToSPIRVBase;

  ConvertGPUToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGPUToSPIRVBase(const ConvertGPUToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert GPU dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGPUToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertGPUToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertGpuLaunchFuncToVulkanLaunchFunc
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertGpuLaunchFuncToVulkanLaunchFuncBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertGpuLaunchFuncToVulkanLaunchFuncBase;

  ConvertGpuLaunchFuncToVulkanLaunchFuncBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuLaunchFuncToVulkanLaunchFuncBase(const ConvertGpuLaunchFuncToVulkanLaunchFuncBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-launch-to-vulkan-launch");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-launch-to-vulkan-launch"; }

  ::llvm::StringRef getDescription() const override { return "Convert gpu.launch_func to vulkanLaunch external call"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuLaunchFuncToVulkanLaunchFunc");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuLaunchFuncToVulkanLaunchFunc"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToNVVMOps
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertGpuOpsToNVVMOpsBase : public ::mlir::OperationPass<gpu::GPUModuleOp> {
public:
  using Base = ConvertGpuOpsToNVVMOpsBase;

  ConvertGpuOpsToNVVMOpsBase() : ::mlir::OperationPass<gpu::GPUModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuOpsToNVVMOpsBase(const ConvertGpuOpsToNVVMOpsBase &other) : ::mlir::OperationPass<gpu::GPUModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-nvvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-nvvm"; }

  ::llvm::StringRef getDescription() const override { return "Generate NVVM operations for gpu operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuOpsToNVVMOps");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuOpsToNVVMOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<cf::ControlFlowDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<NVVM::NVVMDialect>();

  }

protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
};

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToROCDLOps
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertGpuOpsToROCDLOpsBase : public ::mlir::OperationPass<gpu::GPUModuleOp> {
public:
  using Base = ConvertGpuOpsToROCDLOpsBase;

  ConvertGpuOpsToROCDLOpsBase() : ::mlir::OperationPass<gpu::GPUModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertGpuOpsToROCDLOpsBase(const ConvertGpuOpsToROCDLOpsBase &other) : ::mlir::OperationPass<gpu::GPUModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-gpu-to-rocdl");
  }
  ::llvm::StringRef getArgument() const override { return "convert-gpu-to-rocdl"; }

  ::llvm::StringRef getDescription() const override { return "Generate ROCDL operations for gpu operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertGpuOpsToROCDLOps");
  }
  ::llvm::StringRef getName() const override { return "ConvertGpuOpsToROCDLOps"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<ROCDL::ROCDLDialect>();

  }

protected:
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<::mlir::gpu::amd::Runtime> runtime{*this, "runtime", ::llvm::cl::desc("Runtime code will be run on (default is Unknown, can also use HIP or OpenCl)"), ::llvm::cl::init(::mlir::gpu::amd::Runtime::Unknown), ::llvm::cl::values(
            clEnumValN(::mlir::gpu::amd::Runtime::Unknown, "unknown", "Unknown (default)"),
            clEnumValN(::mlir::gpu::amd::Runtime::HIP, "HIP", "HIP"),
            clEnumValN(::mlir::gpu::amd::Runtime::OpenCL, "OpenCL", "OpenCL")
          )};
};

//===----------------------------------------------------------------------===//
// ConvertLinalgToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertLinalgToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertLinalgToLLVMBase;

  ConvertLinalgToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToLLVMBase(const ConvertLinalgToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the operations from the linalg dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<scf::SCFDialect>();

  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertLinalgToSPIRV
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertLinalgToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertLinalgToSPIRVBase;

  ConvertLinalgToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToSPIRVBase(const ConvertLinalgToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Linalg dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertLinalgToStandard
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertLinalgToStandardBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertLinalgToStandardBase;

  ConvertLinalgToStandardBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToStandardBase(const ConvertLinalgToStandardBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-std");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-std"; }

  ::llvm::StringRef getDescription() const override { return "Convert the operations from the linalg dialect into the Standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<func::FuncDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertMathToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertMathToLLVMBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertMathToLLVMBase;

  ConvertMathToLLVMBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToLLVMBase(const ConvertMathToLLVMBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertMathToLibm
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertMathToLibmBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertMathToLibmBase;

  ConvertMathToLibmBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToLibmBase(const ConvertMathToLibmBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-libm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-libm"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to libm calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToLibm");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToLibm"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithmeticDialect>();

  registry.insert<func::FuncDialect>();

  registry.insert<vector::VectorDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertMathToSPIRV
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertMathToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertMathToSPIRVBase;

  ConvertMathToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMathToSPIRVBase(const ConvertMathToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-math-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-math-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Math dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMathToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertMathToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertMemRefToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertMemRefToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertMemRefToLLVMBase;

  ConvertMemRefToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMemRefToLLVMBase(const ConvertMemRefToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-memref-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-memref-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert operations from the MemRef dialect to the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMemRefToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertMemRefToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> useAlignedAlloc{*this, "use-aligned-alloc", ::llvm::cl::desc("Use aligned_alloc in place of malloc for heap allocations"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> indexBitwidth{*this, "index-bitwidth", ::llvm::cl::desc("Bitwidth of the index type, 0 to use size of machine word"), ::llvm::cl::init(0)};
};

//===----------------------------------------------------------------------===//
// ConvertMemRefToSPIRV
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertMemRefToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertMemRefToSPIRVBase;

  ConvertMemRefToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertMemRefToSPIRVBase(const ConvertMemRefToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-memref-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-memref-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert MemRef dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertMemRefToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertMemRefToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

protected:
  ::mlir::Pass::Option<int> boolNumBits{*this, "bool-num-bits", ::llvm::cl::desc("The number of bits to store a boolean value"), ::llvm::cl::init(8)};
};

//===----------------------------------------------------------------------===//
// ConvertOpenACCToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertOpenACCToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertOpenACCToLLVMBase;

  ConvertOpenACCToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertOpenACCToLLVMBase(const ConvertOpenACCToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-openacc-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-openacc-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the OpenACC ops to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertOpenACCToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertOpenACCToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertOpenACCToSCF
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertOpenACCToSCFBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertOpenACCToSCFBase;

  ConvertOpenACCToSCFBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertOpenACCToSCFBase(const ConvertOpenACCToSCFBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-openacc-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-openacc-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Convert the OpenACC ops to OpenACC with SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertOpenACCToSCF");
  }
  ::llvm::StringRef getName() const override { return "ConvertOpenACCToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<scf::SCFDialect>();

  registry.insert<acc::OpenACCDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertOpenMPToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertOpenMPToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertOpenMPToLLVMBase;

  ConvertOpenMPToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertOpenMPToLLVMBase(const ConvertOpenMPToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-openmp-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-openmp-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert the OpenMP ops to OpenMP ops with LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertOpenMPToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertOpenMPToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertPDLToPDLInterp
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertPDLToPDLInterpBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertPDLToPDLInterpBase;

  ConvertPDLToPDLInterpBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertPDLToPDLInterpBase(const ConvertPDLToPDLInterpBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-pdl-to-pdl-interp");
  }
  ::llvm::StringRef getArgument() const override { return "convert-pdl-to-pdl-interp"; }

  ::llvm::StringRef getDescription() const override { return "Convert PDL ops to PDL interpreter ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertPDLToPDLInterp");
  }
  ::llvm::StringRef getName() const override { return "ConvertPDLToPDLInterp"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<pdl_interp::PDLInterpDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertParallelLoopToGpu
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertParallelLoopToGpuBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertParallelLoopToGpuBase;

  ConvertParallelLoopToGpuBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertParallelLoopToGpuBase(const ConvertParallelLoopToGpuBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-parallel-loops-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-parallel-loops-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Convert mapped scf.parallel ops to gpu launch operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertParallelLoopToGpu");
  }
  ::llvm::StringRef getName() const override { return "ConvertParallelLoopToGpu"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<gpu::GPUDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertSCFToOpenMP
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertSCFToOpenMPBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertSCFToOpenMPBase;

  ConvertSCFToOpenMPBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertSCFToOpenMPBase(const ConvertSCFToOpenMPBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-openmp");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-openmp"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF parallel loop to OpenMP parallel + workshare constructs."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertSCFToOpenMP");
  }
  ::llvm::StringRef getName() const override { return "ConvertSCFToOpenMP"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<omp::OpenMPDialect>();

  registry.insert<LLVM::LLVMDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertSPIRVToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertSPIRVToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertSPIRVToLLVMBase;

  ConvertSPIRVToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertSPIRVToLLVMBase(const ConvertSPIRVToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-spirv-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-spirv-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert SPIR-V dialect to LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertSPIRVToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertSPIRVToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertShapeConstraints
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertShapeConstraintsBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertShapeConstraintsBase;

  ConvertShapeConstraintsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertShapeConstraintsBase(const ConvertShapeConstraintsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-shape-constraints");
  }
  ::llvm::StringRef getArgument() const override { return "convert-shape-constraints"; }

  ::llvm::StringRef getDescription() const override { return "Convert shape constraint operations to the standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertShapeConstraints");
  }
  ::llvm::StringRef getName() const override { return "ConvertShapeConstraints"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<cf::ControlFlowDialect>();

  registry.insert<scf::SCFDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertShapeToStandard
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertShapeToStandardBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertShapeToStandardBase;

  ConvertShapeToStandardBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertShapeToStandardBase(const ConvertShapeToStandardBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-shape-to-std");
  }
  ::llvm::StringRef getArgument() const override { return "convert-shape-to-std"; }

  ::llvm::StringRef getDescription() const override { return "Convert operations from the shape dialect into the standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertShapeToStandard");
  }
  ::llvm::StringRef getName() const override { return "ConvertShapeToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<scf::SCFDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertTensorToSPIRV
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertTensorToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertTensorToSPIRVBase;

  ConvertTensorToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertTensorToSPIRVBase(const ConvertTensorToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-tensor-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-tensor-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Tensor dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertTensorToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertTensorToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> emulateNon32BitScalarTypes{*this, "emulate-non-32-bit-scalar-types", ::llvm::cl::desc("Emulate non-32-bit scalar types with 32-bit ones if missing native support"), ::llvm::cl::init(true)};
};

//===----------------------------------------------------------------------===//
// ConvertVectorToGPU
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertVectorToGPUBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToGPUBase;

  ConvertVectorToGPUBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToGPUBase(const ConvertVectorToGPUBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-gpu");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-gpu"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the GPU dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToGPU");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToGPU"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  registry.insert<gpu::GPUDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertVectorToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertVectorToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertVectorToLLVMBase;

  ConvertVectorToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToLLVMBase(const ConvertVectorToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the LLVM dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToLLVM");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<bool> reassociateFPReductions{*this, "reassociate-fp-reductions", ::llvm::cl::desc("Allows llvm to reassociate floating-point reductions for speed"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> force32BitVectorIndices{*this, "force-32bit-vector-indices", ::llvm::cl::desc("Allows compiler to assume vector indices fit in 32-bit if that yields faster code"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> amx{*this, "enable-amx", ::llvm::cl::desc("Enables the use of AMX dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> armNeon{*this, "enable-arm-neon", ::llvm::cl::desc("Enables the use of ArmNeon dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> armSVE{*this, "enable-arm-sve", ::llvm::cl::desc("Enables the use of ArmSVE dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> x86Vector{*this, "enable-x86vector", ::llvm::cl::desc("Enables the use of X86Vector dialect while lowering the vector dialect."), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// ConvertVectorToROCDL
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertVectorToROCDLBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertVectorToROCDLBase;

  ConvertVectorToROCDLBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToROCDLBase(const ConvertVectorToROCDLBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-rocdl");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-rocdl"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the ROCDL dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToROCDL");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToROCDL"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<ROCDL::ROCDLDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertVectorToSCF
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertVectorToSCFBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertVectorToSCFBase;

  ConvertVectorToSCFBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToSCFBase(const ConvertVectorToSCFBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the vector dialect into the SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToSCF");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> fullUnroll{*this, "full-unroll", ::llvm::cl::desc("Perform full unrolling when converting vector transfers to SCF"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> targetRank{*this, "target-rank", ::llvm::cl::desc("Target vector rank to which transfer ops should be lowered"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<bool> lowerPermutationMaps{*this, "lower-permutation-maps", ::llvm::cl::desc("Replace permutation maps with vector transposes/broadcasts before lowering transfer ops"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> lowerTensors{*this, "lower-tensors", ::llvm::cl::desc("Lower transfer ops that operate on tensors"), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// ConvertVectorToSPIRV
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertVectorToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertVectorToSPIRVBase;

  ConvertVectorToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVectorToSPIRVBase(const ConvertVectorToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-vector-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-vector-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert Vector dialect to SPIR-V dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVectorToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "ConvertVectorToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ConvertVulkanLaunchFuncToVulkanCalls
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertVulkanLaunchFuncToVulkanCallsBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertVulkanLaunchFuncToVulkanCallsBase;

  ConvertVulkanLaunchFuncToVulkanCallsBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertVulkanLaunchFuncToVulkanCallsBase(const ConvertVulkanLaunchFuncToVulkanCallsBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("launch-func-to-vulkan");
  }
  ::llvm::StringRef getArgument() const override { return "launch-func-to-vulkan"; }

  ::llvm::StringRef getDescription() const override { return "Convert vulkanLaunch external call to Vulkan runtime external calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertVulkanLaunchFuncToVulkanCalls");
  }
  ::llvm::StringRef getName() const override { return "ConvertVulkanLaunchFuncToVulkanCalls"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// GpuToLLVMConversionPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class GpuToLLVMConversionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = GpuToLLVMConversionPassBase;

  GpuToLLVMConversionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  GpuToLLVMConversionPassBase(const GpuToLLVMConversionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("gpu-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "gpu-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Convert GPU dialect to LLVM dialect with GPU runtime calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GpuToLLVMConversionPass");
  }
  ::llvm::StringRef getName() const override { return "GpuToLLVMConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LowerHostCodeToLLVM
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LowerHostCodeToLLVMBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LowerHostCodeToLLVMBase;

  LowerHostCodeToLLVMBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerHostCodeToLLVMBase(const LowerHostCodeToLLVMBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-host-to-llvm");
  }
  ::llvm::StringRef getArgument() const override { return "lower-host-to-llvm"; }

  ::llvm::StringRef getDescription() const override { return "Lowers the host module code and `gpu.launch_func` to LLVM"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerHostCodeToLLVM");
  }
  ::llvm::StringRef getName() const override { return "LowerHostCodeToLLVM"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<LLVM::LLVMDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ReconcileUnrealizedCasts
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ReconcileUnrealizedCastsBase : public ::mlir::OperationPass<> {
public:
  using Base = ReconcileUnrealizedCastsBase;

  ReconcileUnrealizedCastsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ReconcileUnrealizedCastsBase(const ReconcileUnrealizedCastsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("reconcile-unrealized-casts");
  }
  ::llvm::StringRef getArgument() const override { return "reconcile-unrealized-casts"; }

  ::llvm::StringRef getDescription() const override { return "Simplify and eliminate unrealized conversion casts"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ReconcileUnrealizedCasts");
  }
  ::llvm::StringRef getName() const override { return "ReconcileUnrealizedCasts"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// SCFToControlFlow
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFToControlFlowBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFToControlFlowBase;

  SCFToControlFlowBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFToControlFlowBase(const SCFToControlFlowBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-cf");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-cf"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF dialect to ControlFlow dialect, replacing structured control flow with a CFG"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFToControlFlow");
  }
  ::llvm::StringRef getName() const override { return "SCFToControlFlow"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<cf::ControlFlowDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// SCFToSPIRV
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFToSPIRVBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SCFToSPIRVBase;

  SCFToSPIRVBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SCFToSPIRVBase(const SCFToSPIRVBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-scf-to-spirv");
  }
  ::llvm::StringRef getArgument() const override { return "convert-scf-to-spirv"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF dialect to SPIR-V dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFToSPIRV");
  }
  ::llvm::StringRef getName() const override { return "SCFToSPIRV"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<spirv::SPIRVDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// TosaToLinalg
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TosaToLinalgBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = TosaToLinalgBase;

  TosaToLinalgBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToLinalgBase(const TosaToLinalgBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to LinAlg on tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToLinalg");
  }
  ::llvm::StringRef getName() const override { return "TosaToLinalg"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TosaToLinalgNamed
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TosaToLinalgNamedBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = TosaToLinalgNamedBase;

  TosaToLinalgNamedBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToLinalgNamedBase(const TosaToLinalgNamedBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-linalg-named");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-linalg-named"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to LinAlg named operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToLinalgNamed");
  }
  ::llvm::StringRef getName() const override { return "TosaToLinalgNamed"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TosaToSCF
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TosaToSCFBase : public ::mlir::OperationPass<> {
public:
  using Base = TosaToSCFBase;

  TosaToSCFBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToSCFBase(const TosaToSCFBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-scf");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-scf"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the SCF dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToSCF");
  }
  ::llvm::StringRef getName() const override { return "TosaToSCF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<tensor::TensorDialect, scf::SCFDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// TosaToStandard
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TosaToStandardBase : public ::mlir::OperationPass<> {
public:
  using Base = TosaToStandardBase;

  TosaToStandardBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TosaToStandardBase(const TosaToStandardBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-to-standard");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-to-standard"; }

  ::llvm::StringRef getDescription() const override { return "Lower TOSA to the Standard dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaToStandard");
  }
  ::llvm::StringRef getName() const override { return "TosaToStandard"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithmeticDialect>();

  registry.insert<tensor::TensorDialect>();

  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// ConvertAffineForToGPU Registration
//===----------------------------------------------------------------------===//

inline void registerConvertAffineForToGPUPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAffineForToGPUPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertAffineToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerConvertAffineToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerAffinePass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertArithmeticToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertArithmeticToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::arith::createConvertArithmeticToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertArithmeticToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertArithmeticToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::arith::createConvertArithmeticToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertArmNeon2dToIntr Registration
//===----------------------------------------------------------------------===//

inline void registerConvertArmNeon2dToIntrPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertArmNeon2dToIntrPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertAsyncToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertAsyncToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertAsyncToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertBufferizationToMemRef Registration
//===----------------------------------------------------------------------===//

inline void registerConvertBufferizationToMemRefPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createBufferizationToMemRefPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertComplexToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertComplexToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertComplexToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertComplexToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerConvertComplexToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertComplexToStandardPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertControlFlowToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertControlFlowToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::cf::createConvertControlFlowToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertControlFlowToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertControlFlowToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertControlFlowToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertFuncToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertFuncToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertFuncToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertFuncToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertFuncToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertFuncToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertGPUToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertGPUToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertGPUToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertGpuLaunchFuncToVulkanLaunchFunc Registration
//===----------------------------------------------------------------------===//

inline void registerConvertGpuLaunchFuncToVulkanLaunchFuncPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertGpuLaunchFuncToVulkanLaunchFuncPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToNVVMOps Registration
//===----------------------------------------------------------------------===//

inline void registerConvertGpuOpsToNVVMOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerGpuOpsToNVVMOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertGpuOpsToROCDLOps Registration
//===----------------------------------------------------------------------===//

inline void registerConvertGpuOpsToROCDLOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerGpuOpsToROCDLOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertLinalgToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertLinalgToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertLinalgToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertLinalgToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertLinalgToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerConvertLinalgToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToStandardPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMathToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMathToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMathToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMathToLibm Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMathToLibmPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMathToLibmPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMathToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMathToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMathToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMemRefToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMemRefToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createMemRefToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertMemRefToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertMemRefToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertMemRefToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertOpenACCToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertOpenACCToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertOpenACCToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertOpenACCToSCF Registration
//===----------------------------------------------------------------------===//

inline void registerConvertOpenACCToSCFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertOpenACCToSCFPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertOpenMPToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertOpenMPToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertOpenMPToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertPDLToPDLInterp Registration
//===----------------------------------------------------------------------===//

inline void registerConvertPDLToPDLInterpPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPDLToPDLInterpPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertParallelLoopToGpu Registration
//===----------------------------------------------------------------------===//

inline void registerConvertParallelLoopToGpuPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopToGpuPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertSCFToOpenMP Registration
//===----------------------------------------------------------------------===//

inline void registerConvertSCFToOpenMPPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertSCFToOpenMPPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertSPIRVToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertSPIRVToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertSPIRVToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertShapeConstraints Registration
//===----------------------------------------------------------------------===//

inline void registerConvertShapeConstraintsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertShapeConstraintsPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertShapeToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerConvertShapeToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertShapeToStandardPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertTensorToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertTensorToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertTensorToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToGPU Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToGPUPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToGPUPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToROCDL Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToROCDLPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToROCDLPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToSCF Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToSCFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToSCFPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVectorToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVectorToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVectorToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertVulkanLaunchFuncToVulkanCalls Registration
//===----------------------------------------------------------------------===//

inline void registerConvertVulkanLaunchFuncToVulkanCallsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertVulkanLaunchFuncToVulkanCallsPass();
  });
}

//===----------------------------------------------------------------------===//
// GpuToLLVMConversionPass Registration
//===----------------------------------------------------------------------===//

inline void registerGpuToLLVMConversionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createGpuToLLVMConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// LowerHostCodeToLLVM Registration
//===----------------------------------------------------------------------===//

inline void registerLowerHostCodeToLLVMPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLowerHostCodeToLLVMPass();
  });
}

//===----------------------------------------------------------------------===//
// ReconcileUnrealizedCasts Registration
//===----------------------------------------------------------------------===//

inline void registerReconcileUnrealizedCastsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createReconcileUnrealizedCastsPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFToControlFlow Registration
//===----------------------------------------------------------------------===//

inline void registerSCFToControlFlowPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertSCFToCFPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFToSPIRV Registration
//===----------------------------------------------------------------------===//

inline void registerSCFToSPIRVPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertSCFToSPIRVPass();
  });
}

//===----------------------------------------------------------------------===//
// TosaToLinalg Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToLinalgPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToLinalg();
  });
}

//===----------------------------------------------------------------------===//
// TosaToLinalgNamed Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToLinalgNamedPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToLinalgNamed();
  });
}

//===----------------------------------------------------------------------===//
// TosaToSCF Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToSCFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToSCF();
  });
}

//===----------------------------------------------------------------------===//
// TosaToStandard Registration
//===----------------------------------------------------------------------===//

inline void registerTosaToStandardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaToStandard();
  });
}

//===----------------------------------------------------------------------===//
// Conversion Registration
//===----------------------------------------------------------------------===//

inline void registerConversionPasses() {
  registerConvertAffineForToGPUPass();
  registerConvertAffineToStandardPass();
  registerConvertArithmeticToLLVMPass();
  registerConvertArithmeticToSPIRVPass();
  registerConvertArmNeon2dToIntrPass();
  registerConvertAsyncToLLVMPass();
  registerConvertBufferizationToMemRefPass();
  registerConvertComplexToLLVMPass();
  registerConvertComplexToStandardPass();
  registerConvertControlFlowToLLVMPass();
  registerConvertControlFlowToSPIRVPass();
  registerConvertFuncToLLVMPass();
  registerConvertFuncToSPIRVPass();
  registerConvertGPUToSPIRVPass();
  registerConvertGpuLaunchFuncToVulkanLaunchFuncPass();
  registerConvertGpuOpsToNVVMOpsPass();
  registerConvertGpuOpsToROCDLOpsPass();
  registerConvertLinalgToLLVMPass();
  registerConvertLinalgToSPIRVPass();
  registerConvertLinalgToStandardPass();
  registerConvertMathToLLVMPass();
  registerConvertMathToLibmPass();
  registerConvertMathToSPIRVPass();
  registerConvertMemRefToLLVMPass();
  registerConvertMemRefToSPIRVPass();
  registerConvertOpenACCToLLVMPass();
  registerConvertOpenACCToSCFPass();
  registerConvertOpenMPToLLVMPass();
  registerConvertPDLToPDLInterpPass();
  registerConvertParallelLoopToGpuPass();
  registerConvertSCFToOpenMPPass();
  registerConvertSPIRVToLLVMPass();
  registerConvertShapeConstraintsPass();
  registerConvertShapeToStandardPass();
  registerConvertTensorToSPIRVPass();
  registerConvertVectorToGPUPass();
  registerConvertVectorToLLVMPass();
  registerConvertVectorToROCDLPass();
  registerConvertVectorToSCFPass();
  registerConvertVectorToSPIRVPass();
  registerConvertVulkanLaunchFuncToVulkanCallsPass();
  registerGpuToLLVMConversionPassPass();
  registerLowerHostCodeToLLVMPass();
  registerReconcileUnrealizedCastsPass();
  registerSCFToControlFlowPass();
  registerSCFToSPIRVPass();
  registerTosaToLinalgPass();
  registerTosaToLinalgNamedPass();
  registerTosaToSCFPass();
  registerTosaToStandardPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
