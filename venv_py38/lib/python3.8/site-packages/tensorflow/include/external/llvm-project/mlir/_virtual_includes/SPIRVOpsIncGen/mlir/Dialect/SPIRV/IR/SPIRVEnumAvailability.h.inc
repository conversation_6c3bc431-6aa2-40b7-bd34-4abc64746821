/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* SPIR-V Enum Availability Declarations                                      *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

namespace mlir {
namespace spirv {
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(AddressingModel value);
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(AddressingModel value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(BuiltIn value);
llvm::Optional<::mlir::spirv::Version> getMinVersion(BuiltIn value);
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(BuiltIn value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(Capability value);
llvm::Optional<::mlir::spirv::Version> getMinVersion(Capability value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(Decoration value);
llvm::Optional<::mlir::spirv::Version> getMaxVersion(Decoration value);
llvm::Optional<::mlir::spirv::Version> getMinVersion(Decoration value);
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(Decoration value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(Dim value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(ExecutionMode value);
llvm::Optional<::mlir::spirv::Version> getMinVersion(ExecutionMode value);
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(ExecutionMode value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(ExecutionModel value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(FunctionControl value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(GroupOperation value);
llvm::Optional<::mlir::spirv::Version> getMinVersion(GroupOperation value);
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(GroupOperation value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(ImageFormat value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(ImageOperands value);
llvm::Optional<::mlir::spirv::Version> getMinVersion(ImageOperands value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(LinkageType value);
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(LinkageType value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
llvm::Optional<::mlir::spirv::Version> getMinVersion(LoopControl value);
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(LoopControl value);
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(LoopControl value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
llvm::Optional<::mlir::spirv::Version> getMinVersion(MemoryAccess value);
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(MemoryAccess value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(MemoryModel value);
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(MemoryModel value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(MemorySemantics value);
llvm::Optional<::mlir::spirv::Version> getMinVersion(MemorySemantics value);
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(MemorySemantics value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
llvm::Optional<::mlir::spirv::Version> getMinVersion(Scope value);
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(Scope value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Capability>> getCapabilities(StorageClass value);
llvm::Optional<::llvm::ArrayRef<::mlir::spirv::Extension>> getExtensions(StorageClass value);
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
namespace mlir {
namespace spirv {
} // namespace spirv
} // namespace mlir
