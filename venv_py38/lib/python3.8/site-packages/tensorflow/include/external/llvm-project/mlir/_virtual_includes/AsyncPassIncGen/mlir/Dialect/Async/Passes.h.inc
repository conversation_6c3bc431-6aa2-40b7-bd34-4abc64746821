/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// AsyncParallelFor
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AsyncParallelForBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = AsyncParallelForBase;

  AsyncParallelForBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncParallelForBase(const AsyncParallelForBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-parallel-for");
  }
  ::llvm::StringRef getArgument() const override { return "async-parallel-for"; }

  ::llvm::StringRef getDescription() const override { return "Convert scf.parallel operations to multiple async compute ops executed concurrently for non-overlapping iteration ranges"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncParallelFor");
  }
  ::llvm::StringRef getName() const override { return "AsyncParallelFor"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithmeticDialect>();

  registry.insert<async::AsyncDialect>();

  registry.insert<scf::SCFDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> asyncDispatch{*this, "async-dispatch", ::llvm::cl::desc("Dispatch async compute tasks using recursive work splitting. If `false` async compute tasks will be launched using simple for loop in the caller thread."), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<int32_t> numWorkerThreads{*this, "num-workers", ::llvm::cl::desc("The number of available workers to execute async operations. If `-1` the value will be retrieved from the runtime."), ::llvm::cl::init(8)};
  ::mlir::Pass::Option<int32_t> minTaskSize{*this, "min-task-size", ::llvm::cl::desc("The minimum task size for sharding parallel operation."), ::llvm::cl::init(1000)};
};

//===----------------------------------------------------------------------===//
// AsyncRuntimePolicyBasedRefCounting
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AsyncRuntimePolicyBasedRefCountingBase : public ::mlir::OperationPass<> {
public:
  using Base = AsyncRuntimePolicyBasedRefCountingBase;

  AsyncRuntimePolicyBasedRefCountingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncRuntimePolicyBasedRefCountingBase(const AsyncRuntimePolicyBasedRefCountingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-runtime-policy-based-ref-counting");
  }
  ::llvm::StringRef getArgument() const override { return "async-runtime-policy-based-ref-counting"; }

  ::llvm::StringRef getDescription() const override { return "Policy based reference counting for Async runtime operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncRuntimePolicyBasedRefCounting");
  }
  ::llvm::StringRef getName() const override { return "AsyncRuntimePolicyBasedRefCounting"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// AsyncRuntimeRefCounting
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AsyncRuntimeRefCountingBase : public ::mlir::OperationPass<> {
public:
  using Base = AsyncRuntimeRefCountingBase;

  AsyncRuntimeRefCountingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncRuntimeRefCountingBase(const AsyncRuntimeRefCountingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-runtime-ref-counting");
  }
  ::llvm::StringRef getArgument() const override { return "async-runtime-ref-counting"; }

  ::llvm::StringRef getDescription() const override { return "Automatic reference counting for Async runtime operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncRuntimeRefCounting");
  }
  ::llvm::StringRef getName() const override { return "AsyncRuntimeRefCounting"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// AsyncRuntimeRefCountingOpt
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AsyncRuntimeRefCountingOptBase : public ::mlir::OperationPass<> {
public:
  using Base = AsyncRuntimeRefCountingOptBase;

  AsyncRuntimeRefCountingOptBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncRuntimeRefCountingOptBase(const AsyncRuntimeRefCountingOptBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-runtime-ref-counting-opt");
  }
  ::llvm::StringRef getArgument() const override { return "async-runtime-ref-counting-opt"; }

  ::llvm::StringRef getDescription() const override { return "Optimize automatic reference counting operations for theAsync runtime by removing redundant operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncRuntimeRefCountingOpt");
  }
  ::llvm::StringRef getName() const override { return "AsyncRuntimeRefCountingOpt"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// AsyncToAsyncRuntime
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AsyncToAsyncRuntimeBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = AsyncToAsyncRuntimeBase;

  AsyncToAsyncRuntimeBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  AsyncToAsyncRuntimeBase(const AsyncToAsyncRuntimeBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("async-to-async-runtime");
  }
  ::llvm::StringRef getArgument() const override { return "async-to-async-runtime"; }

  ::llvm::StringRef getDescription() const override { return "Lower high level async operations (e.g. async.execute) to theexplicit async.runtime and async.coro operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AsyncToAsyncRuntime");
  }
  ::llvm::StringRef getName() const override { return "AsyncToAsyncRuntime"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<async::AsyncDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> eliminateBlockingAwaitOps{*this, "eliminate-blocking-await-ops", ::llvm::cl::desc("Rewrite functions with blocking async.runtime.await as coroutines with async.runtime.await_and_resume."), ::llvm::cl::init(false)};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// AsyncParallelFor Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncParallelForPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncParallelForPass();
  });
}

//===----------------------------------------------------------------------===//
// AsyncRuntimePolicyBasedRefCounting Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncRuntimePolicyBasedRefCountingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncRuntimePolicyBasedRefCountingPass();
  });
}

//===----------------------------------------------------------------------===//
// AsyncRuntimeRefCounting Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncRuntimeRefCountingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncRuntimeRefCountingPass();
  });
}

//===----------------------------------------------------------------------===//
// AsyncRuntimeRefCountingOpt Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncRuntimeRefCountingOptPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncRuntimeRefCountingOptPass();
  });
}

//===----------------------------------------------------------------------===//
// AsyncToAsyncRuntime Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncToAsyncRuntimePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAsyncToAsyncRuntimePass();
  });
}

//===----------------------------------------------------------------------===//
// Async Registration
//===----------------------------------------------------------------------===//

inline void registerAsyncPasses() {
  registerAsyncParallelForPass();
  registerAsyncRuntimePolicyBasedRefCountingPass();
  registerAsyncRuntimeRefCountingPass();
  registerAsyncRuntimeRefCountingOptPass();
  registerAsyncToAsyncRuntimePass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
