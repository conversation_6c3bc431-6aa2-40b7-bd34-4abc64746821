/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// SCFBufferize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFBufferizeBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = SCFBufferizeBase;

  SCFBufferizeBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  SCFBufferizeBase(const SCFBufferizeBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "scf-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize the scf dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFBufferize");
  }
  ::llvm::StringRef getName() const override { return "SCFBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// SCFForLoopCanonicalization
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFForLoopCanonicalizationBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = SCFForLoopCanonicalizationBase;

  SCFForLoopCanonicalizationBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopCanonicalizationBase(const SCFForLoopCanonicalizationBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-canonicalization");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-canonicalization"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalize operations within scf.for loop bodies"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopCanonicalization");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopCanonicalization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<tensor::TensorDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// SCFForLoopPeeling
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFForLoopPeelingBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = SCFForLoopPeelingBase;

  SCFForLoopPeelingBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopPeelingBase(const SCFForLoopPeelingBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-peeling");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-peeling"; }

  ::llvm::StringRef getDescription() const override { return "Peel `for` loops at their upper bounds."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopPeeling");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopPeeling"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> skipPartial{*this, "skip-partial", ::llvm::cl::desc("Do not peel loops inside of the last, partial iteration of another already peeled loop."), ::llvm::cl::init(true)};
};

//===----------------------------------------------------------------------===//
// SCFForLoopRangeFolding
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFForLoopRangeFoldingBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopRangeFoldingBase;

  SCFForLoopRangeFoldingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopRangeFoldingBase(const SCFForLoopRangeFoldingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-range-folding");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-range-folding"; }

  ::llvm::StringRef getDescription() const override { return "Fold add/mul ops into loop range"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopRangeFolding");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopRangeFolding"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// SCFForLoopSpecialization
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFForLoopSpecializationBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = SCFForLoopSpecializationBase;

  SCFForLoopSpecializationBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopSpecializationBase(const SCFForLoopSpecializationBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-specialization");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-specialization"; }

  ::llvm::StringRef getDescription() const override { return "Specialize `for` loops for vectorization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopSpecialization");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopSpecialization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// SCFForToWhileLoop
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFForToWhileLoopBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = SCFForToWhileLoopBase;

  SCFForToWhileLoopBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForToWhileLoopBase(const SCFForToWhileLoopBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-to-while");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-to-while"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF for loops to SCF while loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForToWhileLoop");
  }
  ::llvm::StringRef getName() const override { return "SCFForToWhileLoop"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// SCFParallelLoopCollapsing
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFParallelLoopCollapsingBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFParallelLoopCollapsingBase;

  SCFParallelLoopCollapsingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopCollapsingBase(const SCFParallelLoopCollapsingBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-parallel-loop-collapsing");
  }
  ::llvm::StringRef getArgument() const override { return "scf-parallel-loop-collapsing"; }

  ::llvm::StringRef getDescription() const override { return "Collapse parallel loops to use less induction variables"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopCollapsing");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopCollapsing"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices0{*this, "collapsed-indices-0", ::llvm::cl::desc("Which loop indices to combine 0th loop index"), llvm::cl::MiscFlags::CommaSeparated};
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices1{*this, "collapsed-indices-1", ::llvm::cl::desc("Which loop indices to combine into the position 1 loop index"), llvm::cl::MiscFlags::CommaSeparated};
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices2{*this, "collapsed-indices-2", ::llvm::cl::desc("Which loop indices to combine into the position 2 loop index"), llvm::cl::MiscFlags::CommaSeparated};
};

//===----------------------------------------------------------------------===//
// SCFParallelLoopFusion
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFParallelLoopFusionBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFParallelLoopFusionBase;

  SCFParallelLoopFusionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopFusionBase(const SCFParallelLoopFusionBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-parallel-loop-fusion");
  }
  ::llvm::StringRef getArgument() const override { return "scf-parallel-loop-fusion"; }

  ::llvm::StringRef getDescription() const override { return "Fuse adjacent parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopFusion");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopFusion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// SCFParallelLoopSpecialization
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFParallelLoopSpecializationBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = SCFParallelLoopSpecializationBase;

  SCFParallelLoopSpecializationBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopSpecializationBase(const SCFParallelLoopSpecializationBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-parallel-loop-specialization");
  }
  ::llvm::StringRef getArgument() const override { return "scf-parallel-loop-specialization"; }

  ::llvm::StringRef getDescription() const override { return "Specialize parallel loops for vectorization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopSpecialization");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopSpecialization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// SCFParallelLoopTiling
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCFParallelLoopTilingBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = SCFParallelLoopTilingBase;

  SCFParallelLoopTilingBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopTilingBase(const SCFParallelLoopTilingBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-parallel-loop-tiling");
  }
  ::llvm::StringRef getArgument() const override { return "scf-parallel-loop-tiling"; }

  ::llvm::StringRef getDescription() const override { return "Tile parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopTiling");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopTiling"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  }

protected:
  ::mlir::Pass::ListOption<int64_t> tileSizes{*this, "parallel-loop-tile-sizes", ::llvm::cl::desc("Factors to tile parallel loops by"), llvm::cl::ZeroOrMore, llvm::cl::MiscFlags::CommaSeparated};
  ::mlir::Pass::Option<bool> noMinMaxBounds{*this, "no-min-max-bounds", ::llvm::cl::desc("Perform tiling with fixed upper bound with inbound check inside the internal loops"), ::llvm::cl::init(false)};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// SCFBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerSCFBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSCFBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForLoopCanonicalization Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForLoopCanonicalizationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSCFForLoopCanonicalizationPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForLoopPeeling Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForLoopPeelingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForLoopPeelingPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForLoopRangeFolding Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForLoopRangeFoldingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForLoopRangeFoldingPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForLoopSpecialization Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForLoopSpecializationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForLoopSpecializationPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForToWhileLoop Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForToWhileLoopPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForToWhileLoopPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFParallelLoopCollapsing Registration
//===----------------------------------------------------------------------===//

inline void registerSCFParallelLoopCollapsingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopCollapsingPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFParallelLoopFusion Registration
//===----------------------------------------------------------------------===//

inline void registerSCFParallelLoopFusionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopFusionPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFParallelLoopSpecialization Registration
//===----------------------------------------------------------------------===//

inline void registerSCFParallelLoopSpecializationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopSpecializationPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFParallelLoopTiling Registration
//===----------------------------------------------------------------------===//

inline void registerSCFParallelLoopTilingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopTilingPass();
  });
}

//===----------------------------------------------------------------------===//
// SCF Registration
//===----------------------------------------------------------------------===//

inline void registerSCFPasses() {
  registerSCFBufferizePass();
  registerSCFForLoopCanonicalizationPass();
  registerSCFForLoopPeelingPass();
  registerSCFForLoopRangeFoldingPass();
  registerSCFForLoopSpecializationPass();
  registerSCFForToWhileLoopPass();
  registerSCFParallelLoopCollapsingPass();
  registerSCFParallelLoopFusionPass();
  registerSCFParallelLoopSpecializationPass();
  registerSCFParallelLoopTilingPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
