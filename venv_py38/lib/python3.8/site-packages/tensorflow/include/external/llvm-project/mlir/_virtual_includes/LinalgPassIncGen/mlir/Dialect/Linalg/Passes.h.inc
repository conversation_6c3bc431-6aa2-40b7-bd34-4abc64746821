/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// ConvertElementwiseToLinalg
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ConvertElementwiseToLinalgBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertElementwiseToLinalgBase;

  ConvertElementwiseToLinalgBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertElementwiseToLinalgBase(const ConvertElementwiseToLinalgBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-elementwise-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "convert-elementwise-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Convert ElementwiseMappable ops to linalg"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertElementwiseToLinalg");
  }
  ::llvm::StringRef getName() const override { return "ConvertElementwiseToLinalg"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgBufferize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgBufferizeBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgBufferizeBase;

  LinalgBufferizeBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgBufferizeBase(const LinalgBufferizeBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize the linalg dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgBufferize");
  }
  ::llvm::StringRef getName() const override { return "LinalgBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgComprehensiveModuleBufferize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgComprehensiveModuleBufferizeBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LinalgComprehensiveModuleBufferizeBase;

  LinalgComprehensiveModuleBufferizeBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgComprehensiveModuleBufferizeBase(const LinalgComprehensiveModuleBufferizeBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-comprehensive-module-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-comprehensive-module-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize (tensor into memref) for a Module."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgComprehensiveModuleBufferize");
  }
  ::llvm::StringRef getName() const override { return "LinalgComprehensiveModuleBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<bool> testAnalysisOnly{*this, "test-analysis-only", ::llvm::cl::desc("Only runs inplaceability analysis (for testing purposes only)"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> printConflicts{*this, "print-conflicts", ::llvm::cl::desc("Annotates IR with RaW conflicts. Requires test-analysis-only."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> allowReturnAllocs{*this, "allow-return-allocs", ::llvm::cl::desc("Allows returning/yielding new allocations from a block."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> allowUnknownOps{*this, "allow-unknown-ops", ::llvm::cl::desc("Allows unknown (not bufferizable) ops in the input IR."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> alwaysAliasingWithDest{*this, "always-aliasing-with-dest", ::llvm::cl::desc("Tensor OpResult cannot bufferize inplace OpOperands other than out or dest OpOperands (if the op has a notion of such operands)"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> useAlloca{*this, "use-alloca", ::llvm::cl::desc("Use stack allocations for memrefs (for testing purposes only)"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> fullyDynamicLayoutMaps{*this, "fully-dynamic-layout-maps", ::llvm::cl::desc("Generate MemRef types with dynamic offset+strides by default."), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<unsigned> analysisFuzzerSeed{*this, "analysis-fuzzer-seed", ::llvm::cl::desc("Analyze ops in random order with a given seed (fuzzer)"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> initTensorElimination{*this, "init-tensor-elimination", ::llvm::cl::desc("(Experimental) Try to eliminate init_tensor operations that are anchored at an insert_slice op"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> createDeallocs{*this, "create-deallocs", ::llvm::cl::desc("Specify if buffers should be deallocated. For compatibility with core bufferization passes."), ::llvm::cl::init(true)};
};

//===----------------------------------------------------------------------===//
// LinalgDetensorize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgDetensorizeBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgDetensorizeBase;

  LinalgDetensorizeBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgDetensorizeBase(const LinalgDetensorizeBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-detensorize");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-detensorize"; }

  ::llvm::StringRef getDescription() const override { return "Detensorize linalg ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgDetensorize");
  }
  ::llvm::StringRef getName() const override { return "LinalgDetensorize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<bool> aggressiveMode{*this, "aggressive-mode", ::llvm::cl::desc("Detensorize all ops that qualify for detensoring along with branch operands and basic-block arguments."), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// LinalgElementwiseOpFusion
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgElementwiseOpFusionBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgElementwiseOpFusionBase;

  LinalgElementwiseOpFusionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgElementwiseOpFusionBase(const LinalgElementwiseOpFusionBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-fuse-elementwise-ops");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-fuse-elementwise-ops"; }

  ::llvm::StringRef getDescription() const override { return "Fuse elementwise operations on tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgElementwiseOpFusion");
  }
  ::llvm::StringRef getName() const override { return "LinalgElementwiseOpFusion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> allowFoldingUnitDimReshapes{*this, "allow-folding-unit-dim-reshapes", ::llvm::cl::desc("Allow fusing linalg.tensor_reshape ops that performs unit dimension collapsing"), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// LinalgFoldReshapeOpsByLinearization
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgFoldReshapeOpsByLinearizationBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgFoldReshapeOpsByLinearizationBase;

  LinalgFoldReshapeOpsByLinearizationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgFoldReshapeOpsByLinearizationBase(const LinalgFoldReshapeOpsByLinearizationBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-fold-reshape-ops-by-linearization");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-fold-reshape-ops-by-linearization"; }

  ::llvm::StringRef getDescription() const override { return "Fold TensorReshapeOps with generic/indexed generic ops by linearization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgFoldReshapeOpsByLinearization");
  }
  ::llvm::StringRef getName() const override { return "LinalgFoldReshapeOpsByLinearization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> allowFoldingUnitDimReshapes{*this, "allow-folding-unit-dim-reshapes", ::llvm::cl::desc("Allow fusing linalg.tensor_reshape ops that performs unit dimension collapsing"), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// LinalgFoldUnitExtentDims
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgFoldUnitExtentDimsBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgFoldUnitExtentDimsBase;

  LinalgFoldUnitExtentDimsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgFoldUnitExtentDimsBase(const LinalgFoldUnitExtentDimsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-fold-unit-extent-dims");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-fold-unit-extent-dims"; }

  ::llvm::StringRef getDescription() const override { return "Remove unit-extent dimension in Linalg ops on tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgFoldUnitExtentDims");
  }
  ::llvm::StringRef getName() const override { return "LinalgFoldUnitExtentDims"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<AffineDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> foldOneTripLoopsOnly{*this, "fold-one-trip-loops-only", ::llvm::cl::desc("Only folds the one-trip loops from Linalg ops on tensors (for testing purposes only)"), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// LinalgGeneralization
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgGeneralizationBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgGeneralizationBase;

  LinalgGeneralizationBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgGeneralizationBase(const LinalgGeneralizationBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-generalize-named-ops");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-generalize-named-ops"; }

  ::llvm::StringRef getDescription() const override { return "Convert named ops into generic ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgGeneralization");
  }
  ::llvm::StringRef getName() const override { return "LinalgGeneralization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgInlineScalarOperands
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgInlineScalarOperandsBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgInlineScalarOperandsBase;

  LinalgInlineScalarOperandsBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgInlineScalarOperandsBase(const LinalgInlineScalarOperandsBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-inline-scalar-operands");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-inline-scalar-operands"; }

  ::llvm::StringRef getDescription() const override { return "Inline scalar operands into linalg generic ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgInlineScalarOperands");
  }
  ::llvm::StringRef getName() const override { return "LinalgInlineScalarOperands"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgLowerToAffineLoops
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgLowerToAffineLoopsBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgLowerToAffineLoopsBase;

  LinalgLowerToAffineLoopsBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgLowerToAffineLoopsBase(const LinalgLowerToAffineLoopsBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-affine-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-affine-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into affine loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgLowerToAffineLoops");
  }
  ::llvm::StringRef getName() const override { return "LinalgLowerToAffineLoops"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgLowerToLoops
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgLowerToLoopsBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgLowerToLoopsBase;

  LinalgLowerToLoopsBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgLowerToLoopsBase(const LinalgLowerToLoopsBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgLowerToLoops");
  }
  ::llvm::StringRef getName() const override { return "LinalgLowerToLoops"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<AffineDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgLowerToParallelLoops
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgLowerToParallelLoopsBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgLowerToParallelLoopsBase;

  LinalgLowerToParallelLoopsBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgLowerToParallelLoopsBase(const LinalgLowerToParallelLoopsBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-parallel-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-parallel-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgLowerToParallelLoops");
  }
  ::llvm::StringRef getName() const override { return "LinalgLowerToParallelLoops"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgNamedOpConversion
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgNamedOpConversionBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgNamedOpConversionBase;

  LinalgNamedOpConversionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgNamedOpConversionBase(const LinalgNamedOpConversionBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-named-op-conversion");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-named-op-conversion"; }

  ::llvm::StringRef getDescription() const override { return "Convert from one named linalg op to another."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgNamedOpConversion");
  }
  ::llvm::StringRef getName() const override { return "LinalgNamedOpConversion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  registry.insert<tensor::TensorDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// LinalgPromotion
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgPromotionBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgPromotionBase;

  LinalgPromotionBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgPromotionBase(const LinalgPromotionBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-promote-subviews");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-promote-subviews"; }

  ::llvm::StringRef getDescription() const override { return "Promote subview ops to local buffers"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgPromotion");
  }
  ::llvm::StringRef getName() const override { return "LinalgPromotion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

protected:
  ::mlir::Pass::Option<bool> dynamicBuffers{*this, "test-promote-dynamic", ::llvm::cl::desc("Test generation of dynamic promoted buffers"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> useAlloca{*this, "test-use-alloca", ::llvm::cl::desc("Test generation of alloca'ed buffers."), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// LinalgStrategyDecomposePass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgStrategyDecomposePassBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgStrategyDecomposePassBase;

  LinalgStrategyDecomposePassBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgStrategyDecomposePassBase(const LinalgStrategyDecomposePassBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-strategy-decompose-pass");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-strategy-decompose-pass"; }

  ::llvm::StringRef getDescription() const override { return "Configurable pass to apply pattern-based generalization."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgStrategyDecomposePass");
  }
  ::llvm::StringRef getName() const override { return "LinalgStrategyDecomposePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

protected:
  ::mlir::Pass::Option<std::string> anchorFuncName{*this, "anchor-func", ::llvm::cl::desc("Which func op is the anchor to latch on.")};
};

//===----------------------------------------------------------------------===//
// LinalgStrategyEnablePass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgStrategyEnablePassBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgStrategyEnablePassBase;

  LinalgStrategyEnablePassBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgStrategyEnablePassBase(const LinalgStrategyEnablePassBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-strategy-enable-pass");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-strategy-enable-pass"; }

  ::llvm::StringRef getDescription() const override { return "Configurable pass to enable the application of other pattern-based linalg passes."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgStrategyEnablePass");
  }
  ::llvm::StringRef getName() const override { return "LinalgStrategyEnablePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

protected:
  ::mlir::Pass::Option<std::string> anchorFuncName{*this, "anchor-func", ::llvm::cl::desc("Which func op is the anchor to latch on.")};
};

//===----------------------------------------------------------------------===//
// LinalgStrategyGeneralizePass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgStrategyGeneralizePassBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgStrategyGeneralizePassBase;

  LinalgStrategyGeneralizePassBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgStrategyGeneralizePassBase(const LinalgStrategyGeneralizePassBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-strategy-generalize-pass");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-strategy-generalize-pass"; }

  ::llvm::StringRef getDescription() const override { return "Configurable pass to apply pattern-based generalization."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgStrategyGeneralizePass");
  }
  ::llvm::StringRef getName() const override { return "LinalgStrategyGeneralizePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

protected:
  ::mlir::Pass::Option<std::string> anchorFuncName{*this, "anchor-func", ::llvm::cl::desc("Which func op is the anchor to latch on.")};
  ::mlir::Pass::Option<std::string> anchorOpName{*this, "anchor-op", ::llvm::cl::desc("Which linalg op within the func is the anchor to latch on.")};
};

//===----------------------------------------------------------------------===//
// LinalgStrategyInterchangePass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgStrategyInterchangePassBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgStrategyInterchangePassBase;

  LinalgStrategyInterchangePassBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgStrategyInterchangePassBase(const LinalgStrategyInterchangePassBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-strategy-interchange-pass");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-strategy-interchange-pass"; }

  ::llvm::StringRef getDescription() const override { return "Configurable pass to apply pattern-based iterator interchange."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgStrategyInterchangePass");
  }
  ::llvm::StringRef getName() const override { return "LinalgStrategyInterchangePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

protected:
  ::mlir::Pass::Option<std::string> anchorFuncName{*this, "anchor-func", ::llvm::cl::desc("Which func op is the anchor to latch on.")};
};

//===----------------------------------------------------------------------===//
// LinalgStrategyLowerVectorsPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgStrategyLowerVectorsPassBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgStrategyLowerVectorsPassBase;

  LinalgStrategyLowerVectorsPassBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgStrategyLowerVectorsPassBase(const LinalgStrategyLowerVectorsPassBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-strategy-lower-vectors-pass");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-strategy-lower-vectors-pass"; }

  ::llvm::StringRef getDescription() const override { return "Configurable pass to lower vector operations."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgStrategyLowerVectorsPass");
  }
  ::llvm::StringRef getName() const override { return "LinalgStrategyLowerVectorsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

protected:
  ::mlir::Pass::Option<std::string> anchorFuncName{*this, "anchor-func", ::llvm::cl::desc("Which func op is the anchor to latch on.")};
};

//===----------------------------------------------------------------------===//
// LinalgStrategyPadPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgStrategyPadPassBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgStrategyPadPassBase;

  LinalgStrategyPadPassBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgStrategyPadPassBase(const LinalgStrategyPadPassBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-strategy-pad-pass");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-strategy-pad-pass"; }

  ::llvm::StringRef getDescription() const override { return "Configurable pass to apply padding and hoisting."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgStrategyPadPass");
  }
  ::llvm::StringRef getName() const override { return "LinalgStrategyPadPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

protected:
  ::mlir::Pass::Option<std::string> anchorFuncName{*this, "anchor-func", ::llvm::cl::desc("Which func op is the anchor to latch on.")};
  ::mlir::Pass::Option<std::string> anchorOpName{*this, "anchor-op", ::llvm::cl::desc("Which linalg op within the func is the anchor to latch on.")};
};

//===----------------------------------------------------------------------===//
// LinalgStrategyPromotePass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgStrategyPromotePassBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgStrategyPromotePassBase;

  LinalgStrategyPromotePassBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgStrategyPromotePassBase(const LinalgStrategyPromotePassBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-strategy-promote-pass");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-strategy-promote-pass"; }

  ::llvm::StringRef getDescription() const override { return "Configurable pass to apply pattern-based linalg promotion."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgStrategyPromotePass");
  }
  ::llvm::StringRef getName() const override { return "LinalgStrategyPromotePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

protected:
  ::mlir::Pass::Option<std::string> anchorFuncName{*this, "anchor-func", ::llvm::cl::desc("Which func op is the anchor to latch on.")};
  ::mlir::Pass::Option<std::string> anchorOpName{*this, "anchor-op", ::llvm::cl::desc("Which linalg op within the func is the anchor to latch on.")};
};

//===----------------------------------------------------------------------===//
// LinalgStrategyRemoveMarkersPass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgStrategyRemoveMarkersPassBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgStrategyRemoveMarkersPassBase;

  LinalgStrategyRemoveMarkersPassBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgStrategyRemoveMarkersPassBase(const LinalgStrategyRemoveMarkersPassBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-strategy-remove-markers-pass");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-strategy-remove-markers-pass"; }

  ::llvm::StringRef getDescription() const override { return "Cleanup pass that drops markers."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgStrategyRemoveMarkersPass");
  }
  ::llvm::StringRef getName() const override { return "LinalgStrategyRemoveMarkersPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

protected:
  ::mlir::Pass::Option<std::string> anchorFuncName{*this, "anchor-func", ::llvm::cl::desc("Which func op is the anchor to latch on.")};
};

//===----------------------------------------------------------------------===//
// LinalgStrategyTileAndFusePass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgStrategyTileAndFusePassBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgStrategyTileAndFusePassBase;

  LinalgStrategyTileAndFusePassBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgStrategyTileAndFusePassBase(const LinalgStrategyTileAndFusePassBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-strategy-tile-and-fuse-pass");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-strategy-tile-and-fuse-pass"; }

  ::llvm::StringRef getDescription() const override { return "Configurable pass to apply pattern-based tiling and fusion."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgStrategyTileAndFusePass");
  }
  ::llvm::StringRef getName() const override { return "LinalgStrategyTileAndFusePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<std::string> anchorFuncName{*this, "anchor-func", ::llvm::cl::desc("Which func op is the anchor to latch on.")};
  ::mlir::Pass::Option<std::string> anchorOpName{*this, "anchor-op", ::llvm::cl::desc("Which linalg op within the func is the anchor to latch on.")};
};

//===----------------------------------------------------------------------===//
// LinalgStrategyTilePass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgStrategyTilePassBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgStrategyTilePassBase;

  LinalgStrategyTilePassBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgStrategyTilePassBase(const LinalgStrategyTilePassBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-strategy-tile-pass");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-strategy-tile-pass"; }

  ::llvm::StringRef getDescription() const override { return "Configurable pass to apply pattern-based linalg tiling."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgStrategyTilePass");
  }
  ::llvm::StringRef getName() const override { return "LinalgStrategyTilePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

protected:
  ::mlir::Pass::Option<std::string> anchorFuncName{*this, "anchor-func", ::llvm::cl::desc("Which func op is the anchor to latch on.")};
  ::mlir::Pass::Option<std::string> anchorOpName{*this, "anchor-op", ::llvm::cl::desc("Which linalg op within the func is the anchor to latch on.")};
};

//===----------------------------------------------------------------------===//
// LinalgStrategyVectorizePass
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgStrategyVectorizePassBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgStrategyVectorizePassBase;

  LinalgStrategyVectorizePassBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgStrategyVectorizePassBase(const LinalgStrategyVectorizePassBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-strategy-vectorize-pass");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-strategy-vectorize-pass"; }

  ::llvm::StringRef getDescription() const override { return "Configurable pass to apply pattern-based linalg vectorization."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgStrategyVectorizePass");
  }
  ::llvm::StringRef getName() const override { return "LinalgStrategyVectorizePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<linalg::LinalgDialect>();

  }

protected:
  ::mlir::Pass::Option<std::string> anchorFuncName{*this, "anchor-func", ::llvm::cl::desc("Which func op is the anchor to latch on.")};
  ::mlir::Pass::Option<std::string> anchorOpName{*this, "anchor-op", ::llvm::cl::desc("Which linalg op within the func is the anchor to latch on.")};
  ::mlir::Pass::Option<bool> vectorizePadding{*this, "vectorize-padding", ::llvm::cl::desc("Enable vectorization of padding ops."), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// LinalgTiling
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LinalgTilingBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LinalgTilingBase;

  LinalgTilingBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgTilingBase(const LinalgTilingBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-tile");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-tile"; }

  ::llvm::StringRef getDescription() const override { return "Tile operations in the linalg dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgTiling");
  }
  ::llvm::StringRef getName() const override { return "LinalgTiling"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  }

protected:
  ::mlir::Pass::ListOption<int64_t> tileSizes{*this, "tile-sizes", ::llvm::cl::desc("Tile sizes"), llvm::cl::ZeroOrMore, llvm::cl::MiscFlags::CommaSeparated};
  ::mlir::Pass::Option<std::string> loopType{*this, "loop-type", ::llvm::cl::desc("Specify the type of loops to generate: for, parallel"), ::llvm::cl::init("for")};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// ConvertElementwiseToLinalg Registration
//===----------------------------------------------------------------------===//

inline void registerConvertElementwiseToLinalgPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertElementwiseToLinalgPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgComprehensiveModuleBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgComprehensiveModuleBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgComprehensiveModuleBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgDetensorize Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgDetensorizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgDetensorizePass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgElementwiseOpFusion Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgElementwiseOpFusionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgElementwiseOpFusionPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgFoldReshapeOpsByLinearization Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgFoldReshapeOpsByLinearizationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createFoldReshapeOpsByLinearizationPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgFoldUnitExtentDims Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgFoldUnitExtentDimsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgFoldUnitExtentDimsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgGeneralization Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgGeneralizationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgGeneralizationPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgInlineScalarOperands Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgInlineScalarOperandsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgInlineScalarOperandsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgLowerToAffineLoops Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgLowerToAffineLoopsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToAffineLoopsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgLowerToLoops Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgLowerToLoopsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToLoopsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgLowerToParallelLoops Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgLowerToParallelLoopsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createConvertLinalgToParallelLoopsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgNamedOpConversion Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgNamedOpConversionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgNamedOpConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgPromotion Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgPromotionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgPromotionPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgStrategyDecomposePass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgStrategyDecomposePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgStrategyDecomposePass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgStrategyEnablePass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgStrategyEnablePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgStrategyEnablePass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgStrategyGeneralizePass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgStrategyGeneralizePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgStrategyGeneralizePass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgStrategyInterchangePass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgStrategyInterchangePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgStrategyInterchangePass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgStrategyLowerVectorsPass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgStrategyLowerVectorsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgStrategyLowerVectorsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgStrategyPadPass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgStrategyPadPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgStrategyPadPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgStrategyPromotePass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgStrategyPromotePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgStrategyPromotePass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgStrategyRemoveMarkersPass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgStrategyRemoveMarkersPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgStrategyRemoveMarkersPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgStrategyTileAndFusePass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgStrategyTileAndFusePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgStrategyTileAndFusePass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgStrategyTilePass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgStrategyTilePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgStrategyTilePass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgStrategyVectorizePass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgStrategyVectorizePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgStrategyVectorizePass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgTiling Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgTilingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLinalgTilingPass();
  });
}

//===----------------------------------------------------------------------===//
// Linalg Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgPasses() {
  registerConvertElementwiseToLinalgPass();
  registerLinalgBufferizePass();
  registerLinalgComprehensiveModuleBufferizePass();
  registerLinalgDetensorizePass();
  registerLinalgElementwiseOpFusionPass();
  registerLinalgFoldReshapeOpsByLinearizationPass();
  registerLinalgFoldUnitExtentDimsPass();
  registerLinalgGeneralizationPass();
  registerLinalgInlineScalarOperandsPass();
  registerLinalgLowerToAffineLoopsPass();
  registerLinalgLowerToLoopsPass();
  registerLinalgLowerToParallelLoopsPass();
  registerLinalgNamedOpConversionPass();
  registerLinalgPromotionPass();
  registerLinalgStrategyDecomposePassPass();
  registerLinalgStrategyEnablePassPass();
  registerLinalgStrategyGeneralizePassPass();
  registerLinalgStrategyInterchangePassPass();
  registerLinalgStrategyLowerVectorsPassPass();
  registerLinalgStrategyPadPassPass();
  registerLinalgStrategyPromotePassPass();
  registerLinalgStrategyRemoveMarkersPassPass();
  registerLinalgStrategyTileAndFusePassPass();
  registerLinalgStrategyTilePassPass();
  registerLinalgStrategyVectorizePassPass();
  registerLinalgTilingPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
