/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

DEFINE_EXPLICIT_TYPE_ID(::mlir::arm_sve::ArmSVEDialect)
namespace mlir {
namespace arm_sve {

ArmSVEDialect::~ArmSVEDialect() = default;

} // namespace arm_sve
} // namespace mlir
