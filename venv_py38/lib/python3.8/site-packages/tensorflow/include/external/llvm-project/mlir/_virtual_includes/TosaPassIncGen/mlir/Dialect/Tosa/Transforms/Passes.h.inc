/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// TosaInferShapes
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TosaInferShapesBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = TosaInferShapesBase;

  TosaInferShapesBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TosaInferShapesBase(const TosaInferShapesBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-infer-shapes");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-infer-shapes"; }

  ::llvm::StringRef getDescription() const override { return "Propagate shapes across TOSA operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaInferShapes");
  }
  ::llvm::StringRef getName() const override { return "TosaInferShapes"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<func::FuncDialect>();

  registry.insert<tensor::TensorDialect>();

  registry.insert<tosa::TosaDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// TosaMakeBroadcastable
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TosaMakeBroadcastableBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = TosaMakeBroadcastableBase;

  TosaMakeBroadcastableBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TosaMakeBroadcastableBase(const TosaMakeBroadcastableBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-make-broadcastable");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-make-broadcastable"; }

  ::llvm::StringRef getDescription() const override { return "TOSA rank Reshape to enable Broadcasting"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaMakeBroadcastable");
  }
  ::llvm::StringRef getName() const override { return "TosaMakeBroadcastable"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// TosaOptionalDecompositions
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class TosaOptionalDecompositionsBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = TosaOptionalDecompositionsBase;

  TosaOptionalDecompositionsBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TosaOptionalDecompositionsBase(const TosaOptionalDecompositionsBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tosa-optional-decompositions");
  }
  ::llvm::StringRef getArgument() const override { return "tosa-optional-decompositions"; }

  ::llvm::StringRef getDescription() const override { return "Applies Tosa operations optional decompositions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TosaOptionalDecompositions");
  }
  ::llvm::StringRef getName() const override { return "TosaOptionalDecompositions"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// TosaInferShapes Registration
//===----------------------------------------------------------------------===//

inline void registerTosaInferShapesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTosaInferShapesPass();
  });
}

//===----------------------------------------------------------------------===//
// TosaMakeBroadcastable Registration
//===----------------------------------------------------------------------===//

inline void registerTosaMakeBroadcastablePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTosaMakeBroadcastablePass();
  });
}

//===----------------------------------------------------------------------===//
// TosaOptionalDecompositions Registration
//===----------------------------------------------------------------------===//

inline void registerTosaOptionalDecompositionsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tosa::createTosaOptionalDecompositions();
  });
}

//===----------------------------------------------------------------------===//
// TosaOpt Registration
//===----------------------------------------------------------------------===//

inline void registerTosaOptPasses() {
  registerTosaInferShapesPass();
  registerTosaMakeBroadcastablePass();
  registerTosaOptionalDecompositionsPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
