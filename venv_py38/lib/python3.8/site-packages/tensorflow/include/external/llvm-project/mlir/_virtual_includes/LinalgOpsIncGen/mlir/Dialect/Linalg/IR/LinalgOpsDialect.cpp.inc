/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

DEFINE_EXPLICIT_TYPE_ID(::mlir::linalg::LinalgDialect)
namespace mlir {
namespace linalg {

LinalgDialect::~LinalgDialect() = default;

} // namespace linalg
} // namespace mlir
