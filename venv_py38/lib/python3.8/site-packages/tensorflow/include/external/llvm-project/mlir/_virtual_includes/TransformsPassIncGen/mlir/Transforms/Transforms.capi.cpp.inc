/* Autogenerated by mlir-tblgen; don't manually edit. */
//===----------------------------------------------------------------------===//
// Transforms Group Registration
//===----------------------------------------------------------------------===//

void mlirRegisterTransformsPasses() {
  registerTransformsPasses();
}

MlirPass mlirCreateTransformsCSE() {
  return wrap(mlir::createCSEPass().release());
}
void mlirRegisterTransformsCSE() {
  registerCSEPass();
}


MlirPass mlirCreateTransformsCanonicalizer() {
  return wrap(mlir::createCanonicalizerPass().release());
}
void mlirRegisterTransformsCanonicalizer() {
  registerCanonicalizerPass();
}


MlirPass mlirCreateTransformsControlFlowSink() {
  return wrap(::mlir::createControlFlowSinkPass().release());
}
void mlirRegisterTransformsControlFlowSink() {
  registerControlFlowSinkPass();
}


MlirPass mlirCreateTransformsInliner() {
  return wrap(mlir::createInlinerPass().release());
}
void mlirRegisterTransformsInliner() {
  registerInlinerPass();
}


MlirPass mlirCreateTransformsLocationSnapshot() {
  return wrap(mlir::createLocationSnapshotPass().release());
}
void mlirRegisterTransformsLocationSnapshot() {
  registerLocationSnapshotPass();
}


MlirPass mlirCreateTransformsLoopInvariantCodeMotion() {
  return wrap(mlir::createLoopInvariantCodeMotionPass().release());
}
void mlirRegisterTransformsLoopInvariantCodeMotion() {
  registerLoopInvariantCodeMotionPass();
}


MlirPass mlirCreateTransformsPrintOpStats() {
  return wrap(mlir::createPrintOpStatsPass().release());
}
void mlirRegisterTransformsPrintOpStats() {
  registerPrintOpStatsPass();
}


MlirPass mlirCreateTransformsSCCP() {
  return wrap(mlir::createSCCPPass().release());
}
void mlirRegisterTransformsSCCP() {
  registerSCCPPass();
}


MlirPass mlirCreateTransformsStripDebugInfo() {
  return wrap(mlir::createStripDebugInfoPass().release());
}
void mlirRegisterTransformsStripDebugInfo() {
  registerStripDebugInfoPass();
}


MlirPass mlirCreateTransformsSymbolDCE() {
  return wrap(mlir::createSymbolDCEPass().release());
}
void mlirRegisterTransformsSymbolDCE() {
  registerSymbolDCEPass();
}


MlirPass mlirCreateTransformsSymbolPrivatize() {
  return wrap(mlir::createSymbolPrivatizePass().release());
}
void mlirRegisterTransformsSymbolPrivatize() {
  registerSymbolPrivatizePass();
}


MlirPass mlirCreateTransformsViewOpGraph() {
  return wrap(mlir::createPrintOpGraphPass().release());
}
void mlirRegisterTransformsViewOpGraph() {
  registerViewOpGraphPass();
}

