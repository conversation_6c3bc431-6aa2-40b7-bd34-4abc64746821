/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// CSE
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class CSEBase : public ::mlir::OperationPass<> {
public:
  using Base = CSEBase;

  CSEBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CSEBase(const CSEBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("cse");
  }
  ::llvm::StringRef getArgument() const override { return "cse"; }

  ::llvm::StringRef getDescription() const override { return "Eliminate common sub-expressions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CSE");
  }
  ::llvm::StringRef getName() const override { return "CSE"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Statistic numCSE{this, "num-cse'd", "Number of operations CSE'd"};
  ::mlir::Pass::Statistic numDCE{this, "num-dce'd", "Number of operations DCE'd"};
};

//===----------------------------------------------------------------------===//
// Canonicalizer
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class CanonicalizerBase : public ::mlir::OperationPass<> {
public:
  using Base = CanonicalizerBase;

  CanonicalizerBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CanonicalizerBase(const CanonicalizerBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("canonicalize");
  }
  ::llvm::StringRef getArgument() const override { return "canonicalize"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalize operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Canonicalizer");
  }
  ::llvm::StringRef getName() const override { return "Canonicalizer"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<bool> topDownProcessingEnabled{*this, "top-down", ::llvm::cl::desc("Seed the worklist in general top-down order"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> enableRegionSimplification{*this, "region-simplify", ::llvm::cl::desc("Seed the worklist in general top-down order"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<int64_t> maxIterations{*this, "max-iterations", ::llvm::cl::desc("Seed the worklist in general top-down order"), ::llvm::cl::init(10)};
  ::mlir::Pass::ListOption<std::string> disabledPatterns{*this, "disable-patterns", ::llvm::cl::desc("Labels of patterns that should be filtered out during application"), llvm::cl::MiscFlags::CommaSeparated};
  ::mlir::Pass::ListOption<std::string> enabledPatterns{*this, "enable-patterns", ::llvm::cl::desc("Labels of patterns that should be used during application, all other patterns are filtered out"), llvm::cl::MiscFlags::CommaSeparated};
};

//===----------------------------------------------------------------------===//
// ControlFlowSink
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ControlFlowSinkBase : public ::mlir::OperationPass<> {
public:
  using Base = ControlFlowSinkBase;

  ControlFlowSinkBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ControlFlowSinkBase(const ControlFlowSinkBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("control-flow-sink");
  }
  ::llvm::StringRef getArgument() const override { return "control-flow-sink"; }

  ::llvm::StringRef getDescription() const override { return "Sink operations into conditional blocks"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ControlFlowSink");
  }
  ::llvm::StringRef getName() const override { return "ControlFlowSink"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Statistic numSunk{this, "num-sunk", "Number of operations sunk"};
};

//===----------------------------------------------------------------------===//
// Inliner
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class InlinerBase : public ::mlir::OperationPass<> {
public:
  using Base = InlinerBase;

  InlinerBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  InlinerBase(const InlinerBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("inline");
  }
  ::llvm::StringRef getArgument() const override { return "inline"; }

  ::llvm::StringRef getDescription() const override { return "Inline function calls"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Inliner");
  }
  ::llvm::StringRef getName() const override { return "Inliner"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<std::string> defaultPipelineStr{*this, "default-pipeline", ::llvm::cl::desc("The default optimizer pipeline used for callables")};
  ::mlir::Pass::ListOption<std::string> opPipelineStrs{*this, "op-pipelines", ::llvm::cl::desc("Callable operation specific optimizer pipelines (in the form of `dialect.op(pipeline)`)"), llvm::cl::MiscFlags::CommaSeparated};
  ::mlir::Pass::Option<unsigned> maxInliningIterations{*this, "max-iterations", ::llvm::cl::desc("Maximum number of iterations when inlining within an SCC"), ::llvm::cl::init(4)};
};

//===----------------------------------------------------------------------===//
// LocationSnapshot
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LocationSnapshotBase : public ::mlir::OperationPass<> {
public:
  using Base = LocationSnapshotBase;

  LocationSnapshotBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LocationSnapshotBase(const LocationSnapshotBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("snapshot-op-locations");
  }
  ::llvm::StringRef getArgument() const override { return "snapshot-op-locations"; }

  ::llvm::StringRef getDescription() const override { return "Generate new locations from the current IR"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LocationSnapshot");
  }
  ::llvm::StringRef getName() const override { return "LocationSnapshot"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<std::string> fileName{*this, "filename", ::llvm::cl::desc("The filename to print the generated IR")};
  ::mlir::Pass::Option<std::string> tag{*this, "tag", ::llvm::cl::desc("A tag to use when fusing the new locations with the original. If unset, the locations are replaced.")};
};

//===----------------------------------------------------------------------===//
// LoopInvariantCodeMotion
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LoopInvariantCodeMotionBase : public ::mlir::OperationPass<> {
public:
  using Base = LoopInvariantCodeMotionBase;

  LoopInvariantCodeMotionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LoopInvariantCodeMotionBase(const LoopInvariantCodeMotionBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("loop-invariant-code-motion");
  }
  ::llvm::StringRef getArgument() const override { return "loop-invariant-code-motion"; }

  ::llvm::StringRef getDescription() const override { return "Hoist loop invariant instructions outside of the loop"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LoopInvariantCodeMotion");
  }
  ::llvm::StringRef getName() const override { return "LoopInvariantCodeMotion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// PrintOpStats
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class PrintOpStatsBase : public ::mlir::OperationPass<> {
public:
  using Base = PrintOpStatsBase;

  PrintOpStatsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  PrintOpStatsBase(const PrintOpStatsBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("print-op-stats");
  }
  ::llvm::StringRef getArgument() const override { return "print-op-stats"; }

  ::llvm::StringRef getDescription() const override { return "Print statistics of operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PrintOpStats");
  }
  ::llvm::StringRef getName() const override { return "PrintOpStats"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// SCCP
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SCCPBase : public ::mlir::OperationPass<> {
public:
  using Base = SCCPBase;

  SCCPBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCCPBase(const SCCPBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sccp");
  }
  ::llvm::StringRef getArgument() const override { return "sccp"; }

  ::llvm::StringRef getDescription() const override { return "Sparse Conditional Constant Propagation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCCP");
  }
  ::llvm::StringRef getName() const override { return "SCCP"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// StripDebugInfo
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class StripDebugInfoBase : public ::mlir::OperationPass<> {
public:
  using Base = StripDebugInfoBase;

  StripDebugInfoBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  StripDebugInfoBase(const StripDebugInfoBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("strip-debuginfo");
  }
  ::llvm::StringRef getArgument() const override { return "strip-debuginfo"; }

  ::llvm::StringRef getDescription() const override { return "Strip debug info from all operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StripDebugInfo");
  }
  ::llvm::StringRef getName() const override { return "StripDebugInfo"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// SymbolDCE
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SymbolDCEBase : public ::mlir::OperationPass<> {
public:
  using Base = SymbolDCEBase;

  SymbolDCEBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SymbolDCEBase(const SymbolDCEBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("symbol-dce");
  }
  ::llvm::StringRef getArgument() const override { return "symbol-dce"; }

  ::llvm::StringRef getDescription() const override { return "Eliminate dead symbols"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SymbolDCE");
  }
  ::llvm::StringRef getName() const override { return "SymbolDCE"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Statistic numDCE{this, "num-dce'd", "Number of symbols DCE'd"};
};

//===----------------------------------------------------------------------===//
// SymbolPrivatize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SymbolPrivatizeBase : public ::mlir::OperationPass<> {
public:
  using Base = SymbolPrivatizeBase;

  SymbolPrivatizeBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SymbolPrivatizeBase(const SymbolPrivatizeBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("symbol-privatize");
  }
  ::llvm::StringRef getArgument() const override { return "symbol-privatize"; }

  ::llvm::StringRef getDescription() const override { return "Mark symbols private"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SymbolPrivatize");
  }
  ::llvm::StringRef getName() const override { return "SymbolPrivatize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::ListOption<std::string> exclude{*this, "exclude", ::llvm::cl::desc("Comma separated list of symbols that should not be marked private"), llvm::cl::MiscFlags::CommaSeparated};
};

//===----------------------------------------------------------------------===//
// ViewOpGraph
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ViewOpGraphBase : public ::mlir::OperationPass<> {
public:
  using Base = ViewOpGraphBase;

  ViewOpGraphBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ViewOpGraphBase(const ViewOpGraphBase &other) : ::mlir::OperationPass<>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("view-op-graph");
  }
  ::llvm::StringRef getArgument() const override { return "view-op-graph"; }

  ::llvm::StringRef getDescription() const override { return "Print Graphviz visualization of an operation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ViewOpGraph");
  }
  ::llvm::StringRef getName() const override { return "ViewOpGraph"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<unsigned> maxLabelLen{*this, "max-label-len", ::llvm::cl::desc("Limit attribute/type length to number of chars"), ::llvm::cl::init(20)};
  ::mlir::Pass::Option<bool> printAttrs{*this, "print-attrs", ::llvm::cl::desc("Print attributes of operations"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> printControlFlowEdges{*this, "print-control-flow-edges", ::llvm::cl::desc("Print control flow edges"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> printDataFlowEdges{*this, "print-data-flow-edges", ::llvm::cl::desc("Print data flow edges"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> printResultTypes{*this, "print-result-types", ::llvm::cl::desc("Print result types of operations"), ::llvm::cl::init(true)};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// CSE Registration
//===----------------------------------------------------------------------===//

inline void registerCSEPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createCSEPass();
  });
}

//===----------------------------------------------------------------------===//
// Canonicalizer Registration
//===----------------------------------------------------------------------===//

inline void registerCanonicalizerPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createCanonicalizerPass();
  });
}

//===----------------------------------------------------------------------===//
// ControlFlowSink Registration
//===----------------------------------------------------------------------===//

inline void registerControlFlowSinkPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::createControlFlowSinkPass();
  });
}

//===----------------------------------------------------------------------===//
// Inliner Registration
//===----------------------------------------------------------------------===//

inline void registerInlinerPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createInlinerPass();
  });
}

//===----------------------------------------------------------------------===//
// LocationSnapshot Registration
//===----------------------------------------------------------------------===//

inline void registerLocationSnapshotPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLocationSnapshotPass();
  });
}

//===----------------------------------------------------------------------===//
// LoopInvariantCodeMotion Registration
//===----------------------------------------------------------------------===//

inline void registerLoopInvariantCodeMotionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLoopInvariantCodeMotionPass();
  });
}

//===----------------------------------------------------------------------===//
// PrintOpStats Registration
//===----------------------------------------------------------------------===//

inline void registerPrintOpStatsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPrintOpStatsPass();
  });
}

//===----------------------------------------------------------------------===//
// SCCP Registration
//===----------------------------------------------------------------------===//

inline void registerSCCPPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSCCPPass();
  });
}

//===----------------------------------------------------------------------===//
// StripDebugInfo Registration
//===----------------------------------------------------------------------===//

inline void registerStripDebugInfoPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createStripDebugInfoPass();
  });
}

//===----------------------------------------------------------------------===//
// SymbolDCE Registration
//===----------------------------------------------------------------------===//

inline void registerSymbolDCEPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSymbolDCEPass();
  });
}

//===----------------------------------------------------------------------===//
// SymbolPrivatize Registration
//===----------------------------------------------------------------------===//

inline void registerSymbolPrivatizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSymbolPrivatizePass();
  });
}

//===----------------------------------------------------------------------===//
// ViewOpGraph Registration
//===----------------------------------------------------------------------===//

inline void registerViewOpGraphPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPrintOpGraphPass();
  });
}

//===----------------------------------------------------------------------===//
// Transforms Registration
//===----------------------------------------------------------------------===//

inline void registerTransformsPasses() {
  registerCSEPass();
  registerCanonicalizerPass();
  registerControlFlowSinkPass();
  registerInlinerPass();
  registerLocationSnapshotPass();
  registerLoopInvariantCodeMotionPass();
  registerPrintOpStatsPass();
  registerSCCPPass();
  registerStripDebugInfoPass();
  registerSymbolDCEPass();
  registerSymbolPrivatizePass();
  registerViewOpGraphPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
