/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// VectorBufferize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class VectorBufferizeBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = VectorBufferizeBase;

  VectorBufferizeBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  VectorBufferizeBase(const VectorBufferizeBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("vector-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "vector-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize Vector dialect ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VectorBufferize");
  }
  ::llvm::StringRef getName() const override { return "VectorBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// VectorBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerVectorBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::vector::createVectorBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// Vector Registration
//===----------------------------------------------------------------------===//

inline void registerVectorPasses() {
  registerVectorBufferizePass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
