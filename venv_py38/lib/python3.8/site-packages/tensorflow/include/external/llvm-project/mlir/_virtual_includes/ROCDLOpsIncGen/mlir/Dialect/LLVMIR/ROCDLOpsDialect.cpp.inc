/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

DEFINE_EXPLICIT_TYPE_ID(::mlir::ROCDL::ROCDLDialect)
namespace mlir {
namespace ROCDL {

ROCDLDialect::~ROCDLDialect() = default;

} // namespace ROCDL
} // namespace mlir
