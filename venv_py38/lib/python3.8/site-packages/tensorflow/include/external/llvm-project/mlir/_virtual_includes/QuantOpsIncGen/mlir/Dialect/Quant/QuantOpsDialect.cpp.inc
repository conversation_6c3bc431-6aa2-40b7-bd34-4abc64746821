/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

DEFINE_EXPLICIT_TYPE_ID(::mlir::quant::QuantizationDialect)
namespace mlir {
namespace quant {

QuantizationDialect::~QuantizationDialect() = default;

} // namespace quant
} // namespace mlir
