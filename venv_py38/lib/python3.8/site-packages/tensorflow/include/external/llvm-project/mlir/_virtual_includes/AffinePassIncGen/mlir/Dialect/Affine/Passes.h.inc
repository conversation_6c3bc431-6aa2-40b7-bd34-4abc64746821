/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// AffineDataCopyGeneration
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AffineDataCopyGenerationBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = AffineDataCopyGenerationBase;

  AffineDataCopyGenerationBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineDataCopyGenerationBase(const AffineDataCopyGenerationBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-data-copy-generate");
  }
  ::llvm::StringRef getArgument() const override { return "affine-data-copy-generate"; }

  ::llvm::StringRef getDescription() const override { return "Generate explicit copying for affine memory operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineDataCopyGeneration");
  }
  ::llvm::StringRef getName() const override { return "AffineDataCopyGeneration"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  }

protected:
  ::mlir::Pass::Option<uint64_t> fastMemoryCapacity{*this, "fast-mem-capacity", ::llvm::cl::desc("Set fast memory space capacity in KiB (default: unlimited)"), ::llvm::cl::init(std::numeric_limits<uint64_t>::max())};
  ::mlir::Pass::Option<unsigned> fastMemorySpace{*this, "fast-mem-space", ::llvm::cl::desc("Fast memory space identifier for copy generation (default: 1)"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<bool> generateDma{*this, "generate-dma", ::llvm::cl::desc("Generate DMA instead of point-wise copy"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<int> minDmaTransferSize{*this, "min-dma-transfer", ::llvm::cl::desc("Minimum DMA transfer size supported by the target in bytes"), ::llvm::cl::init(1024)};
  ::mlir::Pass::Option<unsigned> slowMemorySpace{*this, "slow-mem-space", ::llvm::cl::desc("Slow memory space identifier for copy generation (default: 0)"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> skipNonUnitStrideLoops{*this, "skip-non-unit-stride-loops", ::llvm::cl::desc("Testing purposes: avoid non-unit stride loop choice depths for copy placement"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> tagMemorySpace{*this, "tag-mem-space", ::llvm::cl::desc("Tag memory space identifier for copy generation (default: 0)"), ::llvm::cl::init(0)};
};

//===----------------------------------------------------------------------===//
// AffineLoopFusion
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AffineLoopFusionBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = AffineLoopFusionBase;

  AffineLoopFusionBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopFusionBase(const AffineLoopFusionBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-fusion");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-fusion"; }

  ::llvm::StringRef getDescription() const override { return "Fuse affine loop nests"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopFusion");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopFusion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  }

protected:
  ::mlir::Pass::Option<double> computeToleranceThreshold{*this, "fusion-compute-tolerance", ::llvm::cl::desc("Fractional increase in additional computation tolerated while fusing"), ::llvm::cl::init(0.30f)};
  ::mlir::Pass::Option<unsigned> fastMemorySpace{*this, "fusion-fast-mem-space", ::llvm::cl::desc("Faster memory space number to promote fusion buffers to"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<uint64_t> localBufSizeThreshold{*this, "fusion-local-buf-threshold", ::llvm::cl::desc("Threshold size (KiB) for promoting local buffers to fast memory space"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> maximalFusion{*this, "fusion-maximal", ::llvm::cl::desc("Enables maximal loop fusion"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<enum FusionMode> affineFusionMode{*this, "mode", ::llvm::cl::desc("fusion mode to attempt"), ::llvm::cl::init(mlir::FusionMode::Greedy), llvm::cl::values(clEnumValN(mlir::FusionMode::Greedy, "greedy", "Perform greedy (both producer-consumer and sibling)  fusion"), clEnumValN( mlir::FusionMode::ProducerConsumer, "producer", "Perform only producer-consumer fusion"), clEnumValN( mlir::FusionMode::Sibling, "sibling", "Perform only sibling fusion"))};
};

//===----------------------------------------------------------------------===//
// AffineLoopInvariantCodeMotion
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AffineLoopInvariantCodeMotionBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = AffineLoopInvariantCodeMotionBase;

  AffineLoopInvariantCodeMotionBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopInvariantCodeMotionBase(const AffineLoopInvariantCodeMotionBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-invariant-code-motion");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-invariant-code-motion"; }

  ::llvm::StringRef getDescription() const override { return "Hoist loop invariant instructions outside of affine loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopInvariantCodeMotion");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopInvariantCodeMotion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// AffineLoopNormalize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AffineLoopNormalizeBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = AffineLoopNormalizeBase;

  AffineLoopNormalizeBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopNormalizeBase(const AffineLoopNormalizeBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-normalize");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-normalize"; }

  ::llvm::StringRef getDescription() const override { return "Apply normalization transformations to affine loop-like ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopNormalize");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopNormalize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// AffineLoopTiling
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AffineLoopTilingBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = AffineLoopTilingBase;

  AffineLoopTilingBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopTilingBase(const AffineLoopTilingBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-tile");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-tile"; }

  ::llvm::StringRef getDescription() const override { return "Tile affine loop nests"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopTiling");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopTiling"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<uint64_t> cacheSizeInKiB{*this, "cache-size", ::llvm::cl::desc("Set size of cache to tile for in KiB"), ::llvm::cl::init(512)};
  ::mlir::Pass::Option<bool> separate{*this, "separate", ::llvm::cl::desc("Separate full and partial tiles")};
  ::mlir::Pass::Option<unsigned> tileSize{*this, "tile-size", ::llvm::cl::desc("Use this tile size for all loops")};
  ::mlir::Pass::ListOption<unsigned> tileSizes{*this, "tile-sizes", ::llvm::cl::desc("List of tile sizes for each perfect nest (overridden by -tile-size)"), llvm::cl::ZeroOrMore};
};

//===----------------------------------------------------------------------===//
// AffineLoopUnroll
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AffineLoopUnrollBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = AffineLoopUnrollBase;

  AffineLoopUnrollBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopUnrollBase(const AffineLoopUnrollBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-unroll");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-unroll"; }

  ::llvm::StringRef getDescription() const override { return "Unroll affine loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopUnroll");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopUnroll"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<unsigned> unrollFactor{*this, "unroll-factor", ::llvm::cl::desc("Use this unroll factor for all loops being unrolled"), ::llvm::cl::init(4)};
  ::mlir::Pass::Option<bool> unrollUpToFactor{*this, "unroll-up-to-factor", ::llvm::cl::desc("Allow unrolling up to the factor specified"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> unrollFull{*this, "unroll-full", ::llvm::cl::desc("Fully unroll loops"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> numRepetitions{*this, "unroll-num-reps", ::llvm::cl::desc("Unroll innermost loops repeatedly this many times"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<unsigned> unrollFullThreshold{*this, "unroll-full-threshold", ::llvm::cl::desc("Unroll all loops with trip count less than or equal to this"), ::llvm::cl::init(1)};
};

//===----------------------------------------------------------------------===//
// AffineLoopUnrollAndJam
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AffineLoopUnrollAndJamBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = AffineLoopUnrollAndJamBase;

  AffineLoopUnrollAndJamBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineLoopUnrollAndJamBase(const AffineLoopUnrollAndJamBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-unroll-jam");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-unroll-jam"; }

  ::llvm::StringRef getDescription() const override { return "Unroll and jam affine loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineLoopUnrollAndJam");
  }
  ::llvm::StringRef getName() const override { return "AffineLoopUnrollAndJam"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<unsigned> unrollJamFactor{*this, "unroll-jam-factor", ::llvm::cl::desc("Use this unroll jam factor for all loops (default 4)"), ::llvm::cl::init(4)};
};

//===----------------------------------------------------------------------===//
// AffineParallelize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AffineParallelizeBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = AffineParallelizeBase;

  AffineParallelizeBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineParallelizeBase(const AffineParallelizeBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-parallelize");
  }
  ::llvm::StringRef getArgument() const override { return "affine-parallelize"; }

  ::llvm::StringRef getDescription() const override { return "Convert affine.for ops into 1-D affine.parallel"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineParallelize");
  }
  ::llvm::StringRef getName() const override { return "AffineParallelize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<unsigned> maxNested{*this, "max-nested", ::llvm::cl::desc("Maximum number of nested parallel loops to produce. Defaults to unlimited (UINT_MAX)."), ::llvm::cl::init(-1u)};
  ::mlir::Pass::Option<bool> parallelReductions{*this, "parallel-reductions", ::llvm::cl::desc("Whether to parallelize reduction loops. Defaults to false."), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// AffinePipelineDataTransfer
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AffinePipelineDataTransferBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = AffinePipelineDataTransferBase;

  AffinePipelineDataTransferBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffinePipelineDataTransferBase(const AffinePipelineDataTransferBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-pipeline-data-transfer");
  }
  ::llvm::StringRef getArgument() const override { return "affine-pipeline-data-transfer"; }

  ::llvm::StringRef getDescription() const override { return "Pipeline non-blocking data transfers between explicitly managed levels of the memory hierarchy"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffinePipelineDataTransfer");
  }
  ::llvm::StringRef getName() const override { return "AffinePipelineDataTransfer"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// AffineScalarReplacement
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AffineScalarReplacementBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = AffineScalarReplacementBase;

  AffineScalarReplacementBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineScalarReplacementBase(const AffineScalarReplacementBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-scalrep");
  }
  ::llvm::StringRef getArgument() const override { return "affine-scalrep"; }

  ::llvm::StringRef getDescription() const override { return "Replace affine memref acceses by scalars by forwarding stores to loads and eliminating redundant loads"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineScalarReplacement");
  }
  ::llvm::StringRef getName() const override { return "AffineScalarReplacement"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// AffineVectorize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class AffineVectorizeBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = AffineVectorizeBase;

  AffineVectorizeBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AffineVectorizeBase(const AffineVectorizeBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-super-vectorize");
  }
  ::llvm::StringRef getArgument() const override { return "affine-super-vectorize"; }

  ::llvm::StringRef getDescription() const override { return "Vectorize to a target independent n-D vector abstraction"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AffineVectorize");
  }
  ::llvm::StringRef getName() const override { return "AffineVectorize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<vector::VectorDialect>();

  }

protected:
  ::mlir::Pass::ListOption<int64_t> vectorSizes{*this, "virtual-vector-size", ::llvm::cl::desc("Specify an n-D virtual vector size for vectorization"), llvm::cl::ZeroOrMore, llvm::cl::MiscFlags::CommaSeparated};
  ::mlir::Pass::ListOption<int64_t> fastestVaryingPattern{*this, "test-fastest-varying", ::llvm::cl::desc("Specify a 1-D, 2-D or 3-D pattern of fastest varying memory dimensions to match. See defaultPatterns in Vectorize.cpp for a description and examples. This is used for testing purposes"), llvm::cl::ZeroOrMore, llvm::cl::MiscFlags::CommaSeparated};
  ::mlir::Pass::Option<bool> vectorizeReductions{*this, "vectorize-reductions", ::llvm::cl::desc("Vectorize known reductions expressed via iter_args. Switched off by default."), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// LoopCoalescing
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class LoopCoalescingBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = LoopCoalescingBase;

  LoopCoalescingBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LoopCoalescingBase(const LoopCoalescingBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-loop-coalescing");
  }
  ::llvm::StringRef getArgument() const override { return "affine-loop-coalescing"; }

  ::llvm::StringRef getDescription() const override { return "Coalesce nested loops with independent bounds into a single loop"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LoopCoalescing");
  }
  ::llvm::StringRef getName() const override { return "LoopCoalescing"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithmeticDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// SimplifyAffineStructures
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SimplifyAffineStructuresBase : public ::mlir::OperationPass<FuncOp> {
public:
  using Base = SimplifyAffineStructuresBase;

  SimplifyAffineStructuresBase() : ::mlir::OperationPass<FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  SimplifyAffineStructuresBase(const SimplifyAffineStructuresBase &other) : ::mlir::OperationPass<FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("affine-simplify-structures");
  }
  ::llvm::StringRef getArgument() const override { return "affine-simplify-structures"; }

  ::llvm::StringRef getDescription() const override { return "Simplify affine expressions in maps/sets and normalize memrefs"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SimplifyAffineStructures");
  }
  ::llvm::StringRef getName() const override { return "SimplifyAffineStructures"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// AffineDataCopyGeneration Registration
//===----------------------------------------------------------------------===//

inline void registerAffineDataCopyGenerationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAffineDataCopyGenerationPass();
  });
}

//===----------------------------------------------------------------------===//
// AffineLoopFusion Registration
//===----------------------------------------------------------------------===//

inline void registerAffineLoopFusionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLoopFusionPass();
  });
}

//===----------------------------------------------------------------------===//
// AffineLoopInvariantCodeMotion Registration
//===----------------------------------------------------------------------===//

inline void registerAffineLoopInvariantCodeMotionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAffineLoopInvariantCodeMotionPass();
  });
}

//===----------------------------------------------------------------------===//
// AffineLoopNormalize Registration
//===----------------------------------------------------------------------===//

inline void registerAffineLoopNormalizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAffineLoopNormalizePass();
  });
}

//===----------------------------------------------------------------------===//
// AffineLoopTiling Registration
//===----------------------------------------------------------------------===//

inline void registerAffineLoopTilingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLoopTilingPass();
  });
}

//===----------------------------------------------------------------------===//
// AffineLoopUnroll Registration
//===----------------------------------------------------------------------===//

inline void registerAffineLoopUnrollPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLoopUnrollPass();
  });
}

//===----------------------------------------------------------------------===//
// AffineLoopUnrollAndJam Registration
//===----------------------------------------------------------------------===//

inline void registerAffineLoopUnrollAndJamPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLoopUnrollAndJamPass();
  });
}

//===----------------------------------------------------------------------===//
// AffineParallelize Registration
//===----------------------------------------------------------------------===//

inline void registerAffineParallelizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAffineParallelizePass();
  });
}

//===----------------------------------------------------------------------===//
// AffinePipelineDataTransfer Registration
//===----------------------------------------------------------------------===//

inline void registerAffinePipelineDataTransferPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createPipelineDataTransferPass();
  });
}

//===----------------------------------------------------------------------===//
// AffineScalarReplacement Registration
//===----------------------------------------------------------------------===//

inline void registerAffineScalarReplacementPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createAffineScalarReplacementPass();
  });
}

//===----------------------------------------------------------------------===//
// AffineVectorize Registration
//===----------------------------------------------------------------------===//

inline void registerAffineVectorizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSuperVectorizePass();
  });
}

//===----------------------------------------------------------------------===//
// LoopCoalescing Registration
//===----------------------------------------------------------------------===//

inline void registerLoopCoalescingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createLoopCoalescingPass();
  });
}

//===----------------------------------------------------------------------===//
// SimplifyAffineStructures Registration
//===----------------------------------------------------------------------===//

inline void registerSimplifyAffineStructuresPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSimplifyAffineStructuresPass();
  });
}

//===----------------------------------------------------------------------===//
// Affine Registration
//===----------------------------------------------------------------------===//

inline void registerAffinePasses() {
  registerAffineDataCopyGenerationPass();
  registerAffineLoopFusionPass();
  registerAffineLoopInvariantCodeMotionPass();
  registerAffineLoopNormalizePass();
  registerAffineLoopTilingPass();
  registerAffineLoopUnrollPass();
  registerAffineLoopUnrollAndJamPass();
  registerAffineParallelizePass();
  registerAffinePipelineDataTransferPass();
  registerAffineScalarReplacementPass();
  registerAffineVectorizePass();
  registerLoopCoalescingPass();
  registerSimplifyAffineStructuresPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
