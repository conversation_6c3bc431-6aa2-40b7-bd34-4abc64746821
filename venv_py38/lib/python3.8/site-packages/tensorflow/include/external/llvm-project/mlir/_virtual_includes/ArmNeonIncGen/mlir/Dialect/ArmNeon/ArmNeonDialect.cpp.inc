/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

DEFINE_EXPLICIT_TYPE_ID(::mlir::arm_neon::ArmNeonDialect)
namespace mlir {
namespace arm_neon {

ArmNeonDialect::~ArmNeonDialect() = default;

} // namespace arm_neon
} // namespace mlir
