
/* Autogenerated by mlir-tblgen; don't manually edit. */

#include "mlir-c/Pass.h"

#ifdef __cplusplus
extern "C" {
#endif

// Registration for the entire group
MLIR_CAPI_EXPORTED void mlirRegisterConversionPasses();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertAffineForToGPU();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertAffineForToGPU();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertAffineToStandard();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertAffineToStandard();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertArithmeticToLLVM();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertArithmeticToLLVM();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertArithmeticToSPIRV();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertArithmeticToSPIRV();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertArmNeon2dToIntr();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertArmNeon2dToIntr();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertAsyncToLLVM();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertAsyncToLLVM();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertBufferizationToMemRef();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertBufferizationToMemRef();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertComplexToLLVM();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertComplexToLLVM();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertComplexToStandard();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertComplexToStandard();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertControlFlowToLLVM();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertControlFlowToLLVM();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertControlFlowToSPIRV();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertControlFlowToSPIRV();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertFuncToLLVM();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertFuncToLLVM();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertFuncToSPIRV();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertFuncToSPIRV();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertGPUToSPIRV();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertGPUToSPIRV();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertGpuLaunchFuncToVulkanLaunchFunc();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertGpuLaunchFuncToVulkanLaunchFunc();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertGpuOpsToNVVMOps();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertGpuOpsToNVVMOps();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertGpuOpsToROCDLOps();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertGpuOpsToROCDLOps();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertLinalgToLLVM();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertLinalgToLLVM();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertLinalgToSPIRV();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertLinalgToSPIRV();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertLinalgToStandard();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertLinalgToStandard();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertMathToLLVM();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertMathToLLVM();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertMathToLibm();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertMathToLibm();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertMathToSPIRV();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertMathToSPIRV();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertMemRefToLLVM();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertMemRefToLLVM();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertMemRefToSPIRV();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertMemRefToSPIRV();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertOpenACCToLLVM();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertOpenACCToLLVM();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertOpenACCToSCF();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertOpenACCToSCF();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertOpenMPToLLVM();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertOpenMPToLLVM();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertPDLToPDLInterp();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertPDLToPDLInterp();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertParallelLoopToGpu();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertParallelLoopToGpu();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertSCFToOpenMP();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertSCFToOpenMP();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertSPIRVToLLVM();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertSPIRVToLLVM();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertShapeConstraints();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertShapeConstraints();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertShapeToStandard();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertShapeToStandard();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertTensorToSPIRV();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertTensorToSPIRV();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertVectorToGPU();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertVectorToGPU();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertVectorToLLVM();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertVectorToLLVM();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertVectorToROCDL();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertVectorToROCDL();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertVectorToSCF();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertVectorToSCF();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertVectorToSPIRV();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertVectorToSPIRV();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionConvertVulkanLaunchFuncToVulkanCalls();
MLIR_CAPI_EXPORTED void mlirRegisterConversionConvertVulkanLaunchFuncToVulkanCalls();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionGpuToLLVMConversionPass();
MLIR_CAPI_EXPORTED void mlirRegisterConversionGpuToLLVMConversionPass();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionLowerHostCodeToLLVM();
MLIR_CAPI_EXPORTED void mlirRegisterConversionLowerHostCodeToLLVM();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionReconcileUnrealizedCasts();
MLIR_CAPI_EXPORTED void mlirRegisterConversionReconcileUnrealizedCasts();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionSCFToControlFlow();
MLIR_CAPI_EXPORTED void mlirRegisterConversionSCFToControlFlow();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionSCFToSPIRV();
MLIR_CAPI_EXPORTED void mlirRegisterConversionSCFToSPIRV();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionTosaToLinalg();
MLIR_CAPI_EXPORTED void mlirRegisterConversionTosaToLinalg();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionTosaToLinalgNamed();
MLIR_CAPI_EXPORTED void mlirRegisterConversionTosaToLinalgNamed();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionTosaToSCF();
MLIR_CAPI_EXPORTED void mlirRegisterConversionTosaToSCF();


/* Create Conversion Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConversionTosaToStandard();
MLIR_CAPI_EXPORTED void mlirRegisterConversionTosaToStandard();



#ifdef __cplusplus
}
#endif
