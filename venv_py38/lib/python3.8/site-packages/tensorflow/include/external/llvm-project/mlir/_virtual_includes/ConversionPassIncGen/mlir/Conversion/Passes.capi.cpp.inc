/* Autogenerated by mlir-tblgen; don't manually edit. */
//===----------------------------------------------------------------------===//
// Conversion Group Registration
//===----------------------------------------------------------------------===//

void mlirRegisterConversionPasses() {
  registerConversionPasses();
}

MlirPass mlirCreateConversionConvertAffineForToGPU() {
  return wrap(mlir::createAffineForToGPUPass().release());
}
void mlirRegisterConversionConvertAffineForToGPU() {
  registerConvertAffineForToGPUPass();
}


MlirPass mlirCreateConversionConvertAffineToStandard() {
  return wrap(mlir::createLowerAffinePass().release());
}
void mlirRegisterConversionConvertAffineToStandard() {
  registerConvertAffineToStandardPass();
}


MlirPass mlirCreateConversionConvertArithmeticToLLVM() {
  return wrap(mlir::arith::createConvertArithmeticToLLVMPass().release());
}
void mlirRegisterConversionConvertArithmeticToLLVM() {
  registerConvertArithmeticToLLVMPass();
}


MlirPass mlirCreateConversionConvertArithmeticToSPIRV() {
  return wrap(mlir::arith::createConvertArithmeticToSPIRVPass().release());
}
void mlirRegisterConversionConvertArithmeticToSPIRV() {
  registerConvertArithmeticToSPIRVPass();
}


MlirPass mlirCreateConversionConvertArmNeon2dToIntr() {
  return wrap(mlir::createConvertArmNeon2dToIntrPass().release());
}
void mlirRegisterConversionConvertArmNeon2dToIntr() {
  registerConvertArmNeon2dToIntrPass();
}


MlirPass mlirCreateConversionConvertAsyncToLLVM() {
  return wrap(mlir::createConvertAsyncToLLVMPass().release());
}
void mlirRegisterConversionConvertAsyncToLLVM() {
  registerConvertAsyncToLLVMPass();
}


MlirPass mlirCreateConversionConvertBufferizationToMemRef() {
  return wrap(mlir::createBufferizationToMemRefPass().release());
}
void mlirRegisterConversionConvertBufferizationToMemRef() {
  registerConvertBufferizationToMemRefPass();
}


MlirPass mlirCreateConversionConvertComplexToLLVM() {
  return wrap(mlir::createConvertComplexToLLVMPass().release());
}
void mlirRegisterConversionConvertComplexToLLVM() {
  registerConvertComplexToLLVMPass();
}


MlirPass mlirCreateConversionConvertComplexToStandard() {
  return wrap(mlir::createConvertComplexToStandardPass().release());
}
void mlirRegisterConversionConvertComplexToStandard() {
  registerConvertComplexToStandardPass();
}


MlirPass mlirCreateConversionConvertControlFlowToLLVM() {
  return wrap(mlir::cf::createConvertControlFlowToLLVMPass().release());
}
void mlirRegisterConversionConvertControlFlowToLLVM() {
  registerConvertControlFlowToLLVMPass();
}


MlirPass mlirCreateConversionConvertControlFlowToSPIRV() {
  return wrap(mlir::createConvertControlFlowToSPIRVPass().release());
}
void mlirRegisterConversionConvertControlFlowToSPIRV() {
  registerConvertControlFlowToSPIRVPass();
}


MlirPass mlirCreateConversionConvertFuncToLLVM() {
  return wrap(mlir::createConvertFuncToLLVMPass().release());
}
void mlirRegisterConversionConvertFuncToLLVM() {
  registerConvertFuncToLLVMPass();
}


MlirPass mlirCreateConversionConvertFuncToSPIRV() {
  return wrap(mlir::createConvertFuncToSPIRVPass().release());
}
void mlirRegisterConversionConvertFuncToSPIRV() {
  registerConvertFuncToSPIRVPass();
}


MlirPass mlirCreateConversionConvertGPUToSPIRV() {
  return wrap(mlir::createConvertGPUToSPIRVPass().release());
}
void mlirRegisterConversionConvertGPUToSPIRV() {
  registerConvertGPUToSPIRVPass();
}


MlirPass mlirCreateConversionConvertGpuLaunchFuncToVulkanLaunchFunc() {
  return wrap(mlir::createConvertGpuLaunchFuncToVulkanLaunchFuncPass().release());
}
void mlirRegisterConversionConvertGpuLaunchFuncToVulkanLaunchFunc() {
  registerConvertGpuLaunchFuncToVulkanLaunchFuncPass();
}


MlirPass mlirCreateConversionConvertGpuOpsToNVVMOps() {
  return wrap(mlir::createLowerGpuOpsToNVVMOpsPass().release());
}
void mlirRegisterConversionConvertGpuOpsToNVVMOps() {
  registerConvertGpuOpsToNVVMOpsPass();
}


MlirPass mlirCreateConversionConvertGpuOpsToROCDLOps() {
  return wrap(mlir::createLowerGpuOpsToROCDLOpsPass().release());
}
void mlirRegisterConversionConvertGpuOpsToROCDLOps() {
  registerConvertGpuOpsToROCDLOpsPass();
}


MlirPass mlirCreateConversionConvertLinalgToLLVM() {
  return wrap(mlir::createConvertLinalgToLLVMPass().release());
}
void mlirRegisterConversionConvertLinalgToLLVM() {
  registerConvertLinalgToLLVMPass();
}


MlirPass mlirCreateConversionConvertLinalgToSPIRV() {
  return wrap(mlir::createLinalgToSPIRVPass().release());
}
void mlirRegisterConversionConvertLinalgToSPIRV() {
  registerConvertLinalgToSPIRVPass();
}


MlirPass mlirCreateConversionConvertLinalgToStandard() {
  return wrap(mlir::createConvertLinalgToStandardPass().release());
}
void mlirRegisterConversionConvertLinalgToStandard() {
  registerConvertLinalgToStandardPass();
}


MlirPass mlirCreateConversionConvertMathToLLVM() {
  return wrap(mlir::createConvertMathToLLVMPass().release());
}
void mlirRegisterConversionConvertMathToLLVM() {
  registerConvertMathToLLVMPass();
}


MlirPass mlirCreateConversionConvertMathToLibm() {
  return wrap(mlir::createConvertMathToLibmPass().release());
}
void mlirRegisterConversionConvertMathToLibm() {
  registerConvertMathToLibmPass();
}


MlirPass mlirCreateConversionConvertMathToSPIRV() {
  return wrap(mlir::createConvertMathToSPIRVPass().release());
}
void mlirRegisterConversionConvertMathToSPIRV() {
  registerConvertMathToSPIRVPass();
}


MlirPass mlirCreateConversionConvertMemRefToLLVM() {
  return wrap(mlir::createMemRefToLLVMPass().release());
}
void mlirRegisterConversionConvertMemRefToLLVM() {
  registerConvertMemRefToLLVMPass();
}


MlirPass mlirCreateConversionConvertMemRefToSPIRV() {
  return wrap(mlir::createConvertMemRefToSPIRVPass().release());
}
void mlirRegisterConversionConvertMemRefToSPIRV() {
  registerConvertMemRefToSPIRVPass();
}


MlirPass mlirCreateConversionConvertOpenACCToLLVM() {
  return wrap(mlir::createConvertOpenACCToLLVMPass().release());
}
void mlirRegisterConversionConvertOpenACCToLLVM() {
  registerConvertOpenACCToLLVMPass();
}


MlirPass mlirCreateConversionConvertOpenACCToSCF() {
  return wrap(mlir::createConvertOpenACCToSCFPass().release());
}
void mlirRegisterConversionConvertOpenACCToSCF() {
  registerConvertOpenACCToSCFPass();
}


MlirPass mlirCreateConversionConvertOpenMPToLLVM() {
  return wrap(mlir::createConvertOpenMPToLLVMPass().release());
}
void mlirRegisterConversionConvertOpenMPToLLVM() {
  registerConvertOpenMPToLLVMPass();
}


MlirPass mlirCreateConversionConvertPDLToPDLInterp() {
  return wrap(mlir::createPDLToPDLInterpPass().release());
}
void mlirRegisterConversionConvertPDLToPDLInterp() {
  registerConvertPDLToPDLInterpPass();
}


MlirPass mlirCreateConversionConvertParallelLoopToGpu() {
  return wrap(mlir::createParallelLoopToGpuPass().release());
}
void mlirRegisterConversionConvertParallelLoopToGpu() {
  registerConvertParallelLoopToGpuPass();
}


MlirPass mlirCreateConversionConvertSCFToOpenMP() {
  return wrap(mlir::createConvertSCFToOpenMPPass().release());
}
void mlirRegisterConversionConvertSCFToOpenMP() {
  registerConvertSCFToOpenMPPass();
}


MlirPass mlirCreateConversionConvertSPIRVToLLVM() {
  return wrap(mlir::createConvertSPIRVToLLVMPass().release());
}
void mlirRegisterConversionConvertSPIRVToLLVM() {
  registerConvertSPIRVToLLVMPass();
}


MlirPass mlirCreateConversionConvertShapeConstraints() {
  return wrap(mlir::createConvertShapeConstraintsPass().release());
}
void mlirRegisterConversionConvertShapeConstraints() {
  registerConvertShapeConstraintsPass();
}


MlirPass mlirCreateConversionConvertShapeToStandard() {
  return wrap(mlir::createConvertShapeToStandardPass().release());
}
void mlirRegisterConversionConvertShapeToStandard() {
  registerConvertShapeToStandardPass();
}


MlirPass mlirCreateConversionConvertTensorToSPIRV() {
  return wrap(mlir::createConvertTensorToSPIRVPass().release());
}
void mlirRegisterConversionConvertTensorToSPIRV() {
  registerConvertTensorToSPIRVPass();
}


MlirPass mlirCreateConversionConvertVectorToGPU() {
  return wrap(mlir::createConvertVectorToGPUPass().release());
}
void mlirRegisterConversionConvertVectorToGPU() {
  registerConvertVectorToGPUPass();
}


MlirPass mlirCreateConversionConvertVectorToLLVM() {
  return wrap(mlir::createConvertVectorToLLVMPass().release());
}
void mlirRegisterConversionConvertVectorToLLVM() {
  registerConvertVectorToLLVMPass();
}


MlirPass mlirCreateConversionConvertVectorToROCDL() {
  return wrap(mlir::createConvertVectorToROCDLPass().release());
}
void mlirRegisterConversionConvertVectorToROCDL() {
  registerConvertVectorToROCDLPass();
}


MlirPass mlirCreateConversionConvertVectorToSCF() {
  return wrap(mlir::createConvertVectorToSCFPass().release());
}
void mlirRegisterConversionConvertVectorToSCF() {
  registerConvertVectorToSCFPass();
}


MlirPass mlirCreateConversionConvertVectorToSPIRV() {
  return wrap(mlir::createConvertVectorToSPIRVPass().release());
}
void mlirRegisterConversionConvertVectorToSPIRV() {
  registerConvertVectorToSPIRVPass();
}


MlirPass mlirCreateConversionConvertVulkanLaunchFuncToVulkanCalls() {
  return wrap(mlir::createConvertVulkanLaunchFuncToVulkanCallsPass().release());
}
void mlirRegisterConversionConvertVulkanLaunchFuncToVulkanCalls() {
  registerConvertVulkanLaunchFuncToVulkanCallsPass();
}


MlirPass mlirCreateConversionGpuToLLVMConversionPass() {
  return wrap(mlir::createGpuToLLVMConversionPass().release());
}
void mlirRegisterConversionGpuToLLVMConversionPass() {
  registerGpuToLLVMConversionPassPass();
}


MlirPass mlirCreateConversionLowerHostCodeToLLVM() {
  return wrap(mlir::createLowerHostCodeToLLVMPass().release());
}
void mlirRegisterConversionLowerHostCodeToLLVM() {
  registerLowerHostCodeToLLVMPass();
}


MlirPass mlirCreateConversionReconcileUnrealizedCasts() {
  return wrap(mlir::createReconcileUnrealizedCastsPass().release());
}
void mlirRegisterConversionReconcileUnrealizedCasts() {
  registerReconcileUnrealizedCastsPass();
}


MlirPass mlirCreateConversionSCFToControlFlow() {
  return wrap(mlir::createConvertSCFToCFPass().release());
}
void mlirRegisterConversionSCFToControlFlow() {
  registerSCFToControlFlowPass();
}


MlirPass mlirCreateConversionSCFToSPIRV() {
  return wrap(mlir::createConvertSCFToSPIRVPass().release());
}
void mlirRegisterConversionSCFToSPIRV() {
  registerSCFToSPIRVPass();
}


MlirPass mlirCreateConversionTosaToLinalg() {
  return wrap(tosa::createTosaToLinalg().release());
}
void mlirRegisterConversionTosaToLinalg() {
  registerTosaToLinalgPass();
}


MlirPass mlirCreateConversionTosaToLinalgNamed() {
  return wrap(tosa::createTosaToLinalgNamed().release());
}
void mlirRegisterConversionTosaToLinalgNamed() {
  registerTosaToLinalgNamedPass();
}


MlirPass mlirCreateConversionTosaToSCF() {
  return wrap(tosa::createTosaToSCF().release());
}
void mlirRegisterConversionTosaToSCF() {
  registerTosaToSCFPass();
}


MlirPass mlirCreateConversionTosaToStandard() {
  return wrap(tosa::createTosaToStandard().release());
}
void mlirRegisterConversionTosaToStandard() {
  registerTosaToStandardPass();
}

