/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Interface Definitions                                                      *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

unsigned mlir::LLVM::PointerElementTypeInterface::getSizeInBytes(const DataLayout & dataLayout) const {
      return getImpl()->getSizeInBytes(getImpl(), *this, dataLayout);
  }
