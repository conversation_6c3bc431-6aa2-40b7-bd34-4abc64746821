/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

DEFINE_EXPLICIT_TYPE_ID(::mlir::cf::ControlFlowDialect)
namespace mlir {
namespace cf {

ControlFlowDialect::~ControlFlowDialect() = default;

} // namespace cf
} // namespace mlir
