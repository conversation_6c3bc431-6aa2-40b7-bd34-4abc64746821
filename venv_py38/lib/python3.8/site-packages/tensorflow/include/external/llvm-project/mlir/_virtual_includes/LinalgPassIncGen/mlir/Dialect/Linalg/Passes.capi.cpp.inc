/* Autogenerated by mlir-tblgen; don't manually edit. */
//===----------------------------------------------------------------------===//
// Linalg Group Registration
//===----------------------------------------------------------------------===//

void mlirRegisterLinalgPasses() {
  registerLinalgPasses();
}

MlirPass mlirCreateLinalgConvertElementwiseToLinalg() {
  return wrap(mlir::createConvertElementwiseToLinalgPass().release());
}
void mlirRegisterLinalgConvertElementwiseToLinalg() {
  registerConvertElementwiseToLinalgPass();
}


MlirPass mlirCreateLinalgLinalgBufferize() {
  return wrap(mlir::createLinalgBufferizePass().release());
}
void mlirRegisterLinalgLinalgBufferize() {
  registerLinalgBufferizePass();
}


MlirPass mlirCreateLinalgLinalgComprehensiveModuleBufferize() {
  return wrap(mlir::createLinalgComprehensiveModuleBufferizePass().release());
}
void mlirRegisterLinalgLinalgComprehensiveModuleBufferize() {
  registerLinalgComprehensiveModuleBufferizePass();
}


MlirPass mlirCreateLinalgLinalgDetensorize() {
  return wrap(mlir::createLinalgDetensorizePass().release());
}
void mlirRegisterLinalgLinalgDetensorize() {
  registerLinalgDetensorizePass();
}


MlirPass mlirCreateLinalgLinalgElementwiseOpFusion() {
  return wrap(mlir::createLinalgElementwiseOpFusionPass().release());
}
void mlirRegisterLinalgLinalgElementwiseOpFusion() {
  registerLinalgElementwiseOpFusionPass();
}


MlirPass mlirCreateLinalgLinalgFoldReshapeOpsByLinearization() {
  return wrap(mlir::createFoldReshapeOpsByLinearizationPass().release());
}
void mlirRegisterLinalgLinalgFoldReshapeOpsByLinearization() {
  registerLinalgFoldReshapeOpsByLinearizationPass();
}


MlirPass mlirCreateLinalgLinalgFoldUnitExtentDims() {
  return wrap(mlir::createLinalgFoldUnitExtentDimsPass().release());
}
void mlirRegisterLinalgLinalgFoldUnitExtentDims() {
  registerLinalgFoldUnitExtentDimsPass();
}


MlirPass mlirCreateLinalgLinalgGeneralization() {
  return wrap(mlir::createLinalgGeneralizationPass().release());
}
void mlirRegisterLinalgLinalgGeneralization() {
  registerLinalgGeneralizationPass();
}


MlirPass mlirCreateLinalgLinalgInlineScalarOperands() {
  return wrap(mlir::createLinalgInlineScalarOperandsPass().release());
}
void mlirRegisterLinalgLinalgInlineScalarOperands() {
  registerLinalgInlineScalarOperandsPass();
}


MlirPass mlirCreateLinalgLinalgLowerToAffineLoops() {
  return wrap(mlir::createConvertLinalgToAffineLoopsPass().release());
}
void mlirRegisterLinalgLinalgLowerToAffineLoops() {
  registerLinalgLowerToAffineLoopsPass();
}


MlirPass mlirCreateLinalgLinalgLowerToLoops() {
  return wrap(mlir::createConvertLinalgToLoopsPass().release());
}
void mlirRegisterLinalgLinalgLowerToLoops() {
  registerLinalgLowerToLoopsPass();
}


MlirPass mlirCreateLinalgLinalgLowerToParallelLoops() {
  return wrap(mlir::createConvertLinalgToParallelLoopsPass().release());
}
void mlirRegisterLinalgLinalgLowerToParallelLoops() {
  registerLinalgLowerToParallelLoopsPass();
}


MlirPass mlirCreateLinalgLinalgNamedOpConversion() {
  return wrap(mlir::createLinalgNamedOpConversionPass().release());
}
void mlirRegisterLinalgLinalgNamedOpConversion() {
  registerLinalgNamedOpConversionPass();
}


MlirPass mlirCreateLinalgLinalgPromotion() {
  return wrap(mlir::createLinalgPromotionPass().release());
}
void mlirRegisterLinalgLinalgPromotion() {
  registerLinalgPromotionPass();
}


MlirPass mlirCreateLinalgLinalgStrategyDecomposePass() {
  return wrap(mlir::createLinalgStrategyDecomposePass().release());
}
void mlirRegisterLinalgLinalgStrategyDecomposePass() {
  registerLinalgStrategyDecomposePassPass();
}


MlirPass mlirCreateLinalgLinalgStrategyEnablePass() {
  return wrap(mlir::createLinalgStrategyEnablePass().release());
}
void mlirRegisterLinalgLinalgStrategyEnablePass() {
  registerLinalgStrategyEnablePassPass();
}


MlirPass mlirCreateLinalgLinalgStrategyGeneralizePass() {
  return wrap(mlir::createLinalgStrategyGeneralizePass().release());
}
void mlirRegisterLinalgLinalgStrategyGeneralizePass() {
  registerLinalgStrategyGeneralizePassPass();
}


MlirPass mlirCreateLinalgLinalgStrategyInterchangePass() {
  return wrap(mlir::createLinalgStrategyInterchangePass().release());
}
void mlirRegisterLinalgLinalgStrategyInterchangePass() {
  registerLinalgStrategyInterchangePassPass();
}


MlirPass mlirCreateLinalgLinalgStrategyLowerVectorsPass() {
  return wrap(mlir::createLinalgStrategyLowerVectorsPass().release());
}
void mlirRegisterLinalgLinalgStrategyLowerVectorsPass() {
  registerLinalgStrategyLowerVectorsPassPass();
}


MlirPass mlirCreateLinalgLinalgStrategyPadPass() {
  return wrap(mlir::createLinalgStrategyPadPass().release());
}
void mlirRegisterLinalgLinalgStrategyPadPass() {
  registerLinalgStrategyPadPassPass();
}


MlirPass mlirCreateLinalgLinalgStrategyPromotePass() {
  return wrap(mlir::createLinalgStrategyPromotePass().release());
}
void mlirRegisterLinalgLinalgStrategyPromotePass() {
  registerLinalgStrategyPromotePassPass();
}


MlirPass mlirCreateLinalgLinalgStrategyRemoveMarkersPass() {
  return wrap(mlir::createLinalgStrategyRemoveMarkersPass().release());
}
void mlirRegisterLinalgLinalgStrategyRemoveMarkersPass() {
  registerLinalgStrategyRemoveMarkersPassPass();
}


MlirPass mlirCreateLinalgLinalgStrategyTileAndFusePass() {
  return wrap(mlir::createLinalgStrategyTileAndFusePass().release());
}
void mlirRegisterLinalgLinalgStrategyTileAndFusePass() {
  registerLinalgStrategyTileAndFusePassPass();
}


MlirPass mlirCreateLinalgLinalgStrategyTilePass() {
  return wrap(mlir::createLinalgStrategyTilePass().release());
}
void mlirRegisterLinalgLinalgStrategyTilePass() {
  registerLinalgStrategyTilePassPass();
}


MlirPass mlirCreateLinalgLinalgStrategyVectorizePass() {
  return wrap(mlir::createLinalgStrategyVectorizePass().release());
}
void mlirRegisterLinalgLinalgStrategyVectorizePass() {
  registerLinalgStrategyVectorizePassPass();
}


MlirPass mlirCreateLinalgLinalgTiling() {
  return wrap(mlir::createLinalgTilingPass().release());
}
void mlirRegisterLinalgLinalgTiling() {
  registerLinalgTilingPass();
}

