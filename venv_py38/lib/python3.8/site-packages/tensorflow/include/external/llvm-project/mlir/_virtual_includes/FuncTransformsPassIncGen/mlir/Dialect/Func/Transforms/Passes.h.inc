/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// FuncBufferize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class FuncBufferizeBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FuncBufferizeBase;

  FuncBufferizeBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FuncBufferizeBase(const FuncBufferizeBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("func-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "func-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize func/call/return ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FuncBufferize");
  }
  ::llvm::StringRef getName() const override { return "FuncBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<memref::MemRefDialect>();

  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// FuncBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerFuncBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::func::createFuncBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// Func Registration
//===----------------------------------------------------------------------===//

inline void registerFuncPasses() {
  registerFuncBufferizePass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
