/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

DEFINE_EXPLICIT_TYPE_ID(::mlir::spirv::SPIRVDialect)
namespace mlir {
namespace spirv {

SPIRVDialect::~SPIRVDialect() = default;

} // namespace spirv
} // namespace mlir
