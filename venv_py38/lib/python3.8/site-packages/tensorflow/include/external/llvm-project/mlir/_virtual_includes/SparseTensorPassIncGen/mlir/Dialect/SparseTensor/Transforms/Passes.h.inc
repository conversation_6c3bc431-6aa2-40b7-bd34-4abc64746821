/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// SparseTensorConversion
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SparseTensorConversionBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SparseTensorConversionBase;

  SparseTensorConversionBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SparseTensorConversionBase(const SparseTensorConversionBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sparse-tensor-conversion");
  }
  ::llvm::StringRef getArgument() const override { return "sparse-tensor-conversion"; }

  ::llvm::StringRef getDescription() const override { return "Apply conversion rules to sparse tensor primitives and types"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SparseTensorConversion");
  }
  ::llvm::StringRef getName() const override { return "SparseTensorConversion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<arith::ArithmeticDialect>();

  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<LLVM::LLVMDialect>();

  registry.insert<linalg::LinalgDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  registry.insert<vector::VectorDialect>();

  }

protected:
  ::mlir::Pass::Option<int32_t> sparseToSparse{*this, "s2s-strategy", ::llvm::cl::desc("Set the strategy for sparse-to-sparse conversion"), ::llvm::cl::init(0)};
};

//===----------------------------------------------------------------------===//
// Sparsification
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class SparsificationBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = SparsificationBase;

  SparsificationBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  SparsificationBase(const SparsificationBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sparsification");
  }
  ::llvm::StringRef getArgument() const override { return "sparsification"; }

  ::llvm::StringRef getDescription() const override { return "Automatically generate sparse tensor code from sparse tensor types"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Sparsification");
  }
  ::llvm::StringRef getName() const override { return "Sparsification"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<AffineDialect>();

  registry.insert<arith::ArithmeticDialect>();

  registry.insert<bufferization::BufferizationDialect>();

  registry.insert<LLVM::LLVMDialect>();

  registry.insert<memref::MemRefDialect>();

  registry.insert<scf::SCFDialect>();

  registry.insert<sparse_tensor::SparseTensorDialect>();

  registry.insert<vector::VectorDialect>();

  }

protected:
  ::mlir::Pass::Option<int32_t> parallelization{*this, "parallelization-strategy", ::llvm::cl::desc("Set the parallelization strategy"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<int32_t> vectorization{*this, "vectorization-strategy", ::llvm::cl::desc("Set the vectorization strategy"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<int32_t> vectorLength{*this, "vl", ::llvm::cl::desc("Set the vector length"), ::llvm::cl::init(1)};
  ::mlir::Pass::Option<bool> enableSIMDIndex32{*this, "enable-simd-index32", ::llvm::cl::desc("Enable i32 indexing into vectors (for efficiency)"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> enableVLAVectorization{*this, "enable-vla-vectorization", ::llvm::cl::desc("Enable vector length agnostic vectorization"), ::llvm::cl::init(false)};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// SparseTensorConversion Registration
//===----------------------------------------------------------------------===//

inline void registerSparseTensorConversionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSparseTensorConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// Sparsification Registration
//===----------------------------------------------------------------------===//

inline void registerSparsificationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSparsificationPass();
  });
}

//===----------------------------------------------------------------------===//
// SparseTensor Registration
//===----------------------------------------------------------------------===//

inline void registerSparseTensorPasses() {
  registerSparseTensorConversionPass();
  registerSparsificationPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
