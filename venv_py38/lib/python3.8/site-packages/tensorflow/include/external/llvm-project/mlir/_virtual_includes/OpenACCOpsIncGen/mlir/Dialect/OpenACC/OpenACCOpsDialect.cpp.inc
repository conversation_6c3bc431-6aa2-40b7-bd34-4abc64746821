/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

DEFINE_EXPLICIT_TYPE_ID(::mlir::acc::OpenACCDialect)
namespace mlir {
namespace acc {

OpenACCDialect::~OpenACCDialect() = default;

} // namespace acc
} // namespace mlir
