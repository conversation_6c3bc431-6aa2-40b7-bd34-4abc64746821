/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// BufferDeallocation
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class BufferDeallocationBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = BufferDeallocationBase;

  BufferDeallocationBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  BufferDeallocationBase(const BufferDeallocationBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("buffer-deallocation");
  }
  ::llvm::StringRef getArgument() const override { return "buffer-deallocation"; }

  ::llvm::StringRef getDescription() const override { return "Adds all required dealloc operations for all allocations in the input program"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferDeallocation");
  }
  ::llvm::StringRef getName() const override { return "BufferDeallocation"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// BufferHoisting
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class BufferHoistingBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = BufferHoistingBase;

  BufferHoistingBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  BufferHoistingBase(const BufferHoistingBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("buffer-hoisting");
  }
  ::llvm::StringRef getArgument() const override { return "buffer-hoisting"; }

  ::llvm::StringRef getDescription() const override { return "Optimizes placement of allocation operations by moving them into common dominators and out of nested regions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferHoisting");
  }
  ::llvm::StringRef getName() const override { return "BufferHoisting"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// BufferLoopHoisting
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class BufferLoopHoistingBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = BufferLoopHoistingBase;

  BufferLoopHoistingBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  BufferLoopHoistingBase(const BufferLoopHoistingBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("buffer-loop-hoisting");
  }
  ::llvm::StringRef getArgument() const override { return "buffer-loop-hoisting"; }

  ::llvm::StringRef getDescription() const override { return "Optimizes placement of allocation operations by moving them out of loop nests"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferLoopHoisting");
  }
  ::llvm::StringRef getName() const override { return "BufferLoopHoisting"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// BufferResultsToOutParams
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class BufferResultsToOutParamsBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = BufferResultsToOutParamsBase;

  BufferResultsToOutParamsBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  BufferResultsToOutParamsBase(const BufferResultsToOutParamsBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("buffer-results-to-out-params");
  }
  ::llvm::StringRef getArgument() const override { return "buffer-results-to-out-params"; }

  ::llvm::StringRef getDescription() const override { return "Converts memref-typed function results to out-params"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferResultsToOutParams");
  }
  ::llvm::StringRef getName() const override { return "BufferResultsToOutParams"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// FinalizingBufferize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class FinalizingBufferizeBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = FinalizingBufferizeBase;

  FinalizingBufferizeBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  FinalizingBufferizeBase(const FinalizingBufferizeBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("finalizing-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "finalizing-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Finalize a partial bufferization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FinalizingBufferize");
  }
  ::llvm::StringRef getName() const override { return "FinalizingBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// OneShotBufferize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class OneShotBufferizeBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = OneShotBufferizeBase;

  OneShotBufferizeBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  OneShotBufferizeBase(const OneShotBufferizeBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("one-shot-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "one-shot-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "One-Shot Bufferize"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OneShotBufferize");
  }
  ::llvm::StringRef getName() const override { return "OneShotBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<bool> allowReturnAllocs{*this, "allow-return-allocs", ::llvm::cl::desc("Allows returning/yielding new allocations from a block."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> allowUnknownOps{*this, "allow-unknown-ops", ::llvm::cl::desc("Allows unknown (not bufferizable) ops in the input IR."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<unsigned> analysisFuzzerSeed{*this, "analysis-fuzzer-seed", ::llvm::cl::desc("Test only: Analyze ops in random order with a given seed (fuzzer)"), ::llvm::cl::init(0)};
  ::mlir::Pass::Option<bool> createDeallocs{*this, "create-deallocs", ::llvm::cl::desc("Specify if buffers should be deallocated. For compatibility with core bufferization passes."), ::llvm::cl::init(true)};
  ::mlir::Pass::ListOption<std::string> dialectFilter{*this, "dialect-filter", ::llvm::cl::desc("Restrict bufferization to ops from these dialects."), llvm::cl::MiscFlags::CommaSeparated};
  ::mlir::Pass::Option<bool> fullyDynamicLayoutMaps{*this, "fully-dynamic-layout-maps", ::llvm::cl::desc("Generate MemRef types with dynamic offset+strides by default."), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> testAnalysisOnly{*this, "test-analysis-only", ::llvm::cl::desc("Test only: Only run inplaceability analysis and annotate IR"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> printConflicts{*this, "print-conflicts", ::llvm::cl::desc("Test only: Annotate IR with RaW conflicts. Requires test-analysis-only."), ::llvm::cl::init(false)};
};

//===----------------------------------------------------------------------===//
// PromoteBuffersToStack
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class PromoteBuffersToStackBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = PromoteBuffersToStackBase;

  PromoteBuffersToStackBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  PromoteBuffersToStackBase(const PromoteBuffersToStackBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("promote-buffers-to-stack");
  }
  ::llvm::StringRef getArgument() const override { return "promote-buffers-to-stack"; }

  ::llvm::StringRef getDescription() const override { return "Promotes heap-based allocations to automatically managed stack-based allocations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PromoteBuffersToStack");
  }
  ::llvm::StringRef getName() const override { return "PromoteBuffersToStack"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
  ::mlir::Pass::Option<unsigned> maxAllocSizeInBytes{*this, "max-alloc-size-in-bytes", ::llvm::cl::desc("Maximal size in bytes to promote allocations to stack."), ::llvm::cl::init(1024)};
  ::mlir::Pass::Option<unsigned> maxRankOfAllocatedMemRef{*this, "max-rank-of-allocated-memref", ::llvm::cl::desc("Maximal memref rank to promote dynamic buffers."), ::llvm::cl::init(1)};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// BufferDeallocation Registration
//===----------------------------------------------------------------------===//

inline void registerBufferDeallocationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createBufferDeallocationPass();
  });
}

//===----------------------------------------------------------------------===//
// BufferHoisting Registration
//===----------------------------------------------------------------------===//

inline void registerBufferHoistingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createBufferHoistingPass();
  });
}

//===----------------------------------------------------------------------===//
// BufferLoopHoisting Registration
//===----------------------------------------------------------------------===//

inline void registerBufferLoopHoistingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createBufferLoopHoistingPass();
  });
}

//===----------------------------------------------------------------------===//
// BufferResultsToOutParams Registration
//===----------------------------------------------------------------------===//

inline void registerBufferResultsToOutParamsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createBufferResultsToOutParamsPass();
  });
}

//===----------------------------------------------------------------------===//
// FinalizingBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerFinalizingBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createFinalizingBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// OneShotBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerOneShotBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createOneShotBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// PromoteBuffersToStack Registration
//===----------------------------------------------------------------------===//

inline void registerPromoteBuffersToStackPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::bufferization::createPromoteBuffersToStackPass();
  });
}

//===----------------------------------------------------------------------===//
// Bufferization Registration
//===----------------------------------------------------------------------===//

inline void registerBufferizationPasses() {
  registerBufferDeallocationPass();
  registerBufferHoistingPass();
  registerBufferLoopHoistingPass();
  registerBufferResultsToOutParamsPass();
  registerFinalizingBufferizePass();
  registerOneShotBufferizePass();
  registerPromoteBuffersToStackPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
