
/* Autogenerated by mlir-tblgen; don't manually edit. */

#include "mlir-c/Pass.h"

#ifdef __cplusplus
extern "C" {
#endif

// Registration for the entire group
MLIR_CAPI_EXPORTED void mlirRegisterTransformsPasses();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsCSE();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsCSE();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsCanonicalizer();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsCanonicalizer();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsControlFlowSink();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsControlFlowSink();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsInliner();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsInliner();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsLocationSnapshot();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsLocationSnapshot();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsLoopInvariantCodeMotion();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsLoopInvariantCodeMotion();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsPrintOpStats();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsPrintOpStats();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsSCCP();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsSCCP();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsStripDebugInfo();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsStripDebugInfo();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsSymbolDCE();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsSymbolDCE();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsSymbolPrivatize();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsSymbolPrivatize();


/* Create Transforms Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateTransformsViewOpGraph();
MLIR_CAPI_EXPORTED void mlirRegisterTransformsViewOpGraph();



#ifdef __cplusplus
}
#endif
