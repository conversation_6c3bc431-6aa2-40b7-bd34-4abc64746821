/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

DEFINE_EXPLICIT_TYPE_ID(::mlir::pdl::PDLDialect)
namespace mlir {
namespace pdl {

PDLDialect::~PDLDialect() = default;

} // namespace pdl
} // namespace mlir
