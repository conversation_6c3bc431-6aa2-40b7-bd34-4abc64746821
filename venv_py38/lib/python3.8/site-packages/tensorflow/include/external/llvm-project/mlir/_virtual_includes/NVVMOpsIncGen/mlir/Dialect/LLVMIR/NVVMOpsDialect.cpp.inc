/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

DEFINE_EXPLICIT_TYPE_ID(::mlir::NVVM::NVVMDialect)
namespace mlir {
namespace NVVM {

NVVMDialect::~NVVMDialect() = default;

} // namespace NVVM
} // namespace mlir
