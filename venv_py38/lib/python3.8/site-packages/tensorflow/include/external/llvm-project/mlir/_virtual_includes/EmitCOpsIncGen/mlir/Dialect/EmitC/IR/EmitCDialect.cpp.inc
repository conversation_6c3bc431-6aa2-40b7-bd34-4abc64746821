/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

DEFINE_EXPLICIT_TYPE_ID(::mlir::emitc::EmitCDialect)
namespace mlir {
namespace emitc {

EmitCDialect::~EmitCDialect() = default;

} // namespace emitc
} // namespace mlir
