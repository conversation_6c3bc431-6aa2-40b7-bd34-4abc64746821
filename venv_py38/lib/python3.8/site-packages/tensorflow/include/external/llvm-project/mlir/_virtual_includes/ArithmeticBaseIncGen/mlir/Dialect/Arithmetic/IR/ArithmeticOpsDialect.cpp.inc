/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

DEFINE_EXPLICIT_TYPE_ID(::mlir::arith::ArithmeticDialect)
namespace mlir {
namespace arith {

ArithmeticDialect::~ArithmeticDialect() = default;

} // namespace arith
} // namespace mlir
