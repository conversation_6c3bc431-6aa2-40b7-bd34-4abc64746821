/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

DEFINE_EXPLICIT_TYPE_ID(::mlir::pdl_interp::PDLInterpDialect)
namespace mlir {
namespace pdl_interp {

PDLInterpDialect::~PDLInterpDialect() = default;

} // namespace pdl_interp
} // namespace mlir
