/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

DEFINE_EXPLICIT_TYPE_ID(::mlir::func::FuncDialect)
namespace mlir {
namespace func {

FuncDialect::~FuncDialect() = default;

} // namespace func
} // namespace mlir
