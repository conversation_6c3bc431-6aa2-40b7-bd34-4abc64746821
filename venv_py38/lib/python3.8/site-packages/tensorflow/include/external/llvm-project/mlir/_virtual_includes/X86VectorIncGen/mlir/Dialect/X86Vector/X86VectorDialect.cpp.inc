/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

DEFINE_EXPLICIT_TYPE_ID(::mlir::x86vector::X86VectorDialect)
namespace mlir {
namespace x86vector {

X86VectorDialect::~X86VectorDialect() = default;

} // namespace x86vector
} // namespace mlir
