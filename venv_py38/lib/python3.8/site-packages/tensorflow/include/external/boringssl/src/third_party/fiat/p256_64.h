/* Autogenerated */
/* curve description: p256 */
/* requested operations: (all) */
/* m = 0xffffffff00000001000000000000000000000000ffffffffffffffffffffffff (from "2^256 - 2^224 + 2^192 + 2^96 - 1") */
/* machine_wordsize = 64 (from "64") */
/*                                                                    */
/* NOTE: In addition to the bounds specified above each function, all */
/*   functions synthesized for this Montgomery arithmetic require the */
/*   input to be strictly less than the prime modulus (m), and also   */
/*   require the input to be in the unique saturated representation.  */
/*   All functions also ensure that these two properties are true of  */
/*   return values.                                                   */

#include <stdint.h>
typedef unsigned char fiat_p256_uint1;
typedef signed char fiat_p256_int1;
typedef signed __int128 fiat_p256_int128;
typedef unsigned __int128 fiat_p256_uint128;


/*
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xffffffffffffffff]
 *   arg3: [0x0 ~> 0xffffffffffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffffffffffffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_p256_addcarryx_u64(uint64_t* out1, fiat_p256_uint1* out2, fiat_p256_uint1 arg1, uint64_t arg2, uint64_t arg3) {
  fiat_p256_uint128 x1 = ((arg1 + (fiat_p256_uint128)arg2) + arg3);
  uint64_t x2 = (uint64_t)(x1 & UINT64_C(0xffffffffffffffff));
  fiat_p256_uint1 x3 = (fiat_p256_uint1)(x1 >> 64);
  *out1 = x2;
  *out2 = x3;
}

/*
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xffffffffffffffff]
 *   arg3: [0x0 ~> 0xffffffffffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffffffffffffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_p256_subborrowx_u64(uint64_t* out1, fiat_p256_uint1* out2, fiat_p256_uint1 arg1, uint64_t arg2, uint64_t arg3) {
  fiat_p256_int128 x1 = ((arg2 - (fiat_p256_int128)arg1) - arg3);
  fiat_p256_int1 x2 = (fiat_p256_int1)(x1 >> 64);
  uint64_t x3 = (uint64_t)(x1 & UINT64_C(0xffffffffffffffff));
  *out1 = x3;
  *out2 = (fiat_p256_uint1)(0x0 - x2);
}

/*
 * Input Bounds:
 *   arg1: [0x0 ~> 0xffffffffffffffff]
 *   arg2: [0x0 ~> 0xffffffffffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffffffffffffff]
 *   out2: [0x0 ~> 0xffffffffffffffff]
 */
static void fiat_p256_mulx_u64(uint64_t* out1, uint64_t* out2, uint64_t arg1, uint64_t arg2) {
  fiat_p256_uint128 x1 = ((fiat_p256_uint128)arg1 * arg2);
  uint64_t x2 = (uint64_t)(x1 & UINT64_C(0xffffffffffffffff));
  uint64_t x3 = (uint64_t)(x1 >> 64);
  *out1 = x2;
  *out2 = x3;
}

/*
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xffffffffffffffff]
 *   arg3: [0x0 ~> 0xffffffffffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffffffffffffff]
 */
static void fiat_p256_cmovznz_u64(uint64_t* out1, fiat_p256_uint1 arg1, uint64_t arg2, uint64_t arg3) {
  fiat_p256_uint1 x1 = (!(!arg1));
  uint64_t x2 = ((fiat_p256_int1)(0x0 - x1) & UINT64_C(0xffffffffffffffff));
  // Note this line has been patched from the synthesized code to add value
  // barriers.
  //
  // Clang recognizes this pattern as a select. While it usually transforms it
  // to a cmov, it sometimes further transforms it into a branch, which we do
  // not want.
  uint64_t x3 = ((value_barrier_u64(x2) & arg3) | (value_barrier_u64(~x2) & arg2));
  *out1 = x3;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 *   arg2: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 */
static void fiat_p256_mul(uint64_t out1[4], const uint64_t arg1[4], const uint64_t arg2[4]) {
  uint64_t x1 = (arg1[1]);
  uint64_t x2 = (arg1[2]);
  uint64_t x3 = (arg1[3]);
  uint64_t x4 = (arg1[0]);
  uint64_t x5;
  uint64_t x6;
  fiat_p256_mulx_u64(&x5, &x6, x4, (arg2[3]));
  uint64_t x7;
  uint64_t x8;
  fiat_p256_mulx_u64(&x7, &x8, x4, (arg2[2]));
  uint64_t x9;
  uint64_t x10;
  fiat_p256_mulx_u64(&x9, &x10, x4, (arg2[1]));
  uint64_t x11;
  uint64_t x12;
  fiat_p256_mulx_u64(&x11, &x12, x4, (arg2[0]));
  uint64_t x13;
  fiat_p256_uint1 x14;
  fiat_p256_addcarryx_u64(&x13, &x14, 0x0, x9, x12);
  uint64_t x15;
  fiat_p256_uint1 x16;
  fiat_p256_addcarryx_u64(&x15, &x16, x14, x7, x10);
  uint64_t x17;
  fiat_p256_uint1 x18;
  fiat_p256_addcarryx_u64(&x17, &x18, x16, x5, x8);
  uint64_t x19;
  fiat_p256_uint1 x20;
  fiat_p256_addcarryx_u64(&x19, &x20, x18, 0x0, x6);
  uint64_t x21;
  uint64_t x22;
  fiat_p256_mulx_u64(&x21, &x22, x11, UINT64_C(0xffffffff00000001));
  uint64_t x23;
  uint64_t x24;
  fiat_p256_mulx_u64(&x23, &x24, x11, UINT32_C(0xffffffff));
  uint64_t x25;
  uint64_t x26;
  fiat_p256_mulx_u64(&x25, &x26, x11, UINT64_C(0xffffffffffffffff));
  uint64_t x27;
  fiat_p256_uint1 x28;
  fiat_p256_addcarryx_u64(&x27, &x28, 0x0, x23, x26);
  uint64_t x29;
  fiat_p256_uint1 x30;
  fiat_p256_addcarryx_u64(&x29, &x30, x28, 0x0, x24);
  uint64_t x31;
  fiat_p256_uint1 x32;
  fiat_p256_addcarryx_u64(&x31, &x32, 0x0, x25, x11);
  uint64_t x33;
  fiat_p256_uint1 x34;
  fiat_p256_addcarryx_u64(&x33, &x34, x32, x27, x13);
  uint64_t x35;
  fiat_p256_uint1 x36;
  fiat_p256_addcarryx_u64(&x35, &x36, x34, x29, x15);
  uint64_t x37;
  fiat_p256_uint1 x38;
  fiat_p256_addcarryx_u64(&x37, &x38, x36, x21, x17);
  uint64_t x39;
  fiat_p256_uint1 x40;
  fiat_p256_addcarryx_u64(&x39, &x40, x38, x22, x19);
  uint64_t x41;
  fiat_p256_uint1 x42;
  fiat_p256_addcarryx_u64(&x41, &x42, x40, 0x0, 0x0);
  uint64_t x43;
  uint64_t x44;
  fiat_p256_mulx_u64(&x43, &x44, x1, (arg2[3]));
  uint64_t x45;
  uint64_t x46;
  fiat_p256_mulx_u64(&x45, &x46, x1, (arg2[2]));
  uint64_t x47;
  uint64_t x48;
  fiat_p256_mulx_u64(&x47, &x48, x1, (arg2[1]));
  uint64_t x49;
  uint64_t x50;
  fiat_p256_mulx_u64(&x49, &x50, x1, (arg2[0]));
  uint64_t x51;
  fiat_p256_uint1 x52;
  fiat_p256_addcarryx_u64(&x51, &x52, 0x0, x47, x50);
  uint64_t x53;
  fiat_p256_uint1 x54;
  fiat_p256_addcarryx_u64(&x53, &x54, x52, x45, x48);
  uint64_t x55;
  fiat_p256_uint1 x56;
  fiat_p256_addcarryx_u64(&x55, &x56, x54, x43, x46);
  uint64_t x57;
  fiat_p256_uint1 x58;
  fiat_p256_addcarryx_u64(&x57, &x58, x56, 0x0, x44);
  uint64_t x59;
  fiat_p256_uint1 x60;
  fiat_p256_addcarryx_u64(&x59, &x60, 0x0, x49, x33);
  uint64_t x61;
  fiat_p256_uint1 x62;
  fiat_p256_addcarryx_u64(&x61, &x62, x60, x51, x35);
  uint64_t x63;
  fiat_p256_uint1 x64;
  fiat_p256_addcarryx_u64(&x63, &x64, x62, x53, x37);
  uint64_t x65;
  fiat_p256_uint1 x66;
  fiat_p256_addcarryx_u64(&x65, &x66, x64, x55, x39);
  uint64_t x67;
  fiat_p256_uint1 x68;
  fiat_p256_addcarryx_u64(&x67, &x68, x66, x57, (fiat_p256_uint1)x41);
  uint64_t x69;
  uint64_t x70;
  fiat_p256_mulx_u64(&x69, &x70, x59, UINT64_C(0xffffffff00000001));
  uint64_t x71;
  uint64_t x72;
  fiat_p256_mulx_u64(&x71, &x72, x59, UINT32_C(0xffffffff));
  uint64_t x73;
  uint64_t x74;
  fiat_p256_mulx_u64(&x73, &x74, x59, UINT64_C(0xffffffffffffffff));
  uint64_t x75;
  fiat_p256_uint1 x76;
  fiat_p256_addcarryx_u64(&x75, &x76, 0x0, x71, x74);
  uint64_t x77;
  fiat_p256_uint1 x78;
  fiat_p256_addcarryx_u64(&x77, &x78, x76, 0x0, x72);
  uint64_t x79;
  fiat_p256_uint1 x80;
  fiat_p256_addcarryx_u64(&x79, &x80, 0x0, x73, x59);
  uint64_t x81;
  fiat_p256_uint1 x82;
  fiat_p256_addcarryx_u64(&x81, &x82, x80, x75, x61);
  uint64_t x83;
  fiat_p256_uint1 x84;
  fiat_p256_addcarryx_u64(&x83, &x84, x82, x77, x63);
  uint64_t x85;
  fiat_p256_uint1 x86;
  fiat_p256_addcarryx_u64(&x85, &x86, x84, x69, x65);
  uint64_t x87;
  fiat_p256_uint1 x88;
  fiat_p256_addcarryx_u64(&x87, &x88, x86, x70, x67);
  uint64_t x89;
  fiat_p256_uint1 x90;
  fiat_p256_addcarryx_u64(&x89, &x90, x88, 0x0, x68);
  uint64_t x91;
  uint64_t x92;
  fiat_p256_mulx_u64(&x91, &x92, x2, (arg2[3]));
  uint64_t x93;
  uint64_t x94;
  fiat_p256_mulx_u64(&x93, &x94, x2, (arg2[2]));
  uint64_t x95;
  uint64_t x96;
  fiat_p256_mulx_u64(&x95, &x96, x2, (arg2[1]));
  uint64_t x97;
  uint64_t x98;
  fiat_p256_mulx_u64(&x97, &x98, x2, (arg2[0]));
  uint64_t x99;
  fiat_p256_uint1 x100;
  fiat_p256_addcarryx_u64(&x99, &x100, 0x0, x95, x98);
  uint64_t x101;
  fiat_p256_uint1 x102;
  fiat_p256_addcarryx_u64(&x101, &x102, x100, x93, x96);
  uint64_t x103;
  fiat_p256_uint1 x104;
  fiat_p256_addcarryx_u64(&x103, &x104, x102, x91, x94);
  uint64_t x105;
  fiat_p256_uint1 x106;
  fiat_p256_addcarryx_u64(&x105, &x106, x104, 0x0, x92);
  uint64_t x107;
  fiat_p256_uint1 x108;
  fiat_p256_addcarryx_u64(&x107, &x108, 0x0, x97, x81);
  uint64_t x109;
  fiat_p256_uint1 x110;
  fiat_p256_addcarryx_u64(&x109, &x110, x108, x99, x83);
  uint64_t x111;
  fiat_p256_uint1 x112;
  fiat_p256_addcarryx_u64(&x111, &x112, x110, x101, x85);
  uint64_t x113;
  fiat_p256_uint1 x114;
  fiat_p256_addcarryx_u64(&x113, &x114, x112, x103, x87);
  uint64_t x115;
  fiat_p256_uint1 x116;
  fiat_p256_addcarryx_u64(&x115, &x116, x114, x105, x89);
  uint64_t x117;
  uint64_t x118;
  fiat_p256_mulx_u64(&x117, &x118, x107, UINT64_C(0xffffffff00000001));
  uint64_t x119;
  uint64_t x120;
  fiat_p256_mulx_u64(&x119, &x120, x107, UINT32_C(0xffffffff));
  uint64_t x121;
  uint64_t x122;
  fiat_p256_mulx_u64(&x121, &x122, x107, UINT64_C(0xffffffffffffffff));
  uint64_t x123;
  fiat_p256_uint1 x124;
  fiat_p256_addcarryx_u64(&x123, &x124, 0x0, x119, x122);
  uint64_t x125;
  fiat_p256_uint1 x126;
  fiat_p256_addcarryx_u64(&x125, &x126, x124, 0x0, x120);
  uint64_t x127;
  fiat_p256_uint1 x128;
  fiat_p256_addcarryx_u64(&x127, &x128, 0x0, x121, x107);
  uint64_t x129;
  fiat_p256_uint1 x130;
  fiat_p256_addcarryx_u64(&x129, &x130, x128, x123, x109);
  uint64_t x131;
  fiat_p256_uint1 x132;
  fiat_p256_addcarryx_u64(&x131, &x132, x130, x125, x111);
  uint64_t x133;
  fiat_p256_uint1 x134;
  fiat_p256_addcarryx_u64(&x133, &x134, x132, x117, x113);
  uint64_t x135;
  fiat_p256_uint1 x136;
  fiat_p256_addcarryx_u64(&x135, &x136, x134, x118, x115);
  uint64_t x137;
  fiat_p256_uint1 x138;
  fiat_p256_addcarryx_u64(&x137, &x138, x136, 0x0, x116);
  uint64_t x139;
  uint64_t x140;
  fiat_p256_mulx_u64(&x139, &x140, x3, (arg2[3]));
  uint64_t x141;
  uint64_t x142;
  fiat_p256_mulx_u64(&x141, &x142, x3, (arg2[2]));
  uint64_t x143;
  uint64_t x144;
  fiat_p256_mulx_u64(&x143, &x144, x3, (arg2[1]));
  uint64_t x145;
  uint64_t x146;
  fiat_p256_mulx_u64(&x145, &x146, x3, (arg2[0]));
  uint64_t x147;
  fiat_p256_uint1 x148;
  fiat_p256_addcarryx_u64(&x147, &x148, 0x0, x143, x146);
  uint64_t x149;
  fiat_p256_uint1 x150;
  fiat_p256_addcarryx_u64(&x149, &x150, x148, x141, x144);
  uint64_t x151;
  fiat_p256_uint1 x152;
  fiat_p256_addcarryx_u64(&x151, &x152, x150, x139, x142);
  uint64_t x153;
  fiat_p256_uint1 x154;
  fiat_p256_addcarryx_u64(&x153, &x154, x152, 0x0, x140);
  uint64_t x155;
  fiat_p256_uint1 x156;
  fiat_p256_addcarryx_u64(&x155, &x156, 0x0, x145, x129);
  uint64_t x157;
  fiat_p256_uint1 x158;
  fiat_p256_addcarryx_u64(&x157, &x158, x156, x147, x131);
  uint64_t x159;
  fiat_p256_uint1 x160;
  fiat_p256_addcarryx_u64(&x159, &x160, x158, x149, x133);
  uint64_t x161;
  fiat_p256_uint1 x162;
  fiat_p256_addcarryx_u64(&x161, &x162, x160, x151, x135);
  uint64_t x163;
  fiat_p256_uint1 x164;
  fiat_p256_addcarryx_u64(&x163, &x164, x162, x153, x137);
  uint64_t x165;
  uint64_t x166;
  fiat_p256_mulx_u64(&x165, &x166, x155, UINT64_C(0xffffffff00000001));
  uint64_t x167;
  uint64_t x168;
  fiat_p256_mulx_u64(&x167, &x168, x155, UINT32_C(0xffffffff));
  uint64_t x169;
  uint64_t x170;
  fiat_p256_mulx_u64(&x169, &x170, x155, UINT64_C(0xffffffffffffffff));
  uint64_t x171;
  fiat_p256_uint1 x172;
  fiat_p256_addcarryx_u64(&x171, &x172, 0x0, x167, x170);
  uint64_t x173;
  fiat_p256_uint1 x174;
  fiat_p256_addcarryx_u64(&x173, &x174, x172, 0x0, x168);
  uint64_t x175;
  fiat_p256_uint1 x176;
  fiat_p256_addcarryx_u64(&x175, &x176, 0x0, x169, x155);
  uint64_t x177;
  fiat_p256_uint1 x178;
  fiat_p256_addcarryx_u64(&x177, &x178, x176, x171, x157);
  uint64_t x179;
  fiat_p256_uint1 x180;
  fiat_p256_addcarryx_u64(&x179, &x180, x178, x173, x159);
  uint64_t x181;
  fiat_p256_uint1 x182;
  fiat_p256_addcarryx_u64(&x181, &x182, x180, x165, x161);
  uint64_t x183;
  fiat_p256_uint1 x184;
  fiat_p256_addcarryx_u64(&x183, &x184, x182, x166, x163);
  uint64_t x185;
  fiat_p256_uint1 x186;
  fiat_p256_addcarryx_u64(&x185, &x186, x184, 0x0, x164);
  uint64_t x187;
  fiat_p256_uint1 x188;
  fiat_p256_subborrowx_u64(&x187, &x188, 0x0, x177, UINT64_C(0xffffffffffffffff));
  uint64_t x189;
  fiat_p256_uint1 x190;
  fiat_p256_subborrowx_u64(&x189, &x190, x188, x179, UINT32_C(0xffffffff));
  uint64_t x191;
  fiat_p256_uint1 x192;
  fiat_p256_subborrowx_u64(&x191, &x192, x190, x181, 0x0);
  uint64_t x193;
  fiat_p256_uint1 x194;
  fiat_p256_subborrowx_u64(&x193, &x194, x192, x183, UINT64_C(0xffffffff00000001));
  uint64_t x195;
  fiat_p256_uint1 x196;
  fiat_p256_subborrowx_u64(&x195, &x196, x194, x185, 0x0);
  uint64_t x197;
  fiat_p256_cmovznz_u64(&x197, x196, x187, x177);
  uint64_t x198;
  fiat_p256_cmovznz_u64(&x198, x196, x189, x179);
  uint64_t x199;
  fiat_p256_cmovznz_u64(&x199, x196, x191, x181);
  uint64_t x200;
  fiat_p256_cmovznz_u64(&x200, x196, x193, x183);
  out1[0] = x197;
  out1[1] = x198;
  out1[2] = x199;
  out1[3] = x200;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 */
static void fiat_p256_square(uint64_t out1[4], const uint64_t arg1[4]) {
  uint64_t x1 = (arg1[1]);
  uint64_t x2 = (arg1[2]);
  uint64_t x3 = (arg1[3]);
  uint64_t x4 = (arg1[0]);
  uint64_t x5;
  uint64_t x6;
  fiat_p256_mulx_u64(&x5, &x6, x4, (arg1[3]));
  uint64_t x7;
  uint64_t x8;
  fiat_p256_mulx_u64(&x7, &x8, x4, (arg1[2]));
  uint64_t x9;
  uint64_t x10;
  fiat_p256_mulx_u64(&x9, &x10, x4, (arg1[1]));
  uint64_t x11;
  uint64_t x12;
  fiat_p256_mulx_u64(&x11, &x12, x4, (arg1[0]));
  uint64_t x13;
  fiat_p256_uint1 x14;
  fiat_p256_addcarryx_u64(&x13, &x14, 0x0, x9, x12);
  uint64_t x15;
  fiat_p256_uint1 x16;
  fiat_p256_addcarryx_u64(&x15, &x16, x14, x7, x10);
  uint64_t x17;
  fiat_p256_uint1 x18;
  fiat_p256_addcarryx_u64(&x17, &x18, x16, x5, x8);
  uint64_t x19;
  fiat_p256_uint1 x20;
  fiat_p256_addcarryx_u64(&x19, &x20, x18, 0x0, x6);
  uint64_t x21;
  uint64_t x22;
  fiat_p256_mulx_u64(&x21, &x22, x11, UINT64_C(0xffffffff00000001));
  uint64_t x23;
  uint64_t x24;
  fiat_p256_mulx_u64(&x23, &x24, x11, UINT32_C(0xffffffff));
  uint64_t x25;
  uint64_t x26;
  fiat_p256_mulx_u64(&x25, &x26, x11, UINT64_C(0xffffffffffffffff));
  uint64_t x27;
  fiat_p256_uint1 x28;
  fiat_p256_addcarryx_u64(&x27, &x28, 0x0, x23, x26);
  uint64_t x29;
  fiat_p256_uint1 x30;
  fiat_p256_addcarryx_u64(&x29, &x30, x28, 0x0, x24);
  uint64_t x31;
  fiat_p256_uint1 x32;
  fiat_p256_addcarryx_u64(&x31, &x32, 0x0, x25, x11);
  uint64_t x33;
  fiat_p256_uint1 x34;
  fiat_p256_addcarryx_u64(&x33, &x34, x32, x27, x13);
  uint64_t x35;
  fiat_p256_uint1 x36;
  fiat_p256_addcarryx_u64(&x35, &x36, x34, x29, x15);
  uint64_t x37;
  fiat_p256_uint1 x38;
  fiat_p256_addcarryx_u64(&x37, &x38, x36, x21, x17);
  uint64_t x39;
  fiat_p256_uint1 x40;
  fiat_p256_addcarryx_u64(&x39, &x40, x38, x22, x19);
  uint64_t x41;
  fiat_p256_uint1 x42;
  fiat_p256_addcarryx_u64(&x41, &x42, x40, 0x0, 0x0);
  uint64_t x43;
  uint64_t x44;
  fiat_p256_mulx_u64(&x43, &x44, x1, (arg1[3]));
  uint64_t x45;
  uint64_t x46;
  fiat_p256_mulx_u64(&x45, &x46, x1, (arg1[2]));
  uint64_t x47;
  uint64_t x48;
  fiat_p256_mulx_u64(&x47, &x48, x1, (arg1[1]));
  uint64_t x49;
  uint64_t x50;
  fiat_p256_mulx_u64(&x49, &x50, x1, (arg1[0]));
  uint64_t x51;
  fiat_p256_uint1 x52;
  fiat_p256_addcarryx_u64(&x51, &x52, 0x0, x47, x50);
  uint64_t x53;
  fiat_p256_uint1 x54;
  fiat_p256_addcarryx_u64(&x53, &x54, x52, x45, x48);
  uint64_t x55;
  fiat_p256_uint1 x56;
  fiat_p256_addcarryx_u64(&x55, &x56, x54, x43, x46);
  uint64_t x57;
  fiat_p256_uint1 x58;
  fiat_p256_addcarryx_u64(&x57, &x58, x56, 0x0, x44);
  uint64_t x59;
  fiat_p256_uint1 x60;
  fiat_p256_addcarryx_u64(&x59, &x60, 0x0, x49, x33);
  uint64_t x61;
  fiat_p256_uint1 x62;
  fiat_p256_addcarryx_u64(&x61, &x62, x60, x51, x35);
  uint64_t x63;
  fiat_p256_uint1 x64;
  fiat_p256_addcarryx_u64(&x63, &x64, x62, x53, x37);
  uint64_t x65;
  fiat_p256_uint1 x66;
  fiat_p256_addcarryx_u64(&x65, &x66, x64, x55, x39);
  uint64_t x67;
  fiat_p256_uint1 x68;
  fiat_p256_addcarryx_u64(&x67, &x68, x66, x57, (fiat_p256_uint1)x41);
  uint64_t x69;
  uint64_t x70;
  fiat_p256_mulx_u64(&x69, &x70, x59, UINT64_C(0xffffffff00000001));
  uint64_t x71;
  uint64_t x72;
  fiat_p256_mulx_u64(&x71, &x72, x59, UINT32_C(0xffffffff));
  uint64_t x73;
  uint64_t x74;
  fiat_p256_mulx_u64(&x73, &x74, x59, UINT64_C(0xffffffffffffffff));
  uint64_t x75;
  fiat_p256_uint1 x76;
  fiat_p256_addcarryx_u64(&x75, &x76, 0x0, x71, x74);
  uint64_t x77;
  fiat_p256_uint1 x78;
  fiat_p256_addcarryx_u64(&x77, &x78, x76, 0x0, x72);
  uint64_t x79;
  fiat_p256_uint1 x80;
  fiat_p256_addcarryx_u64(&x79, &x80, 0x0, x73, x59);
  uint64_t x81;
  fiat_p256_uint1 x82;
  fiat_p256_addcarryx_u64(&x81, &x82, x80, x75, x61);
  uint64_t x83;
  fiat_p256_uint1 x84;
  fiat_p256_addcarryx_u64(&x83, &x84, x82, x77, x63);
  uint64_t x85;
  fiat_p256_uint1 x86;
  fiat_p256_addcarryx_u64(&x85, &x86, x84, x69, x65);
  uint64_t x87;
  fiat_p256_uint1 x88;
  fiat_p256_addcarryx_u64(&x87, &x88, x86, x70, x67);
  uint64_t x89;
  fiat_p256_uint1 x90;
  fiat_p256_addcarryx_u64(&x89, &x90, x88, 0x0, x68);
  uint64_t x91;
  uint64_t x92;
  fiat_p256_mulx_u64(&x91, &x92, x2, (arg1[3]));
  uint64_t x93;
  uint64_t x94;
  fiat_p256_mulx_u64(&x93, &x94, x2, (arg1[2]));
  uint64_t x95;
  uint64_t x96;
  fiat_p256_mulx_u64(&x95, &x96, x2, (arg1[1]));
  uint64_t x97;
  uint64_t x98;
  fiat_p256_mulx_u64(&x97, &x98, x2, (arg1[0]));
  uint64_t x99;
  fiat_p256_uint1 x100;
  fiat_p256_addcarryx_u64(&x99, &x100, 0x0, x95, x98);
  uint64_t x101;
  fiat_p256_uint1 x102;
  fiat_p256_addcarryx_u64(&x101, &x102, x100, x93, x96);
  uint64_t x103;
  fiat_p256_uint1 x104;
  fiat_p256_addcarryx_u64(&x103, &x104, x102, x91, x94);
  uint64_t x105;
  fiat_p256_uint1 x106;
  fiat_p256_addcarryx_u64(&x105, &x106, x104, 0x0, x92);
  uint64_t x107;
  fiat_p256_uint1 x108;
  fiat_p256_addcarryx_u64(&x107, &x108, 0x0, x97, x81);
  uint64_t x109;
  fiat_p256_uint1 x110;
  fiat_p256_addcarryx_u64(&x109, &x110, x108, x99, x83);
  uint64_t x111;
  fiat_p256_uint1 x112;
  fiat_p256_addcarryx_u64(&x111, &x112, x110, x101, x85);
  uint64_t x113;
  fiat_p256_uint1 x114;
  fiat_p256_addcarryx_u64(&x113, &x114, x112, x103, x87);
  uint64_t x115;
  fiat_p256_uint1 x116;
  fiat_p256_addcarryx_u64(&x115, &x116, x114, x105, x89);
  uint64_t x117;
  uint64_t x118;
  fiat_p256_mulx_u64(&x117, &x118, x107, UINT64_C(0xffffffff00000001));
  uint64_t x119;
  uint64_t x120;
  fiat_p256_mulx_u64(&x119, &x120, x107, UINT32_C(0xffffffff));
  uint64_t x121;
  uint64_t x122;
  fiat_p256_mulx_u64(&x121, &x122, x107, UINT64_C(0xffffffffffffffff));
  uint64_t x123;
  fiat_p256_uint1 x124;
  fiat_p256_addcarryx_u64(&x123, &x124, 0x0, x119, x122);
  uint64_t x125;
  fiat_p256_uint1 x126;
  fiat_p256_addcarryx_u64(&x125, &x126, x124, 0x0, x120);
  uint64_t x127;
  fiat_p256_uint1 x128;
  fiat_p256_addcarryx_u64(&x127, &x128, 0x0, x121, x107);
  uint64_t x129;
  fiat_p256_uint1 x130;
  fiat_p256_addcarryx_u64(&x129, &x130, x128, x123, x109);
  uint64_t x131;
  fiat_p256_uint1 x132;
  fiat_p256_addcarryx_u64(&x131, &x132, x130, x125, x111);
  uint64_t x133;
  fiat_p256_uint1 x134;
  fiat_p256_addcarryx_u64(&x133, &x134, x132, x117, x113);
  uint64_t x135;
  fiat_p256_uint1 x136;
  fiat_p256_addcarryx_u64(&x135, &x136, x134, x118, x115);
  uint64_t x137;
  fiat_p256_uint1 x138;
  fiat_p256_addcarryx_u64(&x137, &x138, x136, 0x0, x116);
  uint64_t x139;
  uint64_t x140;
  fiat_p256_mulx_u64(&x139, &x140, x3, (arg1[3]));
  uint64_t x141;
  uint64_t x142;
  fiat_p256_mulx_u64(&x141, &x142, x3, (arg1[2]));
  uint64_t x143;
  uint64_t x144;
  fiat_p256_mulx_u64(&x143, &x144, x3, (arg1[1]));
  uint64_t x145;
  uint64_t x146;
  fiat_p256_mulx_u64(&x145, &x146, x3, (arg1[0]));
  uint64_t x147;
  fiat_p256_uint1 x148;
  fiat_p256_addcarryx_u64(&x147, &x148, 0x0, x143, x146);
  uint64_t x149;
  fiat_p256_uint1 x150;
  fiat_p256_addcarryx_u64(&x149, &x150, x148, x141, x144);
  uint64_t x151;
  fiat_p256_uint1 x152;
  fiat_p256_addcarryx_u64(&x151, &x152, x150, x139, x142);
  uint64_t x153;
  fiat_p256_uint1 x154;
  fiat_p256_addcarryx_u64(&x153, &x154, x152, 0x0, x140);
  uint64_t x155;
  fiat_p256_uint1 x156;
  fiat_p256_addcarryx_u64(&x155, &x156, 0x0, x145, x129);
  uint64_t x157;
  fiat_p256_uint1 x158;
  fiat_p256_addcarryx_u64(&x157, &x158, x156, x147, x131);
  uint64_t x159;
  fiat_p256_uint1 x160;
  fiat_p256_addcarryx_u64(&x159, &x160, x158, x149, x133);
  uint64_t x161;
  fiat_p256_uint1 x162;
  fiat_p256_addcarryx_u64(&x161, &x162, x160, x151, x135);
  uint64_t x163;
  fiat_p256_uint1 x164;
  fiat_p256_addcarryx_u64(&x163, &x164, x162, x153, x137);
  uint64_t x165;
  uint64_t x166;
  fiat_p256_mulx_u64(&x165, &x166, x155, UINT64_C(0xffffffff00000001));
  uint64_t x167;
  uint64_t x168;
  fiat_p256_mulx_u64(&x167, &x168, x155, UINT32_C(0xffffffff));
  uint64_t x169;
  uint64_t x170;
  fiat_p256_mulx_u64(&x169, &x170, x155, UINT64_C(0xffffffffffffffff));
  uint64_t x171;
  fiat_p256_uint1 x172;
  fiat_p256_addcarryx_u64(&x171, &x172, 0x0, x167, x170);
  uint64_t x173;
  fiat_p256_uint1 x174;
  fiat_p256_addcarryx_u64(&x173, &x174, x172, 0x0, x168);
  uint64_t x175;
  fiat_p256_uint1 x176;
  fiat_p256_addcarryx_u64(&x175, &x176, 0x0, x169, x155);
  uint64_t x177;
  fiat_p256_uint1 x178;
  fiat_p256_addcarryx_u64(&x177, &x178, x176, x171, x157);
  uint64_t x179;
  fiat_p256_uint1 x180;
  fiat_p256_addcarryx_u64(&x179, &x180, x178, x173, x159);
  uint64_t x181;
  fiat_p256_uint1 x182;
  fiat_p256_addcarryx_u64(&x181, &x182, x180, x165, x161);
  uint64_t x183;
  fiat_p256_uint1 x184;
  fiat_p256_addcarryx_u64(&x183, &x184, x182, x166, x163);
  uint64_t x185;
  fiat_p256_uint1 x186;
  fiat_p256_addcarryx_u64(&x185, &x186, x184, 0x0, x164);
  uint64_t x187;
  fiat_p256_uint1 x188;
  fiat_p256_subborrowx_u64(&x187, &x188, 0x0, x177, UINT64_C(0xffffffffffffffff));
  uint64_t x189;
  fiat_p256_uint1 x190;
  fiat_p256_subborrowx_u64(&x189, &x190, x188, x179, UINT32_C(0xffffffff));
  uint64_t x191;
  fiat_p256_uint1 x192;
  fiat_p256_subborrowx_u64(&x191, &x192, x190, x181, 0x0);
  uint64_t x193;
  fiat_p256_uint1 x194;
  fiat_p256_subborrowx_u64(&x193, &x194, x192, x183, UINT64_C(0xffffffff00000001));
  uint64_t x195;
  fiat_p256_uint1 x196;
  fiat_p256_subborrowx_u64(&x195, &x196, x194, x185, 0x0);
  uint64_t x197;
  fiat_p256_cmovznz_u64(&x197, x196, x187, x177);
  uint64_t x198;
  fiat_p256_cmovznz_u64(&x198, x196, x189, x179);
  uint64_t x199;
  fiat_p256_cmovznz_u64(&x199, x196, x191, x181);
  uint64_t x200;
  fiat_p256_cmovznz_u64(&x200, x196, x193, x183);
  out1[0] = x197;
  out1[1] = x198;
  out1[2] = x199;
  out1[3] = x200;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 *   arg2: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 */
static void fiat_p256_add(uint64_t out1[4], const uint64_t arg1[4], const uint64_t arg2[4]) {
  uint64_t x1;
  fiat_p256_uint1 x2;
  fiat_p256_addcarryx_u64(&x1, &x2, 0x0, (arg2[0]), (arg1[0]));
  uint64_t x3;
  fiat_p256_uint1 x4;
  fiat_p256_addcarryx_u64(&x3, &x4, x2, (arg2[1]), (arg1[1]));
  uint64_t x5;
  fiat_p256_uint1 x6;
  fiat_p256_addcarryx_u64(&x5, &x6, x4, (arg2[2]), (arg1[2]));
  uint64_t x7;
  fiat_p256_uint1 x8;
  fiat_p256_addcarryx_u64(&x7, &x8, x6, (arg2[3]), (arg1[3]));
  uint64_t x9;
  fiat_p256_uint1 x10;
  fiat_p256_subborrowx_u64(&x9, &x10, 0x0, x1, UINT64_C(0xffffffffffffffff));
  uint64_t x11;
  fiat_p256_uint1 x12;
  fiat_p256_subborrowx_u64(&x11, &x12, x10, x3, UINT32_C(0xffffffff));
  uint64_t x13;
  fiat_p256_uint1 x14;
  fiat_p256_subborrowx_u64(&x13, &x14, x12, x5, 0x0);
  uint64_t x15;
  fiat_p256_uint1 x16;
  fiat_p256_subborrowx_u64(&x15, &x16, x14, x7, UINT64_C(0xffffffff00000001));
  uint64_t x17;
  fiat_p256_uint1 x18;
  fiat_p256_subborrowx_u64(&x17, &x18, x16, x8, 0x0);
  uint64_t x19;
  fiat_p256_cmovznz_u64(&x19, x18, x9, x1);
  uint64_t x20;
  fiat_p256_cmovznz_u64(&x20, x18, x11, x3);
  uint64_t x21;
  fiat_p256_cmovznz_u64(&x21, x18, x13, x5);
  uint64_t x22;
  fiat_p256_cmovznz_u64(&x22, x18, x15, x7);
  out1[0] = x19;
  out1[1] = x20;
  out1[2] = x21;
  out1[3] = x22;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 *   arg2: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 */
static void fiat_p256_sub(uint64_t out1[4], const uint64_t arg1[4], const uint64_t arg2[4]) {
  uint64_t x1;
  fiat_p256_uint1 x2;
  fiat_p256_subborrowx_u64(&x1, &x2, 0x0, (arg1[0]), (arg2[0]));
  uint64_t x3;
  fiat_p256_uint1 x4;
  fiat_p256_subborrowx_u64(&x3, &x4, x2, (arg1[1]), (arg2[1]));
  uint64_t x5;
  fiat_p256_uint1 x6;
  fiat_p256_subborrowx_u64(&x5, &x6, x4, (arg1[2]), (arg2[2]));
  uint64_t x7;
  fiat_p256_uint1 x8;
  fiat_p256_subborrowx_u64(&x7, &x8, x6, (arg1[3]), (arg2[3]));
  uint64_t x9;
  fiat_p256_cmovznz_u64(&x9, x8, 0x0, UINT64_C(0xffffffffffffffff));
  uint64_t x10;
  fiat_p256_uint1 x11;
  fiat_p256_addcarryx_u64(&x10, &x11, 0x0, (x9 & UINT64_C(0xffffffffffffffff)), x1);
  uint64_t x12;
  fiat_p256_uint1 x13;
  fiat_p256_addcarryx_u64(&x12, &x13, x11, (x9 & UINT32_C(0xffffffff)), x3);
  uint64_t x14;
  fiat_p256_uint1 x15;
  fiat_p256_addcarryx_u64(&x14, &x15, x13, 0x0, x5);
  uint64_t x16;
  fiat_p256_uint1 x17;
  fiat_p256_addcarryx_u64(&x16, &x17, x15, (x9 & UINT64_C(0xffffffff00000001)), x7);
  out1[0] = x10;
  out1[1] = x12;
  out1[2] = x14;
  out1[3] = x16;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 */
static void fiat_p256_opp(uint64_t out1[4], const uint64_t arg1[4]) {
  uint64_t x1;
  fiat_p256_uint1 x2;
  fiat_p256_subborrowx_u64(&x1, &x2, 0x0, 0x0, (arg1[0]));
  uint64_t x3;
  fiat_p256_uint1 x4;
  fiat_p256_subborrowx_u64(&x3, &x4, x2, 0x0, (arg1[1]));
  uint64_t x5;
  fiat_p256_uint1 x6;
  fiat_p256_subborrowx_u64(&x5, &x6, x4, 0x0, (arg1[2]));
  uint64_t x7;
  fiat_p256_uint1 x8;
  fiat_p256_subborrowx_u64(&x7, &x8, x6, 0x0, (arg1[3]));
  uint64_t x9;
  fiat_p256_cmovznz_u64(&x9, x8, 0x0, UINT64_C(0xffffffffffffffff));
  uint64_t x10;
  fiat_p256_uint1 x11;
  fiat_p256_addcarryx_u64(&x10, &x11, 0x0, (x9 & UINT64_C(0xffffffffffffffff)), x1);
  uint64_t x12;
  fiat_p256_uint1 x13;
  fiat_p256_addcarryx_u64(&x12, &x13, x11, (x9 & UINT32_C(0xffffffff)), x3);
  uint64_t x14;
  fiat_p256_uint1 x15;
  fiat_p256_addcarryx_u64(&x14, &x15, x13, 0x0, x5);
  uint64_t x16;
  fiat_p256_uint1 x17;
  fiat_p256_addcarryx_u64(&x16, &x17, x15, (x9 & UINT64_C(0xffffffff00000001)), x7);
  out1[0] = x10;
  out1[1] = x12;
  out1[2] = x14;
  out1[3] = x16;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 */
static void fiat_p256_from_montgomery(uint64_t out1[4], const uint64_t arg1[4]) {
  uint64_t x1 = (arg1[0]);
  uint64_t x2;
  uint64_t x3;
  fiat_p256_mulx_u64(&x2, &x3, x1, UINT64_C(0xffffffff00000001));
  uint64_t x4;
  uint64_t x5;
  fiat_p256_mulx_u64(&x4, &x5, x1, UINT32_C(0xffffffff));
  uint64_t x6;
  uint64_t x7;
  fiat_p256_mulx_u64(&x6, &x7, x1, UINT64_C(0xffffffffffffffff));
  uint64_t x8;
  fiat_p256_uint1 x9;
  fiat_p256_addcarryx_u64(&x8, &x9, 0x0, x4, x7);
  uint64_t x10;
  fiat_p256_uint1 x11;
  fiat_p256_addcarryx_u64(&x10, &x11, 0x0, x6, x1);
  uint64_t x12;
  fiat_p256_uint1 x13;
  fiat_p256_addcarryx_u64(&x12, &x13, x11, x8, 0x0);
  uint64_t x14;
  fiat_p256_uint1 x15;
  fiat_p256_addcarryx_u64(&x14, &x15, 0x0, (arg1[1]), x12);
  uint64_t x16;
  uint64_t x17;
  fiat_p256_mulx_u64(&x16, &x17, x14, UINT64_C(0xffffffff00000001));
  uint64_t x18;
  uint64_t x19;
  fiat_p256_mulx_u64(&x18, &x19, x14, UINT32_C(0xffffffff));
  uint64_t x20;
  uint64_t x21;
  fiat_p256_mulx_u64(&x20, &x21, x14, UINT64_C(0xffffffffffffffff));
  uint64_t x22;
  fiat_p256_uint1 x23;
  fiat_p256_addcarryx_u64(&x22, &x23, 0x0, x18, x21);
  uint64_t x24;
  fiat_p256_uint1 x25;
  fiat_p256_addcarryx_u64(&x24, &x25, x9, 0x0, x5);
  uint64_t x26;
  fiat_p256_uint1 x27;
  fiat_p256_addcarryx_u64(&x26, &x27, x13, x24, 0x0);
  uint64_t x28;
  fiat_p256_uint1 x29;
  fiat_p256_addcarryx_u64(&x28, &x29, x15, 0x0, x26);
  uint64_t x30;
  fiat_p256_uint1 x31;
  fiat_p256_addcarryx_u64(&x30, &x31, 0x0, x20, x14);
  uint64_t x32;
  fiat_p256_uint1 x33;
  fiat_p256_addcarryx_u64(&x32, &x33, x31, x22, x28);
  uint64_t x34;
  fiat_p256_uint1 x35;
  fiat_p256_addcarryx_u64(&x34, &x35, x23, 0x0, x19);
  uint64_t x36;
  fiat_p256_uint1 x37;
  fiat_p256_addcarryx_u64(&x36, &x37, x33, x34, x2);
  uint64_t x38;
  fiat_p256_uint1 x39;
  fiat_p256_addcarryx_u64(&x38, &x39, x37, x16, x3);
  uint64_t x40;
  fiat_p256_uint1 x41;
  fiat_p256_addcarryx_u64(&x40, &x41, 0x0, (arg1[2]), x32);
  uint64_t x42;
  fiat_p256_uint1 x43;
  fiat_p256_addcarryx_u64(&x42, &x43, x41, 0x0, x36);
  uint64_t x44;
  fiat_p256_uint1 x45;
  fiat_p256_addcarryx_u64(&x44, &x45, x43, 0x0, x38);
  uint64_t x46;
  uint64_t x47;
  fiat_p256_mulx_u64(&x46, &x47, x40, UINT64_C(0xffffffff00000001));
  uint64_t x48;
  uint64_t x49;
  fiat_p256_mulx_u64(&x48, &x49, x40, UINT32_C(0xffffffff));
  uint64_t x50;
  uint64_t x51;
  fiat_p256_mulx_u64(&x50, &x51, x40, UINT64_C(0xffffffffffffffff));
  uint64_t x52;
  fiat_p256_uint1 x53;
  fiat_p256_addcarryx_u64(&x52, &x53, 0x0, x48, x51);
  uint64_t x54;
  fiat_p256_uint1 x55;
  fiat_p256_addcarryx_u64(&x54, &x55, 0x0, x50, x40);
  uint64_t x56;
  fiat_p256_uint1 x57;
  fiat_p256_addcarryx_u64(&x56, &x57, x55, x52, x42);
  uint64_t x58;
  fiat_p256_uint1 x59;
  fiat_p256_addcarryx_u64(&x58, &x59, x53, 0x0, x49);
  uint64_t x60;
  fiat_p256_uint1 x61;
  fiat_p256_addcarryx_u64(&x60, &x61, x57, x58, x44);
  uint64_t x62;
  fiat_p256_uint1 x63;
  fiat_p256_addcarryx_u64(&x62, &x63, x39, x17, 0x0);
  uint64_t x64;
  fiat_p256_uint1 x65;
  fiat_p256_addcarryx_u64(&x64, &x65, x45, 0x0, x62);
  uint64_t x66;
  fiat_p256_uint1 x67;
  fiat_p256_addcarryx_u64(&x66, &x67, x61, x46, x64);
  uint64_t x68;
  fiat_p256_uint1 x69;
  fiat_p256_addcarryx_u64(&x68, &x69, 0x0, (arg1[3]), x56);
  uint64_t x70;
  fiat_p256_uint1 x71;
  fiat_p256_addcarryx_u64(&x70, &x71, x69, 0x0, x60);
  uint64_t x72;
  fiat_p256_uint1 x73;
  fiat_p256_addcarryx_u64(&x72, &x73, x71, 0x0, x66);
  uint64_t x74;
  uint64_t x75;
  fiat_p256_mulx_u64(&x74, &x75, x68, UINT64_C(0xffffffff00000001));
  uint64_t x76;
  uint64_t x77;
  fiat_p256_mulx_u64(&x76, &x77, x68, UINT32_C(0xffffffff));
  uint64_t x78;
  uint64_t x79;
  fiat_p256_mulx_u64(&x78, &x79, x68, UINT64_C(0xffffffffffffffff));
  uint64_t x80;
  fiat_p256_uint1 x81;
  fiat_p256_addcarryx_u64(&x80, &x81, 0x0, x76, x79);
  uint64_t x82;
  fiat_p256_uint1 x83;
  fiat_p256_addcarryx_u64(&x82, &x83, 0x0, x78, x68);
  uint64_t x84;
  fiat_p256_uint1 x85;
  fiat_p256_addcarryx_u64(&x84, &x85, x83, x80, x70);
  uint64_t x86;
  fiat_p256_uint1 x87;
  fiat_p256_addcarryx_u64(&x86, &x87, x81, 0x0, x77);
  uint64_t x88;
  fiat_p256_uint1 x89;
  fiat_p256_addcarryx_u64(&x88, &x89, x85, x86, x72);
  uint64_t x90;
  fiat_p256_uint1 x91;
  fiat_p256_addcarryx_u64(&x90, &x91, x67, x47, 0x0);
  uint64_t x92;
  fiat_p256_uint1 x93;
  fiat_p256_addcarryx_u64(&x92, &x93, x73, 0x0, x90);
  uint64_t x94;
  fiat_p256_uint1 x95;
  fiat_p256_addcarryx_u64(&x94, &x95, x89, x74, x92);
  uint64_t x96;
  fiat_p256_uint1 x97;
  fiat_p256_addcarryx_u64(&x96, &x97, x95, x75, 0x0);
  uint64_t x98;
  fiat_p256_uint1 x99;
  fiat_p256_subborrowx_u64(&x98, &x99, 0x0, x84, UINT64_C(0xffffffffffffffff));
  uint64_t x100;
  fiat_p256_uint1 x101;
  fiat_p256_subborrowx_u64(&x100, &x101, x99, x88, UINT32_C(0xffffffff));
  uint64_t x102;
  fiat_p256_uint1 x103;
  fiat_p256_subborrowx_u64(&x102, &x103, x101, x94, 0x0);
  uint64_t x104;
  fiat_p256_uint1 x105;
  fiat_p256_subborrowx_u64(&x104, &x105, x103, x96, UINT64_C(0xffffffff00000001));
  uint64_t x106;
  fiat_p256_uint1 x107;
  fiat_p256_subborrowx_u64(&x106, &x107, x105, 0x0, 0x0);
  uint64_t x108;
  fiat_p256_cmovznz_u64(&x108, x107, x98, x84);
  uint64_t x109;
  fiat_p256_cmovznz_u64(&x109, x107, x100, x88);
  uint64_t x110;
  fiat_p256_cmovznz_u64(&x110, x107, x102, x94);
  uint64_t x111;
  fiat_p256_cmovznz_u64(&x111, x107, x104, x96);
  out1[0] = x108;
  out1[1] = x109;
  out1[2] = x110;
  out1[3] = x111;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffffffffffffff]
 */
static void fiat_p256_nonzero(uint64_t* out1, const uint64_t arg1[4]) {
  uint64_t x1 = ((arg1[0]) | ((arg1[1]) | ((arg1[2]) | ((arg1[3]) | (uint64_t)0x0))));
  *out1 = x1;
}

/*
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 *   arg3: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 */
static void fiat_p256_selectznz(uint64_t out1[4], fiat_p256_uint1 arg1, const uint64_t arg2[4], const uint64_t arg3[4]) {
  uint64_t x1;
  fiat_p256_cmovznz_u64(&x1, arg1, (arg2[0]), (arg3[0]));
  uint64_t x2;
  fiat_p256_cmovznz_u64(&x2, arg1, (arg2[1]), (arg3[1]));
  uint64_t x3;
  fiat_p256_cmovznz_u64(&x3, arg1, (arg2[2]), (arg3[2]));
  uint64_t x4;
  fiat_p256_cmovznz_u64(&x4, arg1, (arg2[3]), (arg3[3]));
  out1[0] = x1;
  out1[1] = x2;
  out1[2] = x3;
  out1[3] = x4;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff]]
 */
static void fiat_p256_to_bytes(uint8_t out1[32], const uint64_t arg1[4]) {
  uint64_t x1 = (arg1[3]);
  uint64_t x2 = (arg1[2]);
  uint64_t x3 = (arg1[1]);
  uint64_t x4 = (arg1[0]);
  uint64_t x5 = (x4 >> 8);
  uint8_t x6 = (uint8_t)(x4 & UINT8_C(0xff));
  uint64_t x7 = (x5 >> 8);
  uint8_t x8 = (uint8_t)(x5 & UINT8_C(0xff));
  uint64_t x9 = (x7 >> 8);
  uint8_t x10 = (uint8_t)(x7 & UINT8_C(0xff));
  uint64_t x11 = (x9 >> 8);
  uint8_t x12 = (uint8_t)(x9 & UINT8_C(0xff));
  uint64_t x13 = (x11 >> 8);
  uint8_t x14 = (uint8_t)(x11 & UINT8_C(0xff));
  uint64_t x15 = (x13 >> 8);
  uint8_t x16 = (uint8_t)(x13 & UINT8_C(0xff));
  uint8_t x17 = (uint8_t)(x15 >> 8);
  uint8_t x18 = (uint8_t)(x15 & UINT8_C(0xff));
  uint8_t x19 = (uint8_t)(x17 & UINT8_C(0xff));
  uint64_t x20 = (x3 >> 8);
  uint8_t x21 = (uint8_t)(x3 & UINT8_C(0xff));
  uint64_t x22 = (x20 >> 8);
  uint8_t x23 = (uint8_t)(x20 & UINT8_C(0xff));
  uint64_t x24 = (x22 >> 8);
  uint8_t x25 = (uint8_t)(x22 & UINT8_C(0xff));
  uint64_t x26 = (x24 >> 8);
  uint8_t x27 = (uint8_t)(x24 & UINT8_C(0xff));
  uint64_t x28 = (x26 >> 8);
  uint8_t x29 = (uint8_t)(x26 & UINT8_C(0xff));
  uint64_t x30 = (x28 >> 8);
  uint8_t x31 = (uint8_t)(x28 & UINT8_C(0xff));
  uint8_t x32 = (uint8_t)(x30 >> 8);
  uint8_t x33 = (uint8_t)(x30 & UINT8_C(0xff));
  uint8_t x34 = (uint8_t)(x32 & UINT8_C(0xff));
  uint64_t x35 = (x2 >> 8);
  uint8_t x36 = (uint8_t)(x2 & UINT8_C(0xff));
  uint64_t x37 = (x35 >> 8);
  uint8_t x38 = (uint8_t)(x35 & UINT8_C(0xff));
  uint64_t x39 = (x37 >> 8);
  uint8_t x40 = (uint8_t)(x37 & UINT8_C(0xff));
  uint64_t x41 = (x39 >> 8);
  uint8_t x42 = (uint8_t)(x39 & UINT8_C(0xff));
  uint64_t x43 = (x41 >> 8);
  uint8_t x44 = (uint8_t)(x41 & UINT8_C(0xff));
  uint64_t x45 = (x43 >> 8);
  uint8_t x46 = (uint8_t)(x43 & UINT8_C(0xff));
  uint8_t x47 = (uint8_t)(x45 >> 8);
  uint8_t x48 = (uint8_t)(x45 & UINT8_C(0xff));
  uint8_t x49 = (uint8_t)(x47 & UINT8_C(0xff));
  uint64_t x50 = (x1 >> 8);
  uint8_t x51 = (uint8_t)(x1 & UINT8_C(0xff));
  uint64_t x52 = (x50 >> 8);
  uint8_t x53 = (uint8_t)(x50 & UINT8_C(0xff));
  uint64_t x54 = (x52 >> 8);
  uint8_t x55 = (uint8_t)(x52 & UINT8_C(0xff));
  uint64_t x56 = (x54 >> 8);
  uint8_t x57 = (uint8_t)(x54 & UINT8_C(0xff));
  uint64_t x58 = (x56 >> 8);
  uint8_t x59 = (uint8_t)(x56 & UINT8_C(0xff));
  uint64_t x60 = (x58 >> 8);
  uint8_t x61 = (uint8_t)(x58 & UINT8_C(0xff));
  uint8_t x62 = (uint8_t)(x60 >> 8);
  uint8_t x63 = (uint8_t)(x60 & UINT8_C(0xff));
  out1[0] = x6;
  out1[1] = x8;
  out1[2] = x10;
  out1[3] = x12;
  out1[4] = x14;
  out1[5] = x16;
  out1[6] = x18;
  out1[7] = x19;
  out1[8] = x21;
  out1[9] = x23;
  out1[10] = x25;
  out1[11] = x27;
  out1[12] = x29;
  out1[13] = x31;
  out1[14] = x33;
  out1[15] = x34;
  out1[16] = x36;
  out1[17] = x38;
  out1[18] = x40;
  out1[19] = x42;
  out1[20] = x44;
  out1[21] = x46;
  out1[22] = x48;
  out1[23] = x49;
  out1[24] = x51;
  out1[25] = x53;
  out1[26] = x55;
  out1[27] = x57;
  out1[28] = x59;
  out1[29] = x61;
  out1[30] = x63;
  out1[31] = x62;
}

/*
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 */
static void fiat_p256_from_bytes(uint64_t out1[4], const uint8_t arg1[32]) {
  uint64_t x1 = ((uint64_t)(arg1[31]) << 56);
  uint64_t x2 = ((uint64_t)(arg1[30]) << 48);
  uint64_t x3 = ((uint64_t)(arg1[29]) << 40);
  uint64_t x4 = ((uint64_t)(arg1[28]) << 32);
  uint64_t x5 = ((uint64_t)(arg1[27]) << 24);
  uint64_t x6 = ((uint64_t)(arg1[26]) << 16);
  uint64_t x7 = ((uint64_t)(arg1[25]) << 8);
  uint8_t x8 = (arg1[24]);
  uint64_t x9 = ((uint64_t)(arg1[23]) << 56);
  uint64_t x10 = ((uint64_t)(arg1[22]) << 48);
  uint64_t x11 = ((uint64_t)(arg1[21]) << 40);
  uint64_t x12 = ((uint64_t)(arg1[20]) << 32);
  uint64_t x13 = ((uint64_t)(arg1[19]) << 24);
  uint64_t x14 = ((uint64_t)(arg1[18]) << 16);
  uint64_t x15 = ((uint64_t)(arg1[17]) << 8);
  uint8_t x16 = (arg1[16]);
  uint64_t x17 = ((uint64_t)(arg1[15]) << 56);
  uint64_t x18 = ((uint64_t)(arg1[14]) << 48);
  uint64_t x19 = ((uint64_t)(arg1[13]) << 40);
  uint64_t x20 = ((uint64_t)(arg1[12]) << 32);
  uint64_t x21 = ((uint64_t)(arg1[11]) << 24);
  uint64_t x22 = ((uint64_t)(arg1[10]) << 16);
  uint64_t x23 = ((uint64_t)(arg1[9]) << 8);
  uint8_t x24 = (arg1[8]);
  uint64_t x25 = ((uint64_t)(arg1[7]) << 56);
  uint64_t x26 = ((uint64_t)(arg1[6]) << 48);
  uint64_t x27 = ((uint64_t)(arg1[5]) << 40);
  uint64_t x28 = ((uint64_t)(arg1[4]) << 32);
  uint64_t x29 = ((uint64_t)(arg1[3]) << 24);
  uint64_t x30 = ((uint64_t)(arg1[2]) << 16);
  uint64_t x31 = ((uint64_t)(arg1[1]) << 8);
  uint8_t x32 = (arg1[0]);
  uint64_t x33 = (x32 + (x31 + (x30 + (x29 + (x28 + (x27 + (x26 + x25)))))));
  uint64_t x34 = (x33 & UINT64_C(0xffffffffffffffff));
  uint64_t x35 = (x8 + (x7 + (x6 + (x5 + (x4 + (x3 + (x2 + x1)))))));
  uint64_t x36 = (x16 + (x15 + (x14 + (x13 + (x12 + (x11 + (x10 + x9)))))));
  uint64_t x37 = (x24 + (x23 + (x22 + (x21 + (x20 + (x19 + (x18 + x17)))))));
  uint64_t x38 = (x37 & UINT64_C(0xffffffffffffffff));
  uint64_t x39 = (x36 & UINT64_C(0xffffffffffffffff));
  out1[0] = x34;
  out1[1] = x38;
  out1[2] = x39;
  out1[3] = x35;
}

