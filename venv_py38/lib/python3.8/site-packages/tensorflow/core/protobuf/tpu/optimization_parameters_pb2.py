# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/core/protobuf/tpu/optimization_parameters.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from tensorflow.compiler.xla.service import hlo_pb2 as tensorflow_dot_compiler_dot_xla_dot_service_dot_hlo__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorflow/core/protobuf/tpu/optimization_parameters.proto',
  package='tensorflow.tpu',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n:tensorflow/core/protobuf/tpu/optimization_parameters.proto\x12\x0etensorflow.tpu\x1a\x1egoogle/protobuf/wrappers.proto\x1a)tensorflow/compiler/xla/service/hlo.proto\"h\n\x0e\x43lippingLimits\x12*\n\x05lower\x18\x01 \x01(\x0b\x32\x1b.google.protobuf.FloatValue\x12*\n\x05upper\x18\x02 \x01(\x0b\x32\x1b.google.protobuf.FloatValue\"v\n\x15SimulatedQuantization\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12\x37\n\x0f\x63lipping_limits\x18\x02 \x01(\x0b\x32\x1e.tensorflow.tpu.ClippingLimits\x12\x13\n\x0bnum_buckets\x18\x03 \x01(\x05\"\"\n\x13\x44ynamicLearningRate\x12\x0b\n\x03tag\x18\x01 \x01(\x05\"k\n\x0cLearningRate\x12\x12\n\x08\x63onstant\x18\x01 \x01(\x02H\x00\x12\x36\n\x07\x64ynamic\x18\x02 \x01(\x0b\x32#.tensorflow.tpu.DynamicLearningRateH\x00\x42\x0f\n\rlearning_rate\".\n\x11\x41\x64\x61gradParametersJ\x04\x08\x01\x10\x02R\x13initial_accumulator\"u\n\x19\x41\x64\x61gradMomentumParameters\x12\x10\n\x08momentum\x18\x01 \x01(\x02\x12\x14\n\x0cuse_nesterov\x18\x02 \x01(\x08\x12\x10\n\x08\x65xponent\x18\x03 \x01(\x02\x12\r\n\x05\x62\x65ta2\x18\x04 \x01(\x02\x12\x0f\n\x07\x65psilon\x18\x05 \x01(\x02\"m\n\x18\x42oundedAdagradParameters\x12 \n\x18update_accumulator_first\x18\x01 \x01(\x08\x12\x16\n\x0emax_var_update\x18\x02 \x01(\x02\x12\x17\n\x0fmax_accumulator\x18\x03 \x01(\x02\"%\n#StochasticGradientDescentParameters\"\xb6\x01\n\x0e\x46trlParameters\x12\n\n\x02l1\x18\x01 \x01(\x02\x12\n\n\x02l2\x18\x02 \x01(\x02\x12\x10\n\x08lr_power\x18\x03 \x01(\x02\x12\x0c\n\x04\x62\x65ta\x18\x07 \x01(\x02\x12\x1d\n\x15multiply_linear_by_lr\x18\x06 \x01(\x08\x12\"\n\x16\x61llow_zero_accumulator\x18\x08 \x01(\x08\x42\x02\x18\x01J\x04\x08\x04\x10\x05J\x04\x08\x05\x10\x06R\rinitial_accumR\x0einitial_linear\"\x99\x01\n\x0e\x41\x64\x61mParameters\x12\r\n\x05\x62\x65ta1\x18\x03 \x01(\x02\x12\r\n\x05\x62\x65ta2\x18\x04 \x01(\x02\x12\x0f\n\x07\x65psilon\x18\x05 \x01(\x02\x12\x19\n\x11use_non_lazy_adam\x18\x08 \x01(\x08\x12\x1b\n\x13use_sum_inside_sqrt\x18\n \x01(\x08J\x04\x08\x06\x10\x07J\x04\x08\x07\x10\x08R\tinitial_mR\tinitial_v\"Q\n\x12MomentumParameters\x12\x10\n\x08momentum\x18\x01 \x01(\x02\x12\x14\n\x0cuse_nesterov\x18\x02 \x01(\x08J\x04\x08\x03\x10\x04R\rinitial_accum\"h\n\x11RmsPropParameters\x12\x0b\n\x03rho\x18\x01 \x01(\x02\x12\x10\n\x08momentum\x18\x02 \x01(\x02\x12\x0f\n\x07\x65psilon\x18\x03 \x01(\x02J\x04\x08\x04\x10\x05J\x04\x08\x05\x10\x06R\ninitial_msR\x0binitial_mom\"\x82\x01\n\x19\x43\x65nteredRmsPropParameters\x12\x0b\n\x03rho\x18\x01 \x01(\x02\x12\x10\n\x08momentum\x18\x02 \x01(\x02\x12\x0f\n\x07\x65psilon\x18\x03 \x01(\x02J\x04\x08\x04\x10\x05J\x04\x08\x05\x10\x06J\x04\x08\x06\x10\x07R\ninitial_msR\x0binitial_momR\ninitial_mg\"\x9d\x03\n\x19MdlAdagradLightParameters\x12\n\n\x02l2\x18\x01 \x01(\x02\x12\x10\n\x08lr_power\x18\x02 \x01(\x02\x12 \n\x18min_servable_mdl_benefit\x18\x03 \x01(\x02\x12\x19\n\x11mdl_mix_in_margin\x18\x04 \x01(\x02\x12 \n\x18mdl_benefit_rampup_coeff\x18\x05 \x01(\x02\x12\x16\n\x0emdl_min_weight\x18\x06 \x01(\x02\x12\x1d\n\x15\x62\x65nefit_revisit_scale\x18\x07 \x01(\x02\x12\x19\n\x11max_event_benefit\x18\x08 \x01(\x02\x12\x19\n\x11max_total_benefit\x18\t \x01(\x02\x12\x16\n\x0emdl_hard_limit\x18\n \x01(\x02\x12\x1e\n\x16hard_limit_min_benefit\x18\x0b \x01(\x08\x12\x16\n\x0emdl_regularize\x18\x0c \x01(\x08J\x04\x08\r\x10\x0eJ\x04\x08\x0e\x10\x0fJ\x04\x08\x0f\x10\x10R\x13initial_accumulatorR\x0einitial_weightR\x0finitial_benefit\"c\n\x12\x41\x64\x61\x64\x65ltaParameters\x12\x0b\n\x03rho\x18\x01 \x01(\x02\x12\x0f\n\x07\x65psilon\x18\x02 \x01(\x02J\x04\x08\x03\x10\x04J\x04\x08\x04\x10\x05R\x13initial_accumulatorR\x0einitial_update\"N\n\x19ProximalAdagradParameters\x12\n\n\x02l1\x18\x01 \x01(\x02\x12\n\n\x02l2\x18\x02 \x01(\x02J\x04\x08\x03\x10\x04R\x13initial_accumulator\"I\n\x14OnlineYogiParameters\x12\n\n\x02l1\x18\x01 \x01(\x02\x12\n\n\x02l2\x18\x02 \x01(\x02\x12\r\n\x05\x62\x65ta2\x18\x03 \x01(\x02J\x04\x08\x06\x10\x07J\x04\x08\x07\x10\x08\"k\n\x16ProximalYogiParameters\x12\n\n\x02l1\x18\x01 \x01(\x02\x12\n\n\x02l2\x18\x02 \x01(\x02\x12\r\n\x05\x62\x65ta1\x18\x03 \x01(\x02\x12\r\n\x05\x62\x65ta2\x18\x04 \x01(\x02\x12\x0f\n\x07\x65psilon\x18\x05 \x01(\x02J\x04\x08\x08\x10\tJ\x04\x08\t\x10\n\"r\n\x1c\x46requencyEstimatorParameters\x12\x0b\n\x03tau\x18\x01 \x01(\x02\x12\x11\n\tmax_delta\x18\x02 \x01(\x02\x12\x19\n\x11outlier_threshold\x18\x03 \x01(\x02\x12\x17\n\x0fweight_exponent\x18\x04 \x01(\x02\"J\n\x1cUserDefinedProgramParameters\x12$\n\x07program\x18\x01 \x01(\x0b\x32\x13.xla.HloModuleProtoJ\x04\x08\x02\x10\x03\"\x12\n\x10\x41ssignParameters\"R\n\x1aGradientAccumulationStatus\"4\n\x06Status\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x0b\n\x07\x45NABLED\x10\x01\x12\x0c\n\x08\x44ISABLED\x10\x02\"S\n\x1bLowDimensionalPackingStatus\"4\n\x06Status\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x0b\n\x07\x45NABLED\x10\x01\x12\x0c\n\x08\x44ISABLED\x10\x02\"\x9b\x01\n\x1dHotIdReplicationConfiguration\x12\x44\n\x06status\x18\x01 \x01(\x0e\x32\x34.tensorflow.tpu.HotIdReplicationConfiguration.Status\"4\n\x06Status\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x0b\n\x07\x45NABLED\x10\x01\x12\x0c\n\x08\x44ISABLED\x10\x02\"\xd4\r\n\x16OptimizationParameters\x12\x33\n\rlearning_rate\x18\r \x01(\x0b\x32\x1c.tensorflow.tpu.LearningRate\x12\x37\n\x0f\x63lipping_limits\x18\x02 \x01(\x0b\x32\x1e.tensorflow.tpu.ClippingLimits\x12@\n\x18gradient_clipping_limits\x18\x07 \x01(\x0b\x32\x1e.tensorflow.tpu.ClippingLimits\x12\x1b\n\x13weight_decay_factor\x18\x10 \x01(\x02\x12\x35\n-multiply_weight_decay_factor_by_learning_rate\x18\x16 \x01(\x08\x12\x45\n\x16simulated_quantization\x18\x1b \x01(\x0b\x32%.tensorflow.tpu.SimulatedQuantization\x12W\n\x1cgradient_accumulation_status\x18\x11 \x01(\x0e\x32\x31.tensorflow.tpu.GradientAccumulationStatus.Status\x12Z\n\x1elow_dimensional_packing_status\x18\x1c \x01(\x0e\x32\x32.tensorflow.tpu.LowDimensionalPackingStatus.Status\x12W\n hot_id_replication_configuration\x18\x12 \x01(\x0b\x32-.tensorflow.tpu.HotIdReplicationConfiguration\x12\x34\n\x07\x61\x64\x61grad\x18\x03 \x01(\x0b\x32!.tensorflow.tpu.AdagradParametersH\x00\x12\x45\n\x10\x61\x64\x61grad_momentum\x18\x1a \x01(\x0b\x32).tensorflow.tpu.AdagradMomentumParametersH\x00\x12\x43\n\x0f\x62ounded_adagrad\x18\x13 \x01(\x0b\x32(.tensorflow.tpu.BoundedAdagradParametersH\x00\x12Z\n\x1bstochastic_gradient_descent\x18\x04 \x01(\x0b\x32\x33.tensorflow.tpu.StochasticGradientDescentParametersH\x00\x12.\n\x04\x66trl\x18\x05 \x01(\x0b\x32\x1e.tensorflow.tpu.FtrlParametersH\x00\x12.\n\x04\x61\x64\x61m\x18\x06 \x01(\x0b\x32\x1e.tensorflow.tpu.AdamParametersH\x00\x12\x36\n\x08momentum\x18\x08 \x01(\x0b\x32\".tensorflow.tpu.MomentumParametersH\x00\x12\x35\n\x08rms_prop\x18\t \x01(\x0b\x32!.tensorflow.tpu.RmsPropParametersH\x00\x12\x46\n\x11\x63\x65ntered_rms_prop\x18\n \x01(\x0b\x32).tensorflow.tpu.CenteredRmsPropParametersH\x00\x12\x46\n\x11mdl_adagrad_light\x18\x0b \x01(\x0b\x32).tensorflow.tpu.MdlAdagradLightParametersH\x00\x12\x36\n\x08\x61\x64\x61\x64\x65lta\x18\x0c \x01(\x0b\x32\".tensorflow.tpu.AdadeltaParametersH\x00\x12\x45\n\x10proximal_adagrad\x18\x0e \x01(\x0b\x32).tensorflow.tpu.ProximalAdagradParametersH\x00\x12;\n\x0bonline_yogi\x18\x14 \x01(\x0b\x32$.tensorflow.tpu.OnlineYogiParametersH\x00\x12?\n\rproximal_yogi\x18\x15 \x01(\x0b\x32&.tensorflow.tpu.ProximalYogiParametersH\x00\x12K\n\x13\x66requency_estimator\x18\x17 \x01(\x0b\x32,.tensorflow.tpu.FrequencyEstimatorParametersH\x00\x12L\n\x14user_defined_program\x18\x18 \x01(\x0b\x32,.tensorflow.tpu.UserDefinedProgramParametersH\x00\x12\x32\n\x06\x61ssign\x18\x19 \x01(\x0b\x32 .tensorflow.tpu.AssignParametersH\x00\x42\x0c\n\nparametersJ\x04\x08\x01\x10\x02J\x04\x08\x0f\x10\x10\"\x9e\x02\n\x1aStateVariableSpecification\x12\x0c\n\x04name\x18\x01 \x01(\t\x12N\n\x0cuser_defined\x18\x02 \x01(\x0b\x32\x36.tensorflow.tpu.StateVariableSpecification.UserDefinedH\x00\x12Y\n\x12\x66ill_with_constant\x18\x03 \x01(\x0b\x32;.tensorflow.tpu.StateVariableSpecification.FillWithConstantH\x00\x1a\x13\n\x0bUserDefinedJ\x04\x08\x01\x10\x02\x1a)\n\x10\x46illWithConstant\x12\x15\n\rinitial_value\x18\x01 \x01(\x01\x42\x07\n\x05usageb\x06proto3')
  ,
  dependencies=[google_dot_protobuf_dot_wrappers__pb2.DESCRIPTOR,tensorflow_dot_compiler_dot_xla_dot_service_dot_hlo__pb2.DESCRIPTOR,])



_GRADIENTACCUMULATIONSTATUS_STATUS = _descriptor.EnumDescriptor(
  name='Status',
  full_name='tensorflow.tpu.GradientAccumulationStatus.Status',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ENABLED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DISABLED', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2527,
  serialized_end=2579,
)
_sym_db.RegisterEnumDescriptor(_GRADIENTACCUMULATIONSTATUS_STATUS)

_LOWDIMENSIONALPACKINGSTATUS_STATUS = _descriptor.EnumDescriptor(
  name='Status',
  full_name='tensorflow.tpu.LowDimensionalPackingStatus.Status',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ENABLED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DISABLED', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2527,
  serialized_end=2579,
)
_sym_db.RegisterEnumDescriptor(_LOWDIMENSIONALPACKINGSTATUS_STATUS)

_HOTIDREPLICATIONCONFIGURATION_STATUS = _descriptor.EnumDescriptor(
  name='Status',
  full_name='tensorflow.tpu.HotIdReplicationConfiguration.Status',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ENABLED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DISABLED', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2527,
  serialized_end=2579,
)
_sym_db.RegisterEnumDescriptor(_HOTIDREPLICATIONCONFIGURATION_STATUS)


_CLIPPINGLIMITS = _descriptor.Descriptor(
  name='ClippingLimits',
  full_name='tensorflow.tpu.ClippingLimits',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='lower', full_name='tensorflow.tpu.ClippingLimits.lower', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='upper', full_name='tensorflow.tpu.ClippingLimits.upper', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=153,
  serialized_end=257,
)


_SIMULATEDQUANTIZATION = _descriptor.Descriptor(
  name='SimulatedQuantization',
  full_name='tensorflow.tpu.SimulatedQuantization',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='tensorflow.tpu.SimulatedQuantization.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='clipping_limits', full_name='tensorflow.tpu.SimulatedQuantization.clipping_limits', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num_buckets', full_name='tensorflow.tpu.SimulatedQuantization.num_buckets', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=259,
  serialized_end=377,
)


_DYNAMICLEARNINGRATE = _descriptor.Descriptor(
  name='DynamicLearningRate',
  full_name='tensorflow.tpu.DynamicLearningRate',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag', full_name='tensorflow.tpu.DynamicLearningRate.tag', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=379,
  serialized_end=413,
)


_LEARNINGRATE = _descriptor.Descriptor(
  name='LearningRate',
  full_name='tensorflow.tpu.LearningRate',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='constant', full_name='tensorflow.tpu.LearningRate.constant', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dynamic', full_name='tensorflow.tpu.LearningRate.dynamic', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='learning_rate', full_name='tensorflow.tpu.LearningRate.learning_rate',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=415,
  serialized_end=522,
)


_ADAGRADPARAMETERS = _descriptor.Descriptor(
  name='AdagradParameters',
  full_name='tensorflow.tpu.AdagradParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=524,
  serialized_end=570,
)


_ADAGRADMOMENTUMPARAMETERS = _descriptor.Descriptor(
  name='AdagradMomentumParameters',
  full_name='tensorflow.tpu.AdagradMomentumParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='momentum', full_name='tensorflow.tpu.AdagradMomentumParameters.momentum', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='use_nesterov', full_name='tensorflow.tpu.AdagradMomentumParameters.use_nesterov', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exponent', full_name='tensorflow.tpu.AdagradMomentumParameters.exponent', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='beta2', full_name='tensorflow.tpu.AdagradMomentumParameters.beta2', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='epsilon', full_name='tensorflow.tpu.AdagradMomentumParameters.epsilon', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=572,
  serialized_end=689,
)


_BOUNDEDADAGRADPARAMETERS = _descriptor.Descriptor(
  name='BoundedAdagradParameters',
  full_name='tensorflow.tpu.BoundedAdagradParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='update_accumulator_first', full_name='tensorflow.tpu.BoundedAdagradParameters.update_accumulator_first', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_var_update', full_name='tensorflow.tpu.BoundedAdagradParameters.max_var_update', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_accumulator', full_name='tensorflow.tpu.BoundedAdagradParameters.max_accumulator', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=691,
  serialized_end=800,
)


_STOCHASTICGRADIENTDESCENTPARAMETERS = _descriptor.Descriptor(
  name='StochasticGradientDescentParameters',
  full_name='tensorflow.tpu.StochasticGradientDescentParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=802,
  serialized_end=839,
)


_FTRLPARAMETERS = _descriptor.Descriptor(
  name='FtrlParameters',
  full_name='tensorflow.tpu.FtrlParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='l1', full_name='tensorflow.tpu.FtrlParameters.l1', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='l2', full_name='tensorflow.tpu.FtrlParameters.l2', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lr_power', full_name='tensorflow.tpu.FtrlParameters.lr_power', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='beta', full_name='tensorflow.tpu.FtrlParameters.beta', index=3,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='multiply_linear_by_lr', full_name='tensorflow.tpu.FtrlParameters.multiply_linear_by_lr', index=4,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_zero_accumulator', full_name='tensorflow.tpu.FtrlParameters.allow_zero_accumulator', index=5,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\030\001'), file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=842,
  serialized_end=1024,
)


_ADAMPARAMETERS = _descriptor.Descriptor(
  name='AdamParameters',
  full_name='tensorflow.tpu.AdamParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='beta1', full_name='tensorflow.tpu.AdamParameters.beta1', index=0,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='beta2', full_name='tensorflow.tpu.AdamParameters.beta2', index=1,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='epsilon', full_name='tensorflow.tpu.AdamParameters.epsilon', index=2,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='use_non_lazy_adam', full_name='tensorflow.tpu.AdamParameters.use_non_lazy_adam', index=3,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='use_sum_inside_sqrt', full_name='tensorflow.tpu.AdamParameters.use_sum_inside_sqrt', index=4,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1027,
  serialized_end=1180,
)


_MOMENTUMPARAMETERS = _descriptor.Descriptor(
  name='MomentumParameters',
  full_name='tensorflow.tpu.MomentumParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='momentum', full_name='tensorflow.tpu.MomentumParameters.momentum', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='use_nesterov', full_name='tensorflow.tpu.MomentumParameters.use_nesterov', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1182,
  serialized_end=1263,
)


_RMSPROPPARAMETERS = _descriptor.Descriptor(
  name='RmsPropParameters',
  full_name='tensorflow.tpu.RmsPropParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rho', full_name='tensorflow.tpu.RmsPropParameters.rho', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='momentum', full_name='tensorflow.tpu.RmsPropParameters.momentum', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='epsilon', full_name='tensorflow.tpu.RmsPropParameters.epsilon', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1265,
  serialized_end=1369,
)


_CENTEREDRMSPROPPARAMETERS = _descriptor.Descriptor(
  name='CenteredRmsPropParameters',
  full_name='tensorflow.tpu.CenteredRmsPropParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rho', full_name='tensorflow.tpu.CenteredRmsPropParameters.rho', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='momentum', full_name='tensorflow.tpu.CenteredRmsPropParameters.momentum', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='epsilon', full_name='tensorflow.tpu.CenteredRmsPropParameters.epsilon', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1372,
  serialized_end=1502,
)


_MDLADAGRADLIGHTPARAMETERS = _descriptor.Descriptor(
  name='MdlAdagradLightParameters',
  full_name='tensorflow.tpu.MdlAdagradLightParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='l2', full_name='tensorflow.tpu.MdlAdagradLightParameters.l2', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lr_power', full_name='tensorflow.tpu.MdlAdagradLightParameters.lr_power', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_servable_mdl_benefit', full_name='tensorflow.tpu.MdlAdagradLightParameters.min_servable_mdl_benefit', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mdl_mix_in_margin', full_name='tensorflow.tpu.MdlAdagradLightParameters.mdl_mix_in_margin', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mdl_benefit_rampup_coeff', full_name='tensorflow.tpu.MdlAdagradLightParameters.mdl_benefit_rampup_coeff', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mdl_min_weight', full_name='tensorflow.tpu.MdlAdagradLightParameters.mdl_min_weight', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='benefit_revisit_scale', full_name='tensorflow.tpu.MdlAdagradLightParameters.benefit_revisit_scale', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_event_benefit', full_name='tensorflow.tpu.MdlAdagradLightParameters.max_event_benefit', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_total_benefit', full_name='tensorflow.tpu.MdlAdagradLightParameters.max_total_benefit', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mdl_hard_limit', full_name='tensorflow.tpu.MdlAdagradLightParameters.mdl_hard_limit', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hard_limit_min_benefit', full_name='tensorflow.tpu.MdlAdagradLightParameters.hard_limit_min_benefit', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mdl_regularize', full_name='tensorflow.tpu.MdlAdagradLightParameters.mdl_regularize', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1505,
  serialized_end=1918,
)


_ADADELTAPARAMETERS = _descriptor.Descriptor(
  name='AdadeltaParameters',
  full_name='tensorflow.tpu.AdadeltaParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rho', full_name='tensorflow.tpu.AdadeltaParameters.rho', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='epsilon', full_name='tensorflow.tpu.AdadeltaParameters.epsilon', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1920,
  serialized_end=2019,
)


_PROXIMALADAGRADPARAMETERS = _descriptor.Descriptor(
  name='ProximalAdagradParameters',
  full_name='tensorflow.tpu.ProximalAdagradParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='l1', full_name='tensorflow.tpu.ProximalAdagradParameters.l1', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='l2', full_name='tensorflow.tpu.ProximalAdagradParameters.l2', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2021,
  serialized_end=2099,
)


_ONLINEYOGIPARAMETERS = _descriptor.Descriptor(
  name='OnlineYogiParameters',
  full_name='tensorflow.tpu.OnlineYogiParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='l1', full_name='tensorflow.tpu.OnlineYogiParameters.l1', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='l2', full_name='tensorflow.tpu.OnlineYogiParameters.l2', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='beta2', full_name='tensorflow.tpu.OnlineYogiParameters.beta2', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2101,
  serialized_end=2174,
)


_PROXIMALYOGIPARAMETERS = _descriptor.Descriptor(
  name='ProximalYogiParameters',
  full_name='tensorflow.tpu.ProximalYogiParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='l1', full_name='tensorflow.tpu.ProximalYogiParameters.l1', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='l2', full_name='tensorflow.tpu.ProximalYogiParameters.l2', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='beta1', full_name='tensorflow.tpu.ProximalYogiParameters.beta1', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='beta2', full_name='tensorflow.tpu.ProximalYogiParameters.beta2', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='epsilon', full_name='tensorflow.tpu.ProximalYogiParameters.epsilon', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2176,
  serialized_end=2283,
)


_FREQUENCYESTIMATORPARAMETERS = _descriptor.Descriptor(
  name='FrequencyEstimatorParameters',
  full_name='tensorflow.tpu.FrequencyEstimatorParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tau', full_name='tensorflow.tpu.FrequencyEstimatorParameters.tau', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_delta', full_name='tensorflow.tpu.FrequencyEstimatorParameters.max_delta', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='outlier_threshold', full_name='tensorflow.tpu.FrequencyEstimatorParameters.outlier_threshold', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='weight_exponent', full_name='tensorflow.tpu.FrequencyEstimatorParameters.weight_exponent', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2285,
  serialized_end=2399,
)


_USERDEFINEDPROGRAMPARAMETERS = _descriptor.Descriptor(
  name='UserDefinedProgramParameters',
  full_name='tensorflow.tpu.UserDefinedProgramParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='program', full_name='tensorflow.tpu.UserDefinedProgramParameters.program', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2401,
  serialized_end=2475,
)


_ASSIGNPARAMETERS = _descriptor.Descriptor(
  name='AssignParameters',
  full_name='tensorflow.tpu.AssignParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2477,
  serialized_end=2495,
)


_GRADIENTACCUMULATIONSTATUS = _descriptor.Descriptor(
  name='GradientAccumulationStatus',
  full_name='tensorflow.tpu.GradientAccumulationStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _GRADIENTACCUMULATIONSTATUS_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2497,
  serialized_end=2579,
)


_LOWDIMENSIONALPACKINGSTATUS = _descriptor.Descriptor(
  name='LowDimensionalPackingStatus',
  full_name='tensorflow.tpu.LowDimensionalPackingStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _LOWDIMENSIONALPACKINGSTATUS_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2581,
  serialized_end=2664,
)


_HOTIDREPLICATIONCONFIGURATION = _descriptor.Descriptor(
  name='HotIdReplicationConfiguration',
  full_name='tensorflow.tpu.HotIdReplicationConfiguration',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='tensorflow.tpu.HotIdReplicationConfiguration.status', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _HOTIDREPLICATIONCONFIGURATION_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2667,
  serialized_end=2822,
)


_OPTIMIZATIONPARAMETERS = _descriptor.Descriptor(
  name='OptimizationParameters',
  full_name='tensorflow.tpu.OptimizationParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='learning_rate', full_name='tensorflow.tpu.OptimizationParameters.learning_rate', index=0,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='clipping_limits', full_name='tensorflow.tpu.OptimizationParameters.clipping_limits', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='gradient_clipping_limits', full_name='tensorflow.tpu.OptimizationParameters.gradient_clipping_limits', index=2,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='weight_decay_factor', full_name='tensorflow.tpu.OptimizationParameters.weight_decay_factor', index=3,
      number=16, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='multiply_weight_decay_factor_by_learning_rate', full_name='tensorflow.tpu.OptimizationParameters.multiply_weight_decay_factor_by_learning_rate', index=4,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='simulated_quantization', full_name='tensorflow.tpu.OptimizationParameters.simulated_quantization', index=5,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='gradient_accumulation_status', full_name='tensorflow.tpu.OptimizationParameters.gradient_accumulation_status', index=6,
      number=17, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='low_dimensional_packing_status', full_name='tensorflow.tpu.OptimizationParameters.low_dimensional_packing_status', index=7,
      number=28, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hot_id_replication_configuration', full_name='tensorflow.tpu.OptimizationParameters.hot_id_replication_configuration', index=8,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adagrad', full_name='tensorflow.tpu.OptimizationParameters.adagrad', index=9,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adagrad_momentum', full_name='tensorflow.tpu.OptimizationParameters.adagrad_momentum', index=10,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bounded_adagrad', full_name='tensorflow.tpu.OptimizationParameters.bounded_adagrad', index=11,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stochastic_gradient_descent', full_name='tensorflow.tpu.OptimizationParameters.stochastic_gradient_descent', index=12,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ftrl', full_name='tensorflow.tpu.OptimizationParameters.ftrl', index=13,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adam', full_name='tensorflow.tpu.OptimizationParameters.adam', index=14,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='momentum', full_name='tensorflow.tpu.OptimizationParameters.momentum', index=15,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rms_prop', full_name='tensorflow.tpu.OptimizationParameters.rms_prop', index=16,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='centered_rms_prop', full_name='tensorflow.tpu.OptimizationParameters.centered_rms_prop', index=17,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mdl_adagrad_light', full_name='tensorflow.tpu.OptimizationParameters.mdl_adagrad_light', index=18,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adadelta', full_name='tensorflow.tpu.OptimizationParameters.adadelta', index=19,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='proximal_adagrad', full_name='tensorflow.tpu.OptimizationParameters.proximal_adagrad', index=20,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='online_yogi', full_name='tensorflow.tpu.OptimizationParameters.online_yogi', index=21,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='proximal_yogi', full_name='tensorflow.tpu.OptimizationParameters.proximal_yogi', index=22,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='frequency_estimator', full_name='tensorflow.tpu.OptimizationParameters.frequency_estimator', index=23,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_defined_program', full_name='tensorflow.tpu.OptimizationParameters.user_defined_program', index=24,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='assign', full_name='tensorflow.tpu.OptimizationParameters.assign', index=25,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='parameters', full_name='tensorflow.tpu.OptimizationParameters.parameters',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=2825,
  serialized_end=4573,
)


_STATEVARIABLESPECIFICATION_USERDEFINED = _descriptor.Descriptor(
  name='UserDefined',
  full_name='tensorflow.tpu.StateVariableSpecification.UserDefined',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4791,
  serialized_end=4810,
)

_STATEVARIABLESPECIFICATION_FILLWITHCONSTANT = _descriptor.Descriptor(
  name='FillWithConstant',
  full_name='tensorflow.tpu.StateVariableSpecification.FillWithConstant',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='initial_value', full_name='tensorflow.tpu.StateVariableSpecification.FillWithConstant.initial_value', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4812,
  serialized_end=4853,
)

_STATEVARIABLESPECIFICATION = _descriptor.Descriptor(
  name='StateVariableSpecification',
  full_name='tensorflow.tpu.StateVariableSpecification',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='tensorflow.tpu.StateVariableSpecification.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_defined', full_name='tensorflow.tpu.StateVariableSpecification.user_defined', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fill_with_constant', full_name='tensorflow.tpu.StateVariableSpecification.fill_with_constant', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_STATEVARIABLESPECIFICATION_USERDEFINED, _STATEVARIABLESPECIFICATION_FILLWITHCONSTANT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='usage', full_name='tensorflow.tpu.StateVariableSpecification.usage',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=4576,
  serialized_end=4862,
)

_CLIPPINGLIMITS.fields_by_name['lower'].message_type = google_dot_protobuf_dot_wrappers__pb2._FLOATVALUE
_CLIPPINGLIMITS.fields_by_name['upper'].message_type = google_dot_protobuf_dot_wrappers__pb2._FLOATVALUE
_SIMULATEDQUANTIZATION.fields_by_name['clipping_limits'].message_type = _CLIPPINGLIMITS
_LEARNINGRATE.fields_by_name['dynamic'].message_type = _DYNAMICLEARNINGRATE
_LEARNINGRATE.oneofs_by_name['learning_rate'].fields.append(
  _LEARNINGRATE.fields_by_name['constant'])
_LEARNINGRATE.fields_by_name['constant'].containing_oneof = _LEARNINGRATE.oneofs_by_name['learning_rate']
_LEARNINGRATE.oneofs_by_name['learning_rate'].fields.append(
  _LEARNINGRATE.fields_by_name['dynamic'])
_LEARNINGRATE.fields_by_name['dynamic'].containing_oneof = _LEARNINGRATE.oneofs_by_name['learning_rate']
_USERDEFINEDPROGRAMPARAMETERS.fields_by_name['program'].message_type = tensorflow_dot_compiler_dot_xla_dot_service_dot_hlo__pb2._HLOMODULEPROTO
_GRADIENTACCUMULATIONSTATUS_STATUS.containing_type = _GRADIENTACCUMULATIONSTATUS
_LOWDIMENSIONALPACKINGSTATUS_STATUS.containing_type = _LOWDIMENSIONALPACKINGSTATUS
_HOTIDREPLICATIONCONFIGURATION.fields_by_name['status'].enum_type = _HOTIDREPLICATIONCONFIGURATION_STATUS
_HOTIDREPLICATIONCONFIGURATION_STATUS.containing_type = _HOTIDREPLICATIONCONFIGURATION
_OPTIMIZATIONPARAMETERS.fields_by_name['learning_rate'].message_type = _LEARNINGRATE
_OPTIMIZATIONPARAMETERS.fields_by_name['clipping_limits'].message_type = _CLIPPINGLIMITS
_OPTIMIZATIONPARAMETERS.fields_by_name['gradient_clipping_limits'].message_type = _CLIPPINGLIMITS
_OPTIMIZATIONPARAMETERS.fields_by_name['simulated_quantization'].message_type = _SIMULATEDQUANTIZATION
_OPTIMIZATIONPARAMETERS.fields_by_name['gradient_accumulation_status'].enum_type = _GRADIENTACCUMULATIONSTATUS_STATUS
_OPTIMIZATIONPARAMETERS.fields_by_name['low_dimensional_packing_status'].enum_type = _LOWDIMENSIONALPACKINGSTATUS_STATUS
_OPTIMIZATIONPARAMETERS.fields_by_name['hot_id_replication_configuration'].message_type = _HOTIDREPLICATIONCONFIGURATION
_OPTIMIZATIONPARAMETERS.fields_by_name['adagrad'].message_type = _ADAGRADPARAMETERS
_OPTIMIZATIONPARAMETERS.fields_by_name['adagrad_momentum'].message_type = _ADAGRADMOMENTUMPARAMETERS
_OPTIMIZATIONPARAMETERS.fields_by_name['bounded_adagrad'].message_type = _BOUNDEDADAGRADPARAMETERS
_OPTIMIZATIONPARAMETERS.fields_by_name['stochastic_gradient_descent'].message_type = _STOCHASTICGRADIENTDESCENTPARAMETERS
_OPTIMIZATIONPARAMETERS.fields_by_name['ftrl'].message_type = _FTRLPARAMETERS
_OPTIMIZATIONPARAMETERS.fields_by_name['adam'].message_type = _ADAMPARAMETERS
_OPTIMIZATIONPARAMETERS.fields_by_name['momentum'].message_type = _MOMENTUMPARAMETERS
_OPTIMIZATIONPARAMETERS.fields_by_name['rms_prop'].message_type = _RMSPROPPARAMETERS
_OPTIMIZATIONPARAMETERS.fields_by_name['centered_rms_prop'].message_type = _CENTEREDRMSPROPPARAMETERS
_OPTIMIZATIONPARAMETERS.fields_by_name['mdl_adagrad_light'].message_type = _MDLADAGRADLIGHTPARAMETERS
_OPTIMIZATIONPARAMETERS.fields_by_name['adadelta'].message_type = _ADADELTAPARAMETERS
_OPTIMIZATIONPARAMETERS.fields_by_name['proximal_adagrad'].message_type = _PROXIMALADAGRADPARAMETERS
_OPTIMIZATIONPARAMETERS.fields_by_name['online_yogi'].message_type = _ONLINEYOGIPARAMETERS
_OPTIMIZATIONPARAMETERS.fields_by_name['proximal_yogi'].message_type = _PROXIMALYOGIPARAMETERS
_OPTIMIZATIONPARAMETERS.fields_by_name['frequency_estimator'].message_type = _FREQUENCYESTIMATORPARAMETERS
_OPTIMIZATIONPARAMETERS.fields_by_name['user_defined_program'].message_type = _USERDEFINEDPROGRAMPARAMETERS
_OPTIMIZATIONPARAMETERS.fields_by_name['assign'].message_type = _ASSIGNPARAMETERS
_OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters'].fields.append(
  _OPTIMIZATIONPARAMETERS.fields_by_name['adagrad'])
_OPTIMIZATIONPARAMETERS.fields_by_name['adagrad'].containing_oneof = _OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters']
_OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters'].fields.append(
  _OPTIMIZATIONPARAMETERS.fields_by_name['adagrad_momentum'])
_OPTIMIZATIONPARAMETERS.fields_by_name['adagrad_momentum'].containing_oneof = _OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters']
_OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters'].fields.append(
  _OPTIMIZATIONPARAMETERS.fields_by_name['bounded_adagrad'])
_OPTIMIZATIONPARAMETERS.fields_by_name['bounded_adagrad'].containing_oneof = _OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters']
_OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters'].fields.append(
  _OPTIMIZATIONPARAMETERS.fields_by_name['stochastic_gradient_descent'])
_OPTIMIZATIONPARAMETERS.fields_by_name['stochastic_gradient_descent'].containing_oneof = _OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters']
_OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters'].fields.append(
  _OPTIMIZATIONPARAMETERS.fields_by_name['ftrl'])
_OPTIMIZATIONPARAMETERS.fields_by_name['ftrl'].containing_oneof = _OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters']
_OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters'].fields.append(
  _OPTIMIZATIONPARAMETERS.fields_by_name['adam'])
_OPTIMIZATIONPARAMETERS.fields_by_name['adam'].containing_oneof = _OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters']
_OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters'].fields.append(
  _OPTIMIZATIONPARAMETERS.fields_by_name['momentum'])
_OPTIMIZATIONPARAMETERS.fields_by_name['momentum'].containing_oneof = _OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters']
_OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters'].fields.append(
  _OPTIMIZATIONPARAMETERS.fields_by_name['rms_prop'])
_OPTIMIZATIONPARAMETERS.fields_by_name['rms_prop'].containing_oneof = _OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters']
_OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters'].fields.append(
  _OPTIMIZATIONPARAMETERS.fields_by_name['centered_rms_prop'])
_OPTIMIZATIONPARAMETERS.fields_by_name['centered_rms_prop'].containing_oneof = _OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters']
_OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters'].fields.append(
  _OPTIMIZATIONPARAMETERS.fields_by_name['mdl_adagrad_light'])
_OPTIMIZATIONPARAMETERS.fields_by_name['mdl_adagrad_light'].containing_oneof = _OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters']
_OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters'].fields.append(
  _OPTIMIZATIONPARAMETERS.fields_by_name['adadelta'])
_OPTIMIZATIONPARAMETERS.fields_by_name['adadelta'].containing_oneof = _OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters']
_OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters'].fields.append(
  _OPTIMIZATIONPARAMETERS.fields_by_name['proximal_adagrad'])
_OPTIMIZATIONPARAMETERS.fields_by_name['proximal_adagrad'].containing_oneof = _OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters']
_OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters'].fields.append(
  _OPTIMIZATIONPARAMETERS.fields_by_name['online_yogi'])
_OPTIMIZATIONPARAMETERS.fields_by_name['online_yogi'].containing_oneof = _OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters']
_OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters'].fields.append(
  _OPTIMIZATIONPARAMETERS.fields_by_name['proximal_yogi'])
_OPTIMIZATIONPARAMETERS.fields_by_name['proximal_yogi'].containing_oneof = _OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters']
_OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters'].fields.append(
  _OPTIMIZATIONPARAMETERS.fields_by_name['frequency_estimator'])
_OPTIMIZATIONPARAMETERS.fields_by_name['frequency_estimator'].containing_oneof = _OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters']
_OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters'].fields.append(
  _OPTIMIZATIONPARAMETERS.fields_by_name['user_defined_program'])
_OPTIMIZATIONPARAMETERS.fields_by_name['user_defined_program'].containing_oneof = _OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters']
_OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters'].fields.append(
  _OPTIMIZATIONPARAMETERS.fields_by_name['assign'])
_OPTIMIZATIONPARAMETERS.fields_by_name['assign'].containing_oneof = _OPTIMIZATIONPARAMETERS.oneofs_by_name['parameters']
_STATEVARIABLESPECIFICATION_USERDEFINED.containing_type = _STATEVARIABLESPECIFICATION
_STATEVARIABLESPECIFICATION_FILLWITHCONSTANT.containing_type = _STATEVARIABLESPECIFICATION
_STATEVARIABLESPECIFICATION.fields_by_name['user_defined'].message_type = _STATEVARIABLESPECIFICATION_USERDEFINED
_STATEVARIABLESPECIFICATION.fields_by_name['fill_with_constant'].message_type = _STATEVARIABLESPECIFICATION_FILLWITHCONSTANT
_STATEVARIABLESPECIFICATION.oneofs_by_name['usage'].fields.append(
  _STATEVARIABLESPECIFICATION.fields_by_name['user_defined'])
_STATEVARIABLESPECIFICATION.fields_by_name['user_defined'].containing_oneof = _STATEVARIABLESPECIFICATION.oneofs_by_name['usage']
_STATEVARIABLESPECIFICATION.oneofs_by_name['usage'].fields.append(
  _STATEVARIABLESPECIFICATION.fields_by_name['fill_with_constant'])
_STATEVARIABLESPECIFICATION.fields_by_name['fill_with_constant'].containing_oneof = _STATEVARIABLESPECIFICATION.oneofs_by_name['usage']
DESCRIPTOR.message_types_by_name['ClippingLimits'] = _CLIPPINGLIMITS
DESCRIPTOR.message_types_by_name['SimulatedQuantization'] = _SIMULATEDQUANTIZATION
DESCRIPTOR.message_types_by_name['DynamicLearningRate'] = _DYNAMICLEARNINGRATE
DESCRIPTOR.message_types_by_name['LearningRate'] = _LEARNINGRATE
DESCRIPTOR.message_types_by_name['AdagradParameters'] = _ADAGRADPARAMETERS
DESCRIPTOR.message_types_by_name['AdagradMomentumParameters'] = _ADAGRADMOMENTUMPARAMETERS
DESCRIPTOR.message_types_by_name['BoundedAdagradParameters'] = _BOUNDEDADAGRADPARAMETERS
DESCRIPTOR.message_types_by_name['StochasticGradientDescentParameters'] = _STOCHASTICGRADIENTDESCENTPARAMETERS
DESCRIPTOR.message_types_by_name['FtrlParameters'] = _FTRLPARAMETERS
DESCRIPTOR.message_types_by_name['AdamParameters'] = _ADAMPARAMETERS
DESCRIPTOR.message_types_by_name['MomentumParameters'] = _MOMENTUMPARAMETERS
DESCRIPTOR.message_types_by_name['RmsPropParameters'] = _RMSPROPPARAMETERS
DESCRIPTOR.message_types_by_name['CenteredRmsPropParameters'] = _CENTEREDRMSPROPPARAMETERS
DESCRIPTOR.message_types_by_name['MdlAdagradLightParameters'] = _MDLADAGRADLIGHTPARAMETERS
DESCRIPTOR.message_types_by_name['AdadeltaParameters'] = _ADADELTAPARAMETERS
DESCRIPTOR.message_types_by_name['ProximalAdagradParameters'] = _PROXIMALADAGRADPARAMETERS
DESCRIPTOR.message_types_by_name['OnlineYogiParameters'] = _ONLINEYOGIPARAMETERS
DESCRIPTOR.message_types_by_name['ProximalYogiParameters'] = _PROXIMALYOGIPARAMETERS
DESCRIPTOR.message_types_by_name['FrequencyEstimatorParameters'] = _FREQUENCYESTIMATORPARAMETERS
DESCRIPTOR.message_types_by_name['UserDefinedProgramParameters'] = _USERDEFINEDPROGRAMPARAMETERS
DESCRIPTOR.message_types_by_name['AssignParameters'] = _ASSIGNPARAMETERS
DESCRIPTOR.message_types_by_name['GradientAccumulationStatus'] = _GRADIENTACCUMULATIONSTATUS
DESCRIPTOR.message_types_by_name['LowDimensionalPackingStatus'] = _LOWDIMENSIONALPACKINGSTATUS
DESCRIPTOR.message_types_by_name['HotIdReplicationConfiguration'] = _HOTIDREPLICATIONCONFIGURATION
DESCRIPTOR.message_types_by_name['OptimizationParameters'] = _OPTIMIZATIONPARAMETERS
DESCRIPTOR.message_types_by_name['StateVariableSpecification'] = _STATEVARIABLESPECIFICATION
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ClippingLimits = _reflection.GeneratedProtocolMessageType('ClippingLimits', (_message.Message,), {
  'DESCRIPTOR' : _CLIPPINGLIMITS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.ClippingLimits)
  })
_sym_db.RegisterMessage(ClippingLimits)

SimulatedQuantization = _reflection.GeneratedProtocolMessageType('SimulatedQuantization', (_message.Message,), {
  'DESCRIPTOR' : _SIMULATEDQUANTIZATION,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.SimulatedQuantization)
  })
_sym_db.RegisterMessage(SimulatedQuantization)

DynamicLearningRate = _reflection.GeneratedProtocolMessageType('DynamicLearningRate', (_message.Message,), {
  'DESCRIPTOR' : _DYNAMICLEARNINGRATE,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.DynamicLearningRate)
  })
_sym_db.RegisterMessage(DynamicLearningRate)

LearningRate = _reflection.GeneratedProtocolMessageType('LearningRate', (_message.Message,), {
  'DESCRIPTOR' : _LEARNINGRATE,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.LearningRate)
  })
_sym_db.RegisterMessage(LearningRate)

AdagradParameters = _reflection.GeneratedProtocolMessageType('AdagradParameters', (_message.Message,), {
  'DESCRIPTOR' : _ADAGRADPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.AdagradParameters)
  })
_sym_db.RegisterMessage(AdagradParameters)

AdagradMomentumParameters = _reflection.GeneratedProtocolMessageType('AdagradMomentumParameters', (_message.Message,), {
  'DESCRIPTOR' : _ADAGRADMOMENTUMPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.AdagradMomentumParameters)
  })
_sym_db.RegisterMessage(AdagradMomentumParameters)

BoundedAdagradParameters = _reflection.GeneratedProtocolMessageType('BoundedAdagradParameters', (_message.Message,), {
  'DESCRIPTOR' : _BOUNDEDADAGRADPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.BoundedAdagradParameters)
  })
_sym_db.RegisterMessage(BoundedAdagradParameters)

StochasticGradientDescentParameters = _reflection.GeneratedProtocolMessageType('StochasticGradientDescentParameters', (_message.Message,), {
  'DESCRIPTOR' : _STOCHASTICGRADIENTDESCENTPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.StochasticGradientDescentParameters)
  })
_sym_db.RegisterMessage(StochasticGradientDescentParameters)

FtrlParameters = _reflection.GeneratedProtocolMessageType('FtrlParameters', (_message.Message,), {
  'DESCRIPTOR' : _FTRLPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.FtrlParameters)
  })
_sym_db.RegisterMessage(FtrlParameters)

AdamParameters = _reflection.GeneratedProtocolMessageType('AdamParameters', (_message.Message,), {
  'DESCRIPTOR' : _ADAMPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.AdamParameters)
  })
_sym_db.RegisterMessage(AdamParameters)

MomentumParameters = _reflection.GeneratedProtocolMessageType('MomentumParameters', (_message.Message,), {
  'DESCRIPTOR' : _MOMENTUMPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.MomentumParameters)
  })
_sym_db.RegisterMessage(MomentumParameters)

RmsPropParameters = _reflection.GeneratedProtocolMessageType('RmsPropParameters', (_message.Message,), {
  'DESCRIPTOR' : _RMSPROPPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.RmsPropParameters)
  })
_sym_db.RegisterMessage(RmsPropParameters)

CenteredRmsPropParameters = _reflection.GeneratedProtocolMessageType('CenteredRmsPropParameters', (_message.Message,), {
  'DESCRIPTOR' : _CENTEREDRMSPROPPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.CenteredRmsPropParameters)
  })
_sym_db.RegisterMessage(CenteredRmsPropParameters)

MdlAdagradLightParameters = _reflection.GeneratedProtocolMessageType('MdlAdagradLightParameters', (_message.Message,), {
  'DESCRIPTOR' : _MDLADAGRADLIGHTPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.MdlAdagradLightParameters)
  })
_sym_db.RegisterMessage(MdlAdagradLightParameters)

AdadeltaParameters = _reflection.GeneratedProtocolMessageType('AdadeltaParameters', (_message.Message,), {
  'DESCRIPTOR' : _ADADELTAPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.AdadeltaParameters)
  })
_sym_db.RegisterMessage(AdadeltaParameters)

ProximalAdagradParameters = _reflection.GeneratedProtocolMessageType('ProximalAdagradParameters', (_message.Message,), {
  'DESCRIPTOR' : _PROXIMALADAGRADPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.ProximalAdagradParameters)
  })
_sym_db.RegisterMessage(ProximalAdagradParameters)

OnlineYogiParameters = _reflection.GeneratedProtocolMessageType('OnlineYogiParameters', (_message.Message,), {
  'DESCRIPTOR' : _ONLINEYOGIPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.OnlineYogiParameters)
  })
_sym_db.RegisterMessage(OnlineYogiParameters)

ProximalYogiParameters = _reflection.GeneratedProtocolMessageType('ProximalYogiParameters', (_message.Message,), {
  'DESCRIPTOR' : _PROXIMALYOGIPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.ProximalYogiParameters)
  })
_sym_db.RegisterMessage(ProximalYogiParameters)

FrequencyEstimatorParameters = _reflection.GeneratedProtocolMessageType('FrequencyEstimatorParameters', (_message.Message,), {
  'DESCRIPTOR' : _FREQUENCYESTIMATORPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.FrequencyEstimatorParameters)
  })
_sym_db.RegisterMessage(FrequencyEstimatorParameters)

UserDefinedProgramParameters = _reflection.GeneratedProtocolMessageType('UserDefinedProgramParameters', (_message.Message,), {
  'DESCRIPTOR' : _USERDEFINEDPROGRAMPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.UserDefinedProgramParameters)
  })
_sym_db.RegisterMessage(UserDefinedProgramParameters)

AssignParameters = _reflection.GeneratedProtocolMessageType('AssignParameters', (_message.Message,), {
  'DESCRIPTOR' : _ASSIGNPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.AssignParameters)
  })
_sym_db.RegisterMessage(AssignParameters)

GradientAccumulationStatus = _reflection.GeneratedProtocolMessageType('GradientAccumulationStatus', (_message.Message,), {
  'DESCRIPTOR' : _GRADIENTACCUMULATIONSTATUS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.GradientAccumulationStatus)
  })
_sym_db.RegisterMessage(GradientAccumulationStatus)

LowDimensionalPackingStatus = _reflection.GeneratedProtocolMessageType('LowDimensionalPackingStatus', (_message.Message,), {
  'DESCRIPTOR' : _LOWDIMENSIONALPACKINGSTATUS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.LowDimensionalPackingStatus)
  })
_sym_db.RegisterMessage(LowDimensionalPackingStatus)

HotIdReplicationConfiguration = _reflection.GeneratedProtocolMessageType('HotIdReplicationConfiguration', (_message.Message,), {
  'DESCRIPTOR' : _HOTIDREPLICATIONCONFIGURATION,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.HotIdReplicationConfiguration)
  })
_sym_db.RegisterMessage(HotIdReplicationConfiguration)

OptimizationParameters = _reflection.GeneratedProtocolMessageType('OptimizationParameters', (_message.Message,), {
  'DESCRIPTOR' : _OPTIMIZATIONPARAMETERS,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.OptimizationParameters)
  })
_sym_db.RegisterMessage(OptimizationParameters)

StateVariableSpecification = _reflection.GeneratedProtocolMessageType('StateVariableSpecification', (_message.Message,), {

  'UserDefined' : _reflection.GeneratedProtocolMessageType('UserDefined', (_message.Message,), {
    'DESCRIPTOR' : _STATEVARIABLESPECIFICATION_USERDEFINED,
    '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tpu.StateVariableSpecification.UserDefined)
    })
  ,

  'FillWithConstant' : _reflection.GeneratedProtocolMessageType('FillWithConstant', (_message.Message,), {
    'DESCRIPTOR' : _STATEVARIABLESPECIFICATION_FILLWITHCONSTANT,
    '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
    })
  ,
  'DESCRIPTOR' : _STATEVARIABLESPECIFICATION,
  '__module__' : 'tensorflow.core.protobuf.tpu.optimization_parameters_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.StateVariableSpecification)
  })
_sym_db.RegisterMessage(StateVariableSpecification)
_sym_db.RegisterMessage(StateVariableSpecification.UserDefined)
_sym_db.RegisterMessage(StateVariableSpecification.FillWithConstant)


_FTRLPARAMETERS.fields_by_name['allow_zero_accumulator']._options = None
# @@protoc_insertion_point(module_scope)
