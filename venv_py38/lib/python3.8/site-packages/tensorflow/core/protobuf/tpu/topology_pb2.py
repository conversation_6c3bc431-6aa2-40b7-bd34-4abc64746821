# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/core/protobuf/tpu/topology.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorflow/core/protobuf/tpu/topology.proto',
  package='tensorflow.tpu',
  syntax='proto3',
  serialized_options=_b('\370\001\001'),
  serialized_pb=_b('\n+tensorflow/core/protobuf/tpu/topology.proto\x12\x0etensorflow.tpu\"\x99\x01\n\x12TPUHardwareFeature\x12N\n\x11\x65mbedding_feature\x18\x01 \x01(\x0e\x32\x33.tensorflow.tpu.TPUHardwareFeature.EmbeddingFeature\"3\n\x10\x45mbeddingFeature\x12\x0f\n\x0bUNSUPPORTED\x10\x00\x12\x06\n\x02V1\x10\x01\x12\x06\n\x02V2\x10\x02\"\xb6\x01\n\rTopologyProto\x12\x12\n\nmesh_shape\x18\x01 \x03(\x05\x12\x11\n\tnum_tasks\x18\x02 \x01(\x05\x12 \n\x18num_tpu_devices_per_task\x18\x03 \x01(\x05\x12\x1a\n\x12\x64\x65vice_coordinates\x18\x04 \x03(\x05\x12@\n\x14tpu_hardware_feature\x18\x05 \x01(\x0b\x32\".tensorflow.tpu.TPUHardwareFeatureB\x03\xf8\x01\x01\x62\x06proto3')
)



_TPUHARDWAREFEATURE_EMBEDDINGFEATURE = _descriptor.EnumDescriptor(
  name='EmbeddingFeature',
  full_name='tensorflow.tpu.TPUHardwareFeature.EmbeddingFeature',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNSUPPORTED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='V1', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='V2', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=166,
  serialized_end=217,
)
_sym_db.RegisterEnumDescriptor(_TPUHARDWAREFEATURE_EMBEDDINGFEATURE)


_TPUHARDWAREFEATURE = _descriptor.Descriptor(
  name='TPUHardwareFeature',
  full_name='tensorflow.tpu.TPUHardwareFeature',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='embedding_feature', full_name='tensorflow.tpu.TPUHardwareFeature.embedding_feature', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _TPUHARDWAREFEATURE_EMBEDDINGFEATURE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=64,
  serialized_end=217,
)


_TOPOLOGYPROTO = _descriptor.Descriptor(
  name='TopologyProto',
  full_name='tensorflow.tpu.TopologyProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='mesh_shape', full_name='tensorflow.tpu.TopologyProto.mesh_shape', index=0,
      number=1, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num_tasks', full_name='tensorflow.tpu.TopologyProto.num_tasks', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num_tpu_devices_per_task', full_name='tensorflow.tpu.TopologyProto.num_tpu_devices_per_task', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='device_coordinates', full_name='tensorflow.tpu.TopologyProto.device_coordinates', index=3,
      number=4, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tpu_hardware_feature', full_name='tensorflow.tpu.TopologyProto.tpu_hardware_feature', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=220,
  serialized_end=402,
)

_TPUHARDWAREFEATURE.fields_by_name['embedding_feature'].enum_type = _TPUHARDWAREFEATURE_EMBEDDINGFEATURE
_TPUHARDWAREFEATURE_EMBEDDINGFEATURE.containing_type = _TPUHARDWAREFEATURE
_TOPOLOGYPROTO.fields_by_name['tpu_hardware_feature'].message_type = _TPUHARDWAREFEATURE
DESCRIPTOR.message_types_by_name['TPUHardwareFeature'] = _TPUHARDWAREFEATURE
DESCRIPTOR.message_types_by_name['TopologyProto'] = _TOPOLOGYPROTO
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

TPUHardwareFeature = _reflection.GeneratedProtocolMessageType('TPUHardwareFeature', (_message.Message,), {
  'DESCRIPTOR' : _TPUHARDWAREFEATURE,
  '__module__' : 'tensorflow.core.protobuf.tpu.topology_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUHardwareFeature)
  })
_sym_db.RegisterMessage(TPUHardwareFeature)

TopologyProto = _reflection.GeneratedProtocolMessageType('TopologyProto', (_message.Message,), {
  'DESCRIPTOR' : _TOPOLOGYPROTO,
  '__module__' : 'tensorflow.core.protobuf.tpu.topology_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tpu.TopologyProto)
  })
_sym_db.RegisterMessage(TopologyProto)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)
