# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/core/grappler/costs/op_performance_data.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorflow.core.framework import tensor_pb2 as tensorflow_dot_core_dot_framework_dot_tensor__pb2
from tensorflow.core.framework import tensor_shape_pb2 as tensorflow_dot_core_dot_framework_dot_tensor__shape__pb2
from tensorflow.core.framework import types_pb2 as tensorflow_dot_core_dot_framework_dot_types__pb2
from tensorflow.core.framework import attr_value_pb2 as tensorflow_dot_core_dot_framework_dot_attr__value__pb2
from tensorflow.core.protobuf import device_properties_pb2 as tensorflow_dot_core_dot_protobuf_dot_device__properties__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorflow/core/grappler/costs/op_performance_data.proto',
  package='tensorflow',
  syntax='proto3',
  serialized_options=_b('\370\001\001'),
  serialized_pb=_b('\n8tensorflow/core/grappler/costs/op_performance_data.proto\x12\ntensorflow\x1a&tensorflow/core/framework/tensor.proto\x1a,tensorflow/core/framework/tensor_shape.proto\x1a%tensorflow/core/framework/types.proto\x1a*tensorflow/core/framework/attr_value.proto\x1a\x30tensorflow/core/protobuf/device_properties.proto\"+\n\x0bSessionInfo\x12\x1c\n\x14intra_op_parallelism\x18\x01 \x01(\x03\"\xdb\x03\n\x06OpInfo\x12\n\n\x02op\x18\x01 \x01(\t\x12*\n\x04\x61ttr\x18\x02 \x03(\x0b\x32\x1c.tensorflow.OpInfo.AttrEntry\x12\x33\n\x06inputs\x18\x03 \x03(\x0b\x32#.tensorflow.OpInfo.TensorProperties\x12\x34\n\x07outputs\x18\x05 \x03(\x0b\x32#.tensorflow.OpInfo.TensorProperties\x12,\n\x06\x64\x65vice\x18\x04 \x01(\x0b\x32\x1c.tensorflow.DeviceProperties\x12-\n\x0csession_info\x18\x06 \x01(\x0b\x32\x17.tensorflow.SessionInfo\x1a\x42\n\tAttrEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12$\n\x05value\x18\x02 \x01(\x0b\x32\x15.tensorflow.AttrValue:\x02\x38\x01\x1a\x8c\x01\n\x10TensorProperties\x12#\n\x05\x64type\x18\x01 \x01(\x0e\x32\x14.tensorflow.DataType\x12+\n\x05shape\x18\x02 \x01(\x0b\x32\x1c.tensorflow.TensorShapeProto\x12&\n\x05value\x18\x03 \x01(\x0b\x32\x17.tensorflow.TensorProto\"/\n\x12NormalDistribution\x12\n\n\x02mu\x18\x01 \x01(\x01\x12\r\n\x05sigma\x18\x02 \x01(\x01\"2\n\x15LogNormalDistribution\x12\n\n\x02mu\x18\x01 \x01(\x01\x12\r\n\x05sigma\x18\x02 \x01(\x01\"\xf3\x04\n\rOpPerformance\x12\x1e\n\x02op\x18\x01 \x01(\x0b\x32\x12.tensorflow.OpInfo\x12\x31\n\x0csession_info\x18\x0c \x01(\x0b\x32\x17.tensorflow.SessionInfoB\x02\x18\x01\x12\x0c\n\x04node\x18\x05 \x01(\t\x12\x1d\n\x15temporary_memory_size\x18\x02 \x01(\x03\x12\x14\n\x0c\x63ompute_cost\x18\x03 \x01(\x03\x12\x14\n\x0c\x63ompute_time\x18\x06 \x01(\x03\x12\x13\n\x0bmemory_time\x18\x07 \x01(\x03\x12\x1a\n\x12\x63ompute_efficiency\x18\x04 \x01(\x01\x12\x19\n\x11memory_efficiency\x18\x08 \x01(\x01\x12?\n\x15\x65xecution_time_normal\x18\n \x01(\x0b\x32\x1e.tensorflow.NormalDistributionH\x00\x12\x46\n\x19\x65xecution_time_log_normal\x18\x0b \x01(\x0b\x32!.tensorflow.LogNormalDistributionH\x00\x12\x35\n\top_memory\x18\t \x01(\x0b\x32\".tensorflow.OpPerformance.OpMemory\x1a\x97\x01\n\x08OpMemory\x12\x15\n\routput_memory\x18\x01 \x03(\x03\x12\x13\n\x0btemp_memory\x18\x02 \x01(\x03\x12\x19\n\x11persistent_memory\x18\x04 \x01(\x03\x12\x1e\n\x12\x64\x65vice_temp_memory\x18\x03 \x01(\x03\x42\x02\x18\x01\x12$\n\x18\x64\x65vice_persistent_memory\x18\x05 \x01(\x03\x42\x02\x18\x01\x42\x10\n\x0e\x65xecution_time\"F\n\x11OpPerformanceList\x12\x31\n\x0eop_performance\x18\x01 \x03(\x0b\x32\x19.tensorflow.OpPerformanceB\x03\xf8\x01\x01\x62\x06proto3')
  ,
  dependencies=[tensorflow_dot_core_dot_framework_dot_tensor__pb2.DESCRIPTOR,tensorflow_dot_core_dot_framework_dot_tensor__shape__pb2.DESCRIPTOR,tensorflow_dot_core_dot_framework_dot_types__pb2.DESCRIPTOR,tensorflow_dot_core_dot_framework_dot_attr__value__pb2.DESCRIPTOR,tensorflow_dot_core_dot_protobuf_dot_device__properties__pb2.DESCRIPTOR,])




_SESSIONINFO = _descriptor.Descriptor(
  name='SessionInfo',
  full_name='tensorflow.SessionInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='intra_op_parallelism', full_name='tensorflow.SessionInfo.intra_op_parallelism', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=291,
  serialized_end=334,
)


_OPINFO_ATTRENTRY = _descriptor.Descriptor(
  name='AttrEntry',
  full_name='tensorflow.OpInfo.AttrEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.OpInfo.AttrEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.OpInfo.AttrEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=603,
  serialized_end=669,
)

_OPINFO_TENSORPROPERTIES = _descriptor.Descriptor(
  name='TensorProperties',
  full_name='tensorflow.OpInfo.TensorProperties',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='dtype', full_name='tensorflow.OpInfo.TensorProperties.dtype', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shape', full_name='tensorflow.OpInfo.TensorProperties.shape', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.OpInfo.TensorProperties.value', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=672,
  serialized_end=812,
)

_OPINFO = _descriptor.Descriptor(
  name='OpInfo',
  full_name='tensorflow.OpInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='op', full_name='tensorflow.OpInfo.op', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attr', full_name='tensorflow.OpInfo.attr', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inputs', full_name='tensorflow.OpInfo.inputs', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='outputs', full_name='tensorflow.OpInfo.outputs', index=3,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='device', full_name='tensorflow.OpInfo.device', index=4,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='session_info', full_name='tensorflow.OpInfo.session_info', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_OPINFO_ATTRENTRY, _OPINFO_TENSORPROPERTIES, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=337,
  serialized_end=812,
)


_NORMALDISTRIBUTION = _descriptor.Descriptor(
  name='NormalDistribution',
  full_name='tensorflow.NormalDistribution',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='mu', full_name='tensorflow.NormalDistribution.mu', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sigma', full_name='tensorflow.NormalDistribution.sigma', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=814,
  serialized_end=861,
)


_LOGNORMALDISTRIBUTION = _descriptor.Descriptor(
  name='LogNormalDistribution',
  full_name='tensorflow.LogNormalDistribution',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='mu', full_name='tensorflow.LogNormalDistribution.mu', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sigma', full_name='tensorflow.LogNormalDistribution.sigma', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=863,
  serialized_end=913,
)


_OPPERFORMANCE_OPMEMORY = _descriptor.Descriptor(
  name='OpMemory',
  full_name='tensorflow.OpPerformance.OpMemory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='output_memory', full_name='tensorflow.OpPerformance.OpMemory.output_memory', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='temp_memory', full_name='tensorflow.OpPerformance.OpMemory.temp_memory', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='persistent_memory', full_name='tensorflow.OpPerformance.OpMemory.persistent_memory', index=2,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='device_temp_memory', full_name='tensorflow.OpPerformance.OpMemory.device_temp_memory', index=3,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\030\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='device_persistent_memory', full_name='tensorflow.OpPerformance.OpMemory.device_persistent_memory', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\030\001'), file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1374,
  serialized_end=1525,
)

_OPPERFORMANCE = _descriptor.Descriptor(
  name='OpPerformance',
  full_name='tensorflow.OpPerformance',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='op', full_name='tensorflow.OpPerformance.op', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='session_info', full_name='tensorflow.OpPerformance.session_info', index=1,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\030\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='node', full_name='tensorflow.OpPerformance.node', index=2,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='temporary_memory_size', full_name='tensorflow.OpPerformance.temporary_memory_size', index=3,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='compute_cost', full_name='tensorflow.OpPerformance.compute_cost', index=4,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='compute_time', full_name='tensorflow.OpPerformance.compute_time', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='memory_time', full_name='tensorflow.OpPerformance.memory_time', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='compute_efficiency', full_name='tensorflow.OpPerformance.compute_efficiency', index=7,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='memory_efficiency', full_name='tensorflow.OpPerformance.memory_efficiency', index=8,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='execution_time_normal', full_name='tensorflow.OpPerformance.execution_time_normal', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='execution_time_log_normal', full_name='tensorflow.OpPerformance.execution_time_log_normal', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='op_memory', full_name='tensorflow.OpPerformance.op_memory', index=11,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_OPPERFORMANCE_OPMEMORY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='execution_time', full_name='tensorflow.OpPerformance.execution_time',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=916,
  serialized_end=1543,
)


_OPPERFORMANCELIST = _descriptor.Descriptor(
  name='OpPerformanceList',
  full_name='tensorflow.OpPerformanceList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='op_performance', full_name='tensorflow.OpPerformanceList.op_performance', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1545,
  serialized_end=1615,
)

_OPINFO_ATTRENTRY.fields_by_name['value'].message_type = tensorflow_dot_core_dot_framework_dot_attr__value__pb2._ATTRVALUE
_OPINFO_ATTRENTRY.containing_type = _OPINFO
_OPINFO_TENSORPROPERTIES.fields_by_name['dtype'].enum_type = tensorflow_dot_core_dot_framework_dot_types__pb2._DATATYPE
_OPINFO_TENSORPROPERTIES.fields_by_name['shape'].message_type = tensorflow_dot_core_dot_framework_dot_tensor__shape__pb2._TENSORSHAPEPROTO
_OPINFO_TENSORPROPERTIES.fields_by_name['value'].message_type = tensorflow_dot_core_dot_framework_dot_tensor__pb2._TENSORPROTO
_OPINFO_TENSORPROPERTIES.containing_type = _OPINFO
_OPINFO.fields_by_name['attr'].message_type = _OPINFO_ATTRENTRY
_OPINFO.fields_by_name['inputs'].message_type = _OPINFO_TENSORPROPERTIES
_OPINFO.fields_by_name['outputs'].message_type = _OPINFO_TENSORPROPERTIES
_OPINFO.fields_by_name['device'].message_type = tensorflow_dot_core_dot_protobuf_dot_device__properties__pb2._DEVICEPROPERTIES
_OPINFO.fields_by_name['session_info'].message_type = _SESSIONINFO
_OPPERFORMANCE_OPMEMORY.containing_type = _OPPERFORMANCE
_OPPERFORMANCE.fields_by_name['op'].message_type = _OPINFO
_OPPERFORMANCE.fields_by_name['session_info'].message_type = _SESSIONINFO
_OPPERFORMANCE.fields_by_name['execution_time_normal'].message_type = _NORMALDISTRIBUTION
_OPPERFORMANCE.fields_by_name['execution_time_log_normal'].message_type = _LOGNORMALDISTRIBUTION
_OPPERFORMANCE.fields_by_name['op_memory'].message_type = _OPPERFORMANCE_OPMEMORY
_OPPERFORMANCE.oneofs_by_name['execution_time'].fields.append(
  _OPPERFORMANCE.fields_by_name['execution_time_normal'])
_OPPERFORMANCE.fields_by_name['execution_time_normal'].containing_oneof = _OPPERFORMANCE.oneofs_by_name['execution_time']
_OPPERFORMANCE.oneofs_by_name['execution_time'].fields.append(
  _OPPERFORMANCE.fields_by_name['execution_time_log_normal'])
_OPPERFORMANCE.fields_by_name['execution_time_log_normal'].containing_oneof = _OPPERFORMANCE.oneofs_by_name['execution_time']
_OPPERFORMANCELIST.fields_by_name['op_performance'].message_type = _OPPERFORMANCE
DESCRIPTOR.message_types_by_name['SessionInfo'] = _SESSIONINFO
DESCRIPTOR.message_types_by_name['OpInfo'] = _OPINFO
DESCRIPTOR.message_types_by_name['NormalDistribution'] = _NORMALDISTRIBUTION
DESCRIPTOR.message_types_by_name['LogNormalDistribution'] = _LOGNORMALDISTRIBUTION
DESCRIPTOR.message_types_by_name['OpPerformance'] = _OPPERFORMANCE
DESCRIPTOR.message_types_by_name['OpPerformanceList'] = _OPPERFORMANCELIST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

SessionInfo = _reflection.GeneratedProtocolMessageType('SessionInfo', (_message.Message,), {
  'DESCRIPTOR' : _SESSIONINFO,
  '__module__' : 'tensorflow.core.grappler.costs.op_performance_data_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.SessionInfo)
  })
_sym_db.RegisterMessage(SessionInfo)

OpInfo = _reflection.GeneratedProtocolMessageType('OpInfo', (_message.Message,), {

  'AttrEntry' : _reflection.GeneratedProtocolMessageType('AttrEntry', (_message.Message,), {
    'DESCRIPTOR' : _OPINFO_ATTRENTRY,
    '__module__' : 'tensorflow.core.grappler.costs.op_performance_data_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.OpInfo.AttrEntry)
    })
  ,

  'TensorProperties' : _reflection.GeneratedProtocolMessageType('TensorProperties', (_message.Message,), {
    'DESCRIPTOR' : _OPINFO_TENSORPROPERTIES,
    '__module__' : 'tensorflow.core.grappler.costs.op_performance_data_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.OpInfo.TensorProperties)
    })
  ,
  'DESCRIPTOR' : _OPINFO,
  '__module__' : 'tensorflow.core.grappler.costs.op_performance_data_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.OpInfo)
  })
_sym_db.RegisterMessage(OpInfo)
_sym_db.RegisterMessage(OpInfo.AttrEntry)
_sym_db.RegisterMessage(OpInfo.TensorProperties)

NormalDistribution = _reflection.GeneratedProtocolMessageType('NormalDistribution', (_message.Message,), {
  'DESCRIPTOR' : _NORMALDISTRIBUTION,
  '__module__' : 'tensorflow.core.grappler.costs.op_performance_data_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.NormalDistribution)
  })
_sym_db.RegisterMessage(NormalDistribution)

LogNormalDistribution = _reflection.GeneratedProtocolMessageType('LogNormalDistribution', (_message.Message,), {
  'DESCRIPTOR' : _LOGNORMALDISTRIBUTION,
  '__module__' : 'tensorflow.core.grappler.costs.op_performance_data_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.LogNormalDistribution)
  })
_sym_db.RegisterMessage(LogNormalDistribution)

OpPerformance = _reflection.GeneratedProtocolMessageType('OpPerformance', (_message.Message,), {

  'OpMemory' : _reflection.GeneratedProtocolMessageType('OpMemory', (_message.Message,), {
    'DESCRIPTOR' : _OPPERFORMANCE_OPMEMORY,
    '__module__' : 'tensorflow.core.grappler.costs.op_performance_data_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.OpPerformance.OpMemory)
    })
  ,
  'DESCRIPTOR' : _OPPERFORMANCE,
  '__module__' : 'tensorflow.core.grappler.costs.op_performance_data_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.OpPerformance)
  })
_sym_db.RegisterMessage(OpPerformance)
_sym_db.RegisterMessage(OpPerformance.OpMemory)

OpPerformanceList = _reflection.GeneratedProtocolMessageType('OpPerformanceList', (_message.Message,), {
  'DESCRIPTOR' : _OPPERFORMANCELIST,
  '__module__' : 'tensorflow.core.grappler.costs.op_performance_data_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.OpPerformanceList)
  })
_sym_db.RegisterMessage(OpPerformanceList)


DESCRIPTOR._options = None
_OPINFO_ATTRENTRY._options = None
_OPPERFORMANCE_OPMEMORY.fields_by_name['device_temp_memory']._options = None
_OPPERFORMANCE_OPMEMORY.fields_by_name['device_persistent_memory']._options = None
_OPPERFORMANCE.fields_by_name['session_info']._options = None
# @@protoc_insertion_point(module_scope)
