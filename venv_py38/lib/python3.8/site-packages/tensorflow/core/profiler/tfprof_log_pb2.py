# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/core/profiler/tfprof_log.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorflow.core.framework import attr_value_pb2 as tensorflow_dot_core_dot_framework_dot_attr__value__pb2
from tensorflow.core.framework import step_stats_pb2 as tensorflow_dot_core_dot_framework_dot_step__stats__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorflow/core/profiler/tfprof_log.proto',
  package='tensorflow.tfprof',
  syntax='proto3',
  serialized_options=_b('ZPgithub.com/tensorflow/tensorflow/tensorflow/go/core/profiler/tfprof_log_go_proto'),
  serialized_pb=_b('\n)tensorflow/core/profiler/tfprof_log.proto\x12\x11tensorflow.tfprof\x1a*tensorflow/core/framework/attr_value.proto\x1a*tensorflow/core/framework/step_stats.proto\"\xdf\x01\n\x07\x43odeDef\x12\x30\n\x06traces\x18\x01 \x03(\x0b\x32 .tensorflow.tfprof.CodeDef.Trace\x1a\xa1\x01\n\x05Trace\x12\x10\n\x04\x66ile\x18\x01 \x01(\tB\x02\x18\x01\x12\x0f\n\x07\x66ile_id\x18\x06 \x01(\x03\x12\x0e\n\x06lineno\x18\x02 \x01(\x05\x12\x14\n\x08\x66unction\x18\x03 \x01(\tB\x02\x18\x01\x12\x13\n\x0b\x66unction_id\x18\x07 \x01(\x03\x12\x10\n\x04line\x18\x04 \x01(\tB\x02\x18\x01\x12\x0f\n\x07line_id\x18\x08 \x01(\x03\x12\x17\n\x0f\x66unc_start_line\x18\x05 \x01(\x05\"j\n\nOpLogEntry\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tfloat_ops\x18\x02 \x01(\x03\x12\r\n\x05types\x18\x03 \x03(\t\x12,\n\x08\x63ode_def\x18\x04 \x01(\x0b\x32\x1a.tensorflow.tfprof.CodeDef\"\xb8\x01\n\nOpLogProto\x12\x32\n\x0blog_entries\x18\x01 \x03(\x0b\x32\x1d.tensorflow.tfprof.OpLogEntry\x12\x43\n\x0cid_to_string\x18\x02 \x03(\x0b\x32-.tensorflow.tfprof.OpLogProto.IdToStringEntry\x1a\x31\n\x0fIdToStringEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xd4\x02\n\x0cProfileProto\x12\x39\n\x05nodes\x18\x01 \x03(\x0b\x32*.tensorflow.tfprof.ProfileProto.NodesEntry\x12\x11\n\thas_trace\x18\x02 \x01(\x08\x12\x1f\n\x17miss_accelerator_stream\x18\x05 \x01(\x08\x12\r\n\x05steps\x18\x03 \x03(\x03\x12\x45\n\x0cid_to_string\x18\x04 \x03(\x0b\x32/.tensorflow.tfprof.ProfileProto.IdToStringEntry\x1aL\n\nNodesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12-\n\x05value\x18\x02 \x01(\x0b\x32\x1e.tensorflow.tfprof.ProfileNode:\x02\x38\x01\x1a\x31\n\x0fIdToStringEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xd3\x08\n\x0bProfileNode\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\n\n\x02op\x18\t \x01(\t\x12\n\n\x02id\x18\r \x01(\x03\x12:\n\x06inputs\x18\x02 \x03(\x0b\x32*.tensorflow.tfprof.ProfileNode.InputsEntry\x12\x45\n\x0cinput_shapes\x18\x10 \x03(\x0b\x32/.tensorflow.tfprof.ProfileNode.InputShapesEntry\x12<\n\x07outputs\x18\x03 \x03(\x0b\x32+.tensorflow.tfprof.ProfileNode.OutputsEntry\x12G\n\routput_shapes\x18\x0f \x03(\x0b\x32\x30.tensorflow.tfprof.ProfileNode.OutputShapesEntry\x12L\n\x10src_output_index\x18\x0e \x03(\x0b\x32\x32.tensorflow.tfprof.ProfileNode.SrcOutputIndexEntry\x12\r\n\x05shape\x18\x04 \x03(\x03\x12\x10\n\x08op_types\x18\x05 \x03(\t\x12\x18\n\x10\x63\x61nonical_device\x18\x06 \x01(\t\x12\x13\n\x0bhost_device\x18\x07 \x01(\t\x12\x11\n\tfloat_ops\x18\x08 \x01(\x03\x12)\n\x05trace\x18\n \x01(\x0b\x32\x1a.tensorflow.tfprof.CodeDef\x12\x38\n\x05\x61ttrs\x18\x0b \x03(\x0b\x32).tensorflow.tfprof.ProfileNode.AttrsEntry\x12\x38\n\x05\x65xecs\x18\x0c \x03(\x0b\x32).tensorflow.tfprof.ProfileNode.ExecsEntry\x1a-\n\x0bInputsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\x03:\x02\x38\x01\x1aL\n\x10InputShapesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\'\n\x05value\x18\x02 \x01(\x0b\x32\x18.tensorflow.tfprof.Tuple:\x02\x38\x01\x1a.\n\x0cOutputsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\x03:\x02\x38\x01\x1aM\n\x11OutputShapesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\'\n\x05value\x18\x02 \x01(\x0b\x32\x18.tensorflow.tfprof.Tuple:\x02\x38\x01\x1a\x35\n\x13SrcOutputIndexEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01\x1a\x43\n\nAttrsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12$\n\x05value\x18\x02 \x01(\x0b\x32\x15.tensorflow.AttrValue:\x02\x38\x01\x1aL\n\nExecsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12-\n\x05value\x18\x02 \x01(\x0b\x32\x1e.tensorflow.tfprof.ExecProfile:\x02\x38\x01\"\x84\x04\n\x0b\x45xecProfile\x12\x11\n\trun_count\x18\x01 \x01(\x03\x12\x18\n\x10\x61ll_start_micros\x18\x02 \x01(\x03\x12\x19\n\x11latest_end_micros\x18\x03 \x01(\x03\x12O\n\x11\x61\x63\x63\x65lerator_execs\x18\x04 \x03(\x0b\x32\x34.tensorflow.tfprof.ExecProfile.AcceleratorExecsEntry\x12?\n\tcpu_execs\x18\x05 \x03(\x0b\x32,.tensorflow.tfprof.ExecProfile.CpuExecsEntry\x12\x33\n\x0cmemory_execs\x18\x07 \x03(\x0b\x32\x1d.tensorflow.tfprof.ExecMemory\x12\x31\n\x0b\x61llocations\x18\x0b \x03(\x0b\x32\x1c.tensorflow.AllocationRecord\x12\x0f\n\x07\x64\x65vices\x18\x06 \x03(\t\x1aT\n\x15\x41\x63\x63\x65leratorExecsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12*\n\x05value\x18\x02 \x01(\x0b\x32\x1b.tensorflow.tfprof.ExecTime:\x02\x38\x01\x1aL\n\rCpuExecsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12*\n\x05value\x18\x02 \x01(\x0b\x32\x1b.tensorflow.tfprof.ExecTime:\x02\x38\x01\"3\n\x08\x45xecTime\x12\'\n\x05times\x18\x01 \x03(\x0b\x32\x18.tensorflow.tfprof.Tuple\"\xb4\x03\n\nExecMemory\x12\x15\n\rmemory_micros\x18\x01 \x01(\x03\x12\x17\n\x0fhost_temp_bytes\x18\x02 \x01(\x03\x12\x1d\n\x15host_persistent_bytes\x18\x03 \x01(\x03\x12\x1e\n\x16\x61\x63\x63\x65lerator_temp_bytes\x18\x04 \x01(\x03\x12$\n\x1c\x61\x63\x63\x65lerator_persistent_bytes\x18\x05 \x01(\x03\x12\x17\n\x0frequested_bytes\x18\x06 \x01(\x03\x12\x12\n\npeak_bytes\x18\x07 \x01(\x03\x12\x16\n\x0eresidual_bytes\x18\x08 \x01(\x03\x12\x14\n\x0coutput_bytes\x18\t \x01(\x03\x12\x1e\n\x16\x61llocator_bytes_in_use\x18\n \x01(\x03\x12\x46\n\routput_memory\x18\x0b \x03(\x0b\x32/.tensorflow.tfprof.ExecMemory.OutputMemoryEntry\x1aN\n\x11OutputMemoryEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12(\n\x05value\x18\x02 \x01(\x0b\x32\x19.tensorflow.tfprof.Memory:\x02\x38\x01\"\x1d\n\x05Tuple\x12\x14\n\x0cint64_values\x18\x01 \x03(\x03\"$\n\x06Memory\x12\r\n\x05\x62ytes\x18\x01 \x01(\x03\x12\x0b\n\x03ptr\x18\x02 \x01(\x04\x42RZPgithub.com/tensorflow/tensorflow/tensorflow/go/core/profiler/tfprof_log_go_protob\x06proto3')
  ,
  dependencies=[tensorflow_dot_core_dot_framework_dot_attr__value__pb2.DESCRIPTOR,tensorflow_dot_core_dot_framework_dot_step__stats__pb2.DESCRIPTOR,])




_CODEDEF_TRACE = _descriptor.Descriptor(
  name='Trace',
  full_name='tensorflow.tfprof.CodeDef.Trace',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='file', full_name='tensorflow.tfprof.CodeDef.Trace.file', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\030\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_id', full_name='tensorflow.tfprof.CodeDef.Trace.file_id', index=1,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lineno', full_name='tensorflow.tfprof.CodeDef.Trace.lineno', index=2,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='function', full_name='tensorflow.tfprof.CodeDef.Trace.function', index=3,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\030\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='function_id', full_name='tensorflow.tfprof.CodeDef.Trace.function_id', index=4,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='line', full_name='tensorflow.tfprof.CodeDef.Trace.line', index=5,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\030\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='line_id', full_name='tensorflow.tfprof.CodeDef.Trace.line_id', index=6,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='func_start_line', full_name='tensorflow.tfprof.CodeDef.Trace.func_start_line', index=7,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=215,
  serialized_end=376,
)

_CODEDEF = _descriptor.Descriptor(
  name='CodeDef',
  full_name='tensorflow.tfprof.CodeDef',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='traces', full_name='tensorflow.tfprof.CodeDef.traces', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_CODEDEF_TRACE, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=153,
  serialized_end=376,
)


_OPLOGENTRY = _descriptor.Descriptor(
  name='OpLogEntry',
  full_name='tensorflow.tfprof.OpLogEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='tensorflow.tfprof.OpLogEntry.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='float_ops', full_name='tensorflow.tfprof.OpLogEntry.float_ops', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='types', full_name='tensorflow.tfprof.OpLogEntry.types', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code_def', full_name='tensorflow.tfprof.OpLogEntry.code_def', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=378,
  serialized_end=484,
)


_OPLOGPROTO_IDTOSTRINGENTRY = _descriptor.Descriptor(
  name='IdToStringEntry',
  full_name='tensorflow.tfprof.OpLogProto.IdToStringEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.tfprof.OpLogProto.IdToStringEntry.key', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.tfprof.OpLogProto.IdToStringEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=622,
  serialized_end=671,
)

_OPLOGPROTO = _descriptor.Descriptor(
  name='OpLogProto',
  full_name='tensorflow.tfprof.OpLogProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='log_entries', full_name='tensorflow.tfprof.OpLogProto.log_entries', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id_to_string', full_name='tensorflow.tfprof.OpLogProto.id_to_string', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_OPLOGPROTO_IDTOSTRINGENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=487,
  serialized_end=671,
)


_PROFILEPROTO_NODESENTRY = _descriptor.Descriptor(
  name='NodesEntry',
  full_name='tensorflow.tfprof.ProfileProto.NodesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.tfprof.ProfileProto.NodesEntry.key', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.tfprof.ProfileProto.NodesEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=887,
  serialized_end=963,
)

_PROFILEPROTO_IDTOSTRINGENTRY = _descriptor.Descriptor(
  name='IdToStringEntry',
  full_name='tensorflow.tfprof.ProfileProto.IdToStringEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.tfprof.ProfileProto.IdToStringEntry.key', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.tfprof.ProfileProto.IdToStringEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=622,
  serialized_end=671,
)

_PROFILEPROTO = _descriptor.Descriptor(
  name='ProfileProto',
  full_name='tensorflow.tfprof.ProfileProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='nodes', full_name='tensorflow.tfprof.ProfileProto.nodes', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_trace', full_name='tensorflow.tfprof.ProfileProto.has_trace', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='miss_accelerator_stream', full_name='tensorflow.tfprof.ProfileProto.miss_accelerator_stream', index=2,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='steps', full_name='tensorflow.tfprof.ProfileProto.steps', index=3,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id_to_string', full_name='tensorflow.tfprof.ProfileProto.id_to_string', index=4,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_PROFILEPROTO_NODESENTRY, _PROFILEPROTO_IDTOSTRINGENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=674,
  serialized_end=1014,
)


_PROFILENODE_INPUTSENTRY = _descriptor.Descriptor(
  name='InputsEntry',
  full_name='tensorflow.tfprof.ProfileNode.InputsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.tfprof.ProfileNode.InputsEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.tfprof.ProfileNode.InputsEntry.value', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1672,
  serialized_end=1717,
)

_PROFILENODE_INPUTSHAPESENTRY = _descriptor.Descriptor(
  name='InputShapesEntry',
  full_name='tensorflow.tfprof.ProfileNode.InputShapesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.tfprof.ProfileNode.InputShapesEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.tfprof.ProfileNode.InputShapesEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1719,
  serialized_end=1795,
)

_PROFILENODE_OUTPUTSENTRY = _descriptor.Descriptor(
  name='OutputsEntry',
  full_name='tensorflow.tfprof.ProfileNode.OutputsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.tfprof.ProfileNode.OutputsEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.tfprof.ProfileNode.OutputsEntry.value', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1797,
  serialized_end=1843,
)

_PROFILENODE_OUTPUTSHAPESENTRY = _descriptor.Descriptor(
  name='OutputShapesEntry',
  full_name='tensorflow.tfprof.ProfileNode.OutputShapesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.tfprof.ProfileNode.OutputShapesEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.tfprof.ProfileNode.OutputShapesEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1845,
  serialized_end=1922,
)

_PROFILENODE_SRCOUTPUTINDEXENTRY = _descriptor.Descriptor(
  name='SrcOutputIndexEntry',
  full_name='tensorflow.tfprof.ProfileNode.SrcOutputIndexEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.tfprof.ProfileNode.SrcOutputIndexEntry.key', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.tfprof.ProfileNode.SrcOutputIndexEntry.value', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1924,
  serialized_end=1977,
)

_PROFILENODE_ATTRSENTRY = _descriptor.Descriptor(
  name='AttrsEntry',
  full_name='tensorflow.tfprof.ProfileNode.AttrsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.tfprof.ProfileNode.AttrsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.tfprof.ProfileNode.AttrsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1979,
  serialized_end=2046,
)

_PROFILENODE_EXECSENTRY = _descriptor.Descriptor(
  name='ExecsEntry',
  full_name='tensorflow.tfprof.ProfileNode.ExecsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.tfprof.ProfileNode.ExecsEntry.key', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.tfprof.ProfileNode.ExecsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2048,
  serialized_end=2124,
)

_PROFILENODE = _descriptor.Descriptor(
  name='ProfileNode',
  full_name='tensorflow.tfprof.ProfileNode',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='tensorflow.tfprof.ProfileNode.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='op', full_name='tensorflow.tfprof.ProfileNode.op', index=1,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='tensorflow.tfprof.ProfileNode.id', index=2,
      number=13, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inputs', full_name='tensorflow.tfprof.ProfileNode.inputs', index=3,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='input_shapes', full_name='tensorflow.tfprof.ProfileNode.input_shapes', index=4,
      number=16, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='outputs', full_name='tensorflow.tfprof.ProfileNode.outputs', index=5,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='output_shapes', full_name='tensorflow.tfprof.ProfileNode.output_shapes', index=6,
      number=15, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='src_output_index', full_name='tensorflow.tfprof.ProfileNode.src_output_index', index=7,
      number=14, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shape', full_name='tensorflow.tfprof.ProfileNode.shape', index=8,
      number=4, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='op_types', full_name='tensorflow.tfprof.ProfileNode.op_types', index=9,
      number=5, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='canonical_device', full_name='tensorflow.tfprof.ProfileNode.canonical_device', index=10,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host_device', full_name='tensorflow.tfprof.ProfileNode.host_device', index=11,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='float_ops', full_name='tensorflow.tfprof.ProfileNode.float_ops', index=12,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trace', full_name='tensorflow.tfprof.ProfileNode.trace', index=13,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attrs', full_name='tensorflow.tfprof.ProfileNode.attrs', index=14,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='execs', full_name='tensorflow.tfprof.ProfileNode.execs', index=15,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_PROFILENODE_INPUTSENTRY, _PROFILENODE_INPUTSHAPESENTRY, _PROFILENODE_OUTPUTSENTRY, _PROFILENODE_OUTPUTSHAPESENTRY, _PROFILENODE_SRCOUTPUTINDEXENTRY, _PROFILENODE_ATTRSENTRY, _PROFILENODE_EXECSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1017,
  serialized_end=2124,
)


_EXECPROFILE_ACCELERATOREXECSENTRY = _descriptor.Descriptor(
  name='AcceleratorExecsEntry',
  full_name='tensorflow.tfprof.ExecProfile.AcceleratorExecsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.tfprof.ExecProfile.AcceleratorExecsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.tfprof.ExecProfile.AcceleratorExecsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2481,
  serialized_end=2565,
)

_EXECPROFILE_CPUEXECSENTRY = _descriptor.Descriptor(
  name='CpuExecsEntry',
  full_name='tensorflow.tfprof.ExecProfile.CpuExecsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.tfprof.ExecProfile.CpuExecsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.tfprof.ExecProfile.CpuExecsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2567,
  serialized_end=2643,
)

_EXECPROFILE = _descriptor.Descriptor(
  name='ExecProfile',
  full_name='tensorflow.tfprof.ExecProfile',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='run_count', full_name='tensorflow.tfprof.ExecProfile.run_count', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='all_start_micros', full_name='tensorflow.tfprof.ExecProfile.all_start_micros', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='latest_end_micros', full_name='tensorflow.tfprof.ExecProfile.latest_end_micros', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accelerator_execs', full_name='tensorflow.tfprof.ExecProfile.accelerator_execs', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cpu_execs', full_name='tensorflow.tfprof.ExecProfile.cpu_execs', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='memory_execs', full_name='tensorflow.tfprof.ExecProfile.memory_execs', index=5,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocations', full_name='tensorflow.tfprof.ExecProfile.allocations', index=6,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='devices', full_name='tensorflow.tfprof.ExecProfile.devices', index=7,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_EXECPROFILE_ACCELERATOREXECSENTRY, _EXECPROFILE_CPUEXECSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2127,
  serialized_end=2643,
)


_EXECTIME = _descriptor.Descriptor(
  name='ExecTime',
  full_name='tensorflow.tfprof.ExecTime',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='times', full_name='tensorflow.tfprof.ExecTime.times', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2645,
  serialized_end=2696,
)


_EXECMEMORY_OUTPUTMEMORYENTRY = _descriptor.Descriptor(
  name='OutputMemoryEntry',
  full_name='tensorflow.tfprof.ExecMemory.OutputMemoryEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.tfprof.ExecMemory.OutputMemoryEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.tfprof.ExecMemory.OutputMemoryEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3057,
  serialized_end=3135,
)

_EXECMEMORY = _descriptor.Descriptor(
  name='ExecMemory',
  full_name='tensorflow.tfprof.ExecMemory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='memory_micros', full_name='tensorflow.tfprof.ExecMemory.memory_micros', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host_temp_bytes', full_name='tensorflow.tfprof.ExecMemory.host_temp_bytes', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host_persistent_bytes', full_name='tensorflow.tfprof.ExecMemory.host_persistent_bytes', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accelerator_temp_bytes', full_name='tensorflow.tfprof.ExecMemory.accelerator_temp_bytes', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accelerator_persistent_bytes', full_name='tensorflow.tfprof.ExecMemory.accelerator_persistent_bytes', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='requested_bytes', full_name='tensorflow.tfprof.ExecMemory.requested_bytes', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='peak_bytes', full_name='tensorflow.tfprof.ExecMemory.peak_bytes', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='residual_bytes', full_name='tensorflow.tfprof.ExecMemory.residual_bytes', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='output_bytes', full_name='tensorflow.tfprof.ExecMemory.output_bytes', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocator_bytes_in_use', full_name='tensorflow.tfprof.ExecMemory.allocator_bytes_in_use', index=9,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='output_memory', full_name='tensorflow.tfprof.ExecMemory.output_memory', index=10,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_EXECMEMORY_OUTPUTMEMORYENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2699,
  serialized_end=3135,
)


_TUPLE = _descriptor.Descriptor(
  name='Tuple',
  full_name='tensorflow.tfprof.Tuple',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='int64_values', full_name='tensorflow.tfprof.Tuple.int64_values', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3137,
  serialized_end=3166,
)


_MEMORY = _descriptor.Descriptor(
  name='Memory',
  full_name='tensorflow.tfprof.Memory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bytes', full_name='tensorflow.tfprof.Memory.bytes', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ptr', full_name='tensorflow.tfprof.Memory.ptr', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3168,
  serialized_end=3204,
)

_CODEDEF_TRACE.containing_type = _CODEDEF
_CODEDEF.fields_by_name['traces'].message_type = _CODEDEF_TRACE
_OPLOGENTRY.fields_by_name['code_def'].message_type = _CODEDEF
_OPLOGPROTO_IDTOSTRINGENTRY.containing_type = _OPLOGPROTO
_OPLOGPROTO.fields_by_name['log_entries'].message_type = _OPLOGENTRY
_OPLOGPROTO.fields_by_name['id_to_string'].message_type = _OPLOGPROTO_IDTOSTRINGENTRY
_PROFILEPROTO_NODESENTRY.fields_by_name['value'].message_type = _PROFILENODE
_PROFILEPROTO_NODESENTRY.containing_type = _PROFILEPROTO
_PROFILEPROTO_IDTOSTRINGENTRY.containing_type = _PROFILEPROTO
_PROFILEPROTO.fields_by_name['nodes'].message_type = _PROFILEPROTO_NODESENTRY
_PROFILEPROTO.fields_by_name['id_to_string'].message_type = _PROFILEPROTO_IDTOSTRINGENTRY
_PROFILENODE_INPUTSENTRY.containing_type = _PROFILENODE
_PROFILENODE_INPUTSHAPESENTRY.fields_by_name['value'].message_type = _TUPLE
_PROFILENODE_INPUTSHAPESENTRY.containing_type = _PROFILENODE
_PROFILENODE_OUTPUTSENTRY.containing_type = _PROFILENODE
_PROFILENODE_OUTPUTSHAPESENTRY.fields_by_name['value'].message_type = _TUPLE
_PROFILENODE_OUTPUTSHAPESENTRY.containing_type = _PROFILENODE
_PROFILENODE_SRCOUTPUTINDEXENTRY.containing_type = _PROFILENODE
_PROFILENODE_ATTRSENTRY.fields_by_name['value'].message_type = tensorflow_dot_core_dot_framework_dot_attr__value__pb2._ATTRVALUE
_PROFILENODE_ATTRSENTRY.containing_type = _PROFILENODE
_PROFILENODE_EXECSENTRY.fields_by_name['value'].message_type = _EXECPROFILE
_PROFILENODE_EXECSENTRY.containing_type = _PROFILENODE
_PROFILENODE.fields_by_name['inputs'].message_type = _PROFILENODE_INPUTSENTRY
_PROFILENODE.fields_by_name['input_shapes'].message_type = _PROFILENODE_INPUTSHAPESENTRY
_PROFILENODE.fields_by_name['outputs'].message_type = _PROFILENODE_OUTPUTSENTRY
_PROFILENODE.fields_by_name['output_shapes'].message_type = _PROFILENODE_OUTPUTSHAPESENTRY
_PROFILENODE.fields_by_name['src_output_index'].message_type = _PROFILENODE_SRCOUTPUTINDEXENTRY
_PROFILENODE.fields_by_name['trace'].message_type = _CODEDEF
_PROFILENODE.fields_by_name['attrs'].message_type = _PROFILENODE_ATTRSENTRY
_PROFILENODE.fields_by_name['execs'].message_type = _PROFILENODE_EXECSENTRY
_EXECPROFILE_ACCELERATOREXECSENTRY.fields_by_name['value'].message_type = _EXECTIME
_EXECPROFILE_ACCELERATOREXECSENTRY.containing_type = _EXECPROFILE
_EXECPROFILE_CPUEXECSENTRY.fields_by_name['value'].message_type = _EXECTIME
_EXECPROFILE_CPUEXECSENTRY.containing_type = _EXECPROFILE
_EXECPROFILE.fields_by_name['accelerator_execs'].message_type = _EXECPROFILE_ACCELERATOREXECSENTRY
_EXECPROFILE.fields_by_name['cpu_execs'].message_type = _EXECPROFILE_CPUEXECSENTRY
_EXECPROFILE.fields_by_name['memory_execs'].message_type = _EXECMEMORY
_EXECPROFILE.fields_by_name['allocations'].message_type = tensorflow_dot_core_dot_framework_dot_step__stats__pb2._ALLOCATIONRECORD
_EXECTIME.fields_by_name['times'].message_type = _TUPLE
_EXECMEMORY_OUTPUTMEMORYENTRY.fields_by_name['value'].message_type = _MEMORY
_EXECMEMORY_OUTPUTMEMORYENTRY.containing_type = _EXECMEMORY
_EXECMEMORY.fields_by_name['output_memory'].message_type = _EXECMEMORY_OUTPUTMEMORYENTRY
DESCRIPTOR.message_types_by_name['CodeDef'] = _CODEDEF
DESCRIPTOR.message_types_by_name['OpLogEntry'] = _OPLOGENTRY
DESCRIPTOR.message_types_by_name['OpLogProto'] = _OPLOGPROTO
DESCRIPTOR.message_types_by_name['ProfileProto'] = _PROFILEPROTO
DESCRIPTOR.message_types_by_name['ProfileNode'] = _PROFILENODE
DESCRIPTOR.message_types_by_name['ExecProfile'] = _EXECPROFILE
DESCRIPTOR.message_types_by_name['ExecTime'] = _EXECTIME
DESCRIPTOR.message_types_by_name['ExecMemory'] = _EXECMEMORY
DESCRIPTOR.message_types_by_name['Tuple'] = _TUPLE
DESCRIPTOR.message_types_by_name['Memory'] = _MEMORY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CodeDef = _reflection.GeneratedProtocolMessageType('CodeDef', (_message.Message,), {

  'Trace' : _reflection.GeneratedProtocolMessageType('Trace', (_message.Message,), {
    'DESCRIPTOR' : _CODEDEF_TRACE,
    '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tfprof.CodeDef.Trace)
    })
  ,
  'DESCRIPTOR' : _CODEDEF,
  '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tfprof.CodeDef)
  })
_sym_db.RegisterMessage(CodeDef)
_sym_db.RegisterMessage(CodeDef.Trace)

OpLogEntry = _reflection.GeneratedProtocolMessageType('OpLogEntry', (_message.Message,), {
  'DESCRIPTOR' : _OPLOGENTRY,
  '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tfprof.OpLogEntry)
  })
_sym_db.RegisterMessage(OpLogEntry)

OpLogProto = _reflection.GeneratedProtocolMessageType('OpLogProto', (_message.Message,), {

  'IdToStringEntry' : _reflection.GeneratedProtocolMessageType('IdToStringEntry', (_message.Message,), {
    'DESCRIPTOR' : _OPLOGPROTO_IDTOSTRINGENTRY,
    '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tfprof.OpLogProto.IdToStringEntry)
    })
  ,
  'DESCRIPTOR' : _OPLOGPROTO,
  '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tfprof.OpLogProto)
  })
_sym_db.RegisterMessage(OpLogProto)
_sym_db.RegisterMessage(OpLogProto.IdToStringEntry)

ProfileProto = _reflection.GeneratedProtocolMessageType('ProfileProto', (_message.Message,), {

  'NodesEntry' : _reflection.GeneratedProtocolMessageType('NodesEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILEPROTO_NODESENTRY,
    '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tfprof.ProfileProto.NodesEntry)
    })
  ,

  'IdToStringEntry' : _reflection.GeneratedProtocolMessageType('IdToStringEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILEPROTO_IDTOSTRINGENTRY,
    '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tfprof.ProfileProto.IdToStringEntry)
    })
  ,
  'DESCRIPTOR' : _PROFILEPROTO,
  '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tfprof.ProfileProto)
  })
_sym_db.RegisterMessage(ProfileProto)
_sym_db.RegisterMessage(ProfileProto.NodesEntry)
_sym_db.RegisterMessage(ProfileProto.IdToStringEntry)

ProfileNode = _reflection.GeneratedProtocolMessageType('ProfileNode', (_message.Message,), {

  'InputsEntry' : _reflection.GeneratedProtocolMessageType('InputsEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILENODE_INPUTSENTRY,
    '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tfprof.ProfileNode.InputsEntry)
    })
  ,

  'InputShapesEntry' : _reflection.GeneratedProtocolMessageType('InputShapesEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILENODE_INPUTSHAPESENTRY,
    '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tfprof.ProfileNode.InputShapesEntry)
    })
  ,

  'OutputsEntry' : _reflection.GeneratedProtocolMessageType('OutputsEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILENODE_OUTPUTSENTRY,
    '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tfprof.ProfileNode.OutputsEntry)
    })
  ,

  'OutputShapesEntry' : _reflection.GeneratedProtocolMessageType('OutputShapesEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILENODE_OUTPUTSHAPESENTRY,
    '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tfprof.ProfileNode.OutputShapesEntry)
    })
  ,

  'SrcOutputIndexEntry' : _reflection.GeneratedProtocolMessageType('SrcOutputIndexEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILENODE_SRCOUTPUTINDEXENTRY,
    '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tfprof.ProfileNode.SrcOutputIndexEntry)
    })
  ,

  'AttrsEntry' : _reflection.GeneratedProtocolMessageType('AttrsEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILENODE_ATTRSENTRY,
    '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tfprof.ProfileNode.AttrsEntry)
    })
  ,

  'ExecsEntry' : _reflection.GeneratedProtocolMessageType('ExecsEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILENODE_EXECSENTRY,
    '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tfprof.ProfileNode.ExecsEntry)
    })
  ,
  'DESCRIPTOR' : _PROFILENODE,
  '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tfprof.ProfileNode)
  })
_sym_db.RegisterMessage(ProfileNode)
_sym_db.RegisterMessage(ProfileNode.InputsEntry)
_sym_db.RegisterMessage(ProfileNode.InputShapesEntry)
_sym_db.RegisterMessage(ProfileNode.OutputsEntry)
_sym_db.RegisterMessage(ProfileNode.OutputShapesEntry)
_sym_db.RegisterMessage(ProfileNode.SrcOutputIndexEntry)
_sym_db.RegisterMessage(ProfileNode.AttrsEntry)
_sym_db.RegisterMessage(ProfileNode.ExecsEntry)

ExecProfile = _reflection.GeneratedProtocolMessageType('ExecProfile', (_message.Message,), {

  'AcceleratorExecsEntry' : _reflection.GeneratedProtocolMessageType('AcceleratorExecsEntry', (_message.Message,), {
    'DESCRIPTOR' : _EXECPROFILE_ACCELERATOREXECSENTRY,
    '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tfprof.ExecProfile.AcceleratorExecsEntry)
    })
  ,

  'CpuExecsEntry' : _reflection.GeneratedProtocolMessageType('CpuExecsEntry', (_message.Message,), {
    'DESCRIPTOR' : _EXECPROFILE_CPUEXECSENTRY,
    '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tfprof.ExecProfile.CpuExecsEntry)
    })
  ,
  'DESCRIPTOR' : _EXECPROFILE,
  '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tfprof.ExecProfile)
  })
_sym_db.RegisterMessage(ExecProfile)
_sym_db.RegisterMessage(ExecProfile.AcceleratorExecsEntry)
_sym_db.RegisterMessage(ExecProfile.CpuExecsEntry)

ExecTime = _reflection.GeneratedProtocolMessageType('ExecTime', (_message.Message,), {
  'DESCRIPTOR' : _EXECTIME,
  '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tfprof.ExecTime)
  })
_sym_db.RegisterMessage(ExecTime)

ExecMemory = _reflection.GeneratedProtocolMessageType('ExecMemory', (_message.Message,), {

  'OutputMemoryEntry' : _reflection.GeneratedProtocolMessageType('OutputMemoryEntry', (_message.Message,), {
    'DESCRIPTOR' : _EXECMEMORY_OUTPUTMEMORYENTRY,
    '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tfprof.ExecMemory.OutputMemoryEntry)
    })
  ,
  'DESCRIPTOR' : _EXECMEMORY,
  '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tfprof.ExecMemory)
  })
_sym_db.RegisterMessage(ExecMemory)
_sym_db.RegisterMessage(ExecMemory.OutputMemoryEntry)

Tuple = _reflection.GeneratedProtocolMessageType('Tuple', (_message.Message,), {
  'DESCRIPTOR' : _TUPLE,
  '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tfprof.Tuple)
  })
_sym_db.RegisterMessage(Tuple)

Memory = _reflection.GeneratedProtocolMessageType('Memory', (_message.Message,), {
  'DESCRIPTOR' : _MEMORY,
  '__module__' : 'tensorflow.core.profiler.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tfprof.Memory)
  })
_sym_db.RegisterMessage(Memory)


DESCRIPTOR._options = None
_CODEDEF_TRACE.fields_by_name['file']._options = None
_CODEDEF_TRACE.fields_by_name['function']._options = None
_CODEDEF_TRACE.fields_by_name['line']._options = None
_OPLOGPROTO_IDTOSTRINGENTRY._options = None
_PROFILEPROTO_NODESENTRY._options = None
_PROFILEPROTO_IDTOSTRINGENTRY._options = None
_PROFILENODE_INPUTSENTRY._options = None
_PROFILENODE_INPUTSHAPESENTRY._options = None
_PROFILENODE_OUTPUTSENTRY._options = None
_PROFILENODE_OUTPUTSHAPESENTRY._options = None
_PROFILENODE_SRCOUTPUTINDEXENTRY._options = None
_PROFILENODE_ATTRSENTRY._options = None
_PROFILENODE_EXECSENTRY._options = None
_EXECPROFILE_ACCELERATOREXECSENTRY._options = None
_EXECPROFILE_CPUEXECSENTRY._options = None
_EXECMEMORY_OUTPUTMEMORYENTRY._options = None
# @@protoc_insertion_point(module_scope)
