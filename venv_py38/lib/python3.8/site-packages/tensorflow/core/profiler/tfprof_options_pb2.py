# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/core/profiler/tfprof_options.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorflow/core/profiler/tfprof_options.proto',
  package='tensorflow.tfprof',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n-tensorflow/core/profiler/tfprof_options.proto\x12\x11tensorflow.tfprof\"\x95\x04\n\x0cOptionsProto\x12\x11\n\tmax_depth\x18\x01 \x01(\x03\x12\x11\n\tmin_bytes\x18\x02 \x01(\x03\x12\x16\n\x0emin_peak_bytes\x18\x13 \x01(\x03\x12\x1a\n\x12min_residual_bytes\x18\x14 \x01(\x03\x12\x18\n\x10min_output_bytes\x18\x15 \x01(\x03\x12\x12\n\nmin_micros\x18\x03 \x01(\x03\x12\x1e\n\x16min_accelerator_micros\x18\x16 \x01(\x03\x12\x16\n\x0emin_cpu_micros\x18\x17 \x01(\x03\x12\x12\n\nmin_params\x18\x04 \x01(\x03\x12\x15\n\rmin_float_ops\x18\x05 \x01(\x03\x12\x16\n\x0emin_occurrence\x18\x11 \x01(\x03\x12\x0c\n\x04step\x18\x12 \x01(\x03\x12\x10\n\x08order_by\x18\x07 \x01(\t\x12\x1c\n\x14\x61\x63\x63ount_type_regexes\x18\x08 \x03(\t\x12\x1a\n\x12start_name_regexes\x18\t \x03(\t\x12\x19\n\x11trim_name_regexes\x18\n \x03(\t\x12\x19\n\x11show_name_regexes\x18\x0b \x03(\t\x12\x19\n\x11hide_name_regexes\x18\x0c \x03(\t\x12!\n\x19\x61\x63\x63ount_displayed_op_only\x18\r \x01(\x08\x12\x0e\n\x06select\x18\x0e \x03(\t\x12\x0e\n\x06output\x18\x0f \x01(\t\x12\x14\n\x0c\x64ump_to_file\x18\x10 \x01(\t\"\xda\x02\n\x13\x41\x64visorOptionsProto\x12\x46\n\x08\x63heckers\x18\x01 \x03(\x0b\x32\x34.tensorflow.tfprof.AdvisorOptionsProto.CheckersEntry\x1a\x65\n\rCheckersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x43\n\x05value\x18\x02 \x01(\x0b\x32\x34.tensorflow.tfprof.AdvisorOptionsProto.CheckerOption:\x02\x38\x01\x1a\x93\x01\n\rCheckerOption\x12R\n\x07options\x18\x01 \x03(\x0b\x32\x41.tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.OptionsEntry\x1a.\n\x0cOptionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x62\x06proto3')
)




_OPTIONSPROTO = _descriptor.Descriptor(
  name='OptionsProto',
  full_name='tensorflow.tfprof.OptionsProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='max_depth', full_name='tensorflow.tfprof.OptionsProto.max_depth', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_bytes', full_name='tensorflow.tfprof.OptionsProto.min_bytes', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_peak_bytes', full_name='tensorflow.tfprof.OptionsProto.min_peak_bytes', index=2,
      number=19, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_residual_bytes', full_name='tensorflow.tfprof.OptionsProto.min_residual_bytes', index=3,
      number=20, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_output_bytes', full_name='tensorflow.tfprof.OptionsProto.min_output_bytes', index=4,
      number=21, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_micros', full_name='tensorflow.tfprof.OptionsProto.min_micros', index=5,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_accelerator_micros', full_name='tensorflow.tfprof.OptionsProto.min_accelerator_micros', index=6,
      number=22, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_cpu_micros', full_name='tensorflow.tfprof.OptionsProto.min_cpu_micros', index=7,
      number=23, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_params', full_name='tensorflow.tfprof.OptionsProto.min_params', index=8,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_float_ops', full_name='tensorflow.tfprof.OptionsProto.min_float_ops', index=9,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_occurrence', full_name='tensorflow.tfprof.OptionsProto.min_occurrence', index=10,
      number=17, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='step', full_name='tensorflow.tfprof.OptionsProto.step', index=11,
      number=18, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by', full_name='tensorflow.tfprof.OptionsProto.order_by', index=12,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_type_regexes', full_name='tensorflow.tfprof.OptionsProto.account_type_regexes', index=13,
      number=8, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_name_regexes', full_name='tensorflow.tfprof.OptionsProto.start_name_regexes', index=14,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trim_name_regexes', full_name='tensorflow.tfprof.OptionsProto.trim_name_regexes', index=15,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='show_name_regexes', full_name='tensorflow.tfprof.OptionsProto.show_name_regexes', index=16,
      number=11, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hide_name_regexes', full_name='tensorflow.tfprof.OptionsProto.hide_name_regexes', index=17,
      number=12, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_displayed_op_only', full_name='tensorflow.tfprof.OptionsProto.account_displayed_op_only', index=18,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='select', full_name='tensorflow.tfprof.OptionsProto.select', index=19,
      number=14, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='output', full_name='tensorflow.tfprof.OptionsProto.output', index=20,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dump_to_file', full_name='tensorflow.tfprof.OptionsProto.dump_to_file', index=21,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=69,
  serialized_end=602,
)


_ADVISOROPTIONSPROTO_CHECKERSENTRY = _descriptor.Descriptor(
  name='CheckersEntry',
  full_name='tensorflow.tfprof.AdvisorOptionsProto.CheckersEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.tfprof.AdvisorOptionsProto.CheckersEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.tfprof.AdvisorOptionsProto.CheckersEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=700,
  serialized_end=801,
)

_ADVISOROPTIONSPROTO_CHECKEROPTION_OPTIONSENTRY = _descriptor.Descriptor(
  name='OptionsEntry',
  full_name='tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.OptionsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.OptionsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.OptionsEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=905,
  serialized_end=951,
)

_ADVISOROPTIONSPROTO_CHECKEROPTION = _descriptor.Descriptor(
  name='CheckerOption',
  full_name='tensorflow.tfprof.AdvisorOptionsProto.CheckerOption',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='options', full_name='tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.options', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_ADVISOROPTIONSPROTO_CHECKEROPTION_OPTIONSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=804,
  serialized_end=951,
)

_ADVISOROPTIONSPROTO = _descriptor.Descriptor(
  name='AdvisorOptionsProto',
  full_name='tensorflow.tfprof.AdvisorOptionsProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='checkers', full_name='tensorflow.tfprof.AdvisorOptionsProto.checkers', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_ADVISOROPTIONSPROTO_CHECKERSENTRY, _ADVISOROPTIONSPROTO_CHECKEROPTION, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=605,
  serialized_end=951,
)

_ADVISOROPTIONSPROTO_CHECKERSENTRY.fields_by_name['value'].message_type = _ADVISOROPTIONSPROTO_CHECKEROPTION
_ADVISOROPTIONSPROTO_CHECKERSENTRY.containing_type = _ADVISOROPTIONSPROTO
_ADVISOROPTIONSPROTO_CHECKEROPTION_OPTIONSENTRY.containing_type = _ADVISOROPTIONSPROTO_CHECKEROPTION
_ADVISOROPTIONSPROTO_CHECKEROPTION.fields_by_name['options'].message_type = _ADVISOROPTIONSPROTO_CHECKEROPTION_OPTIONSENTRY
_ADVISOROPTIONSPROTO_CHECKEROPTION.containing_type = _ADVISOROPTIONSPROTO
_ADVISOROPTIONSPROTO.fields_by_name['checkers'].message_type = _ADVISOROPTIONSPROTO_CHECKERSENTRY
DESCRIPTOR.message_types_by_name['OptionsProto'] = _OPTIONSPROTO
DESCRIPTOR.message_types_by_name['AdvisorOptionsProto'] = _ADVISOROPTIONSPROTO
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

OptionsProto = _reflection.GeneratedProtocolMessageType('OptionsProto', (_message.Message,), {
  'DESCRIPTOR' : _OPTIONSPROTO,
  '__module__' : 'tensorflow.core.profiler.tfprof_options_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tfprof.OptionsProto)
  })
_sym_db.RegisterMessage(OptionsProto)

AdvisorOptionsProto = _reflection.GeneratedProtocolMessageType('AdvisorOptionsProto', (_message.Message,), {

  'CheckersEntry' : _reflection.GeneratedProtocolMessageType('CheckersEntry', (_message.Message,), {
    'DESCRIPTOR' : _ADVISOROPTIONSPROTO_CHECKERSENTRY,
    '__module__' : 'tensorflow.core.profiler.tfprof_options_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tfprof.AdvisorOptionsProto.CheckersEntry)
    })
  ,

  'CheckerOption' : _reflection.GeneratedProtocolMessageType('CheckerOption', (_message.Message,), {

    'OptionsEntry' : _reflection.GeneratedProtocolMessageType('OptionsEntry', (_message.Message,), {
      'DESCRIPTOR' : _ADVISOROPTIONSPROTO_CHECKEROPTION_OPTIONSENTRY,
      '__module__' : 'tensorflow.core.profiler.tfprof_options_pb2'
      # @@protoc_insertion_point(class_scope:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.OptionsEntry)
      })
    ,
    'DESCRIPTOR' : _ADVISOROPTIONSPROTO_CHECKEROPTION,
    '__module__' : 'tensorflow.core.profiler.tfprof_options_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
    })
  ,
  'DESCRIPTOR' : _ADVISOROPTIONSPROTO,
  '__module__' : 'tensorflow.core.profiler.tfprof_options_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.tfprof.AdvisorOptionsProto)
  })
_sym_db.RegisterMessage(AdvisorOptionsProto)
_sym_db.RegisterMessage(AdvisorOptionsProto.CheckersEntry)
_sym_db.RegisterMessage(AdvisorOptionsProto.CheckerOption)
_sym_db.RegisterMessage(AdvisorOptionsProto.CheckerOption.OptionsEntry)


_ADVISOROPTIONSPROTO_CHECKERSENTRY._options = None
_ADVISOROPTIONSPROTO_CHECKEROPTION_OPTIONSENTRY._options = None
# @@protoc_insertion_point(module_scope)
