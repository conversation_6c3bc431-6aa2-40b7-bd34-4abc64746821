# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.tpu.experimental.embedding namespace.
"""

import sys as _sys

from tensorflow.python.tpu.tpu_embedding_for_serving import TPUEmbeddingForServing
from tensorflow.python.tpu.tpu_embedding_for_serving import cpu_embedding_lookup as serving_embedding_lookup
from tensorflow.python.tpu.tpu_embedding_v1 import TPUEmbeddingV0
from tensorflow.python.tpu.tpu_embedding_v2 import TPUEmbedding
from tensorflow.python.tpu.tpu_embedding_v2_utils import Adagrad
from tensorflow.python.tpu.tpu_embedding_v2_utils import AdagradMomentum
from tensorflow.python.tpu.tpu_embedding_v2_utils import Adam
from tensorflow.python.tpu.tpu_embedding_v2_utils import FTRL
from tensorflow.python.tpu.tpu_embedding_v2_utils import FeatureConfig
from tensorflow.python.tpu.tpu_embedding_v2_utils import SGD
from tensorflow.python.tpu.tpu_embedding_v2_utils import TableConfig