# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.raw_ops namespace.
"""

import sys as _sys

from tensorflow.python.ops.gen_array_ops import BatchMatrixBandPart
from tensorflow.python.ops.gen_array_ops import BatchMatrixDiag
from tensorflow.python.ops.gen_array_ops import BatchMatrixDiagPart
from tensorflow.python.ops.gen_array_ops import BatchMatrixSetDiag
from tensorflow.python.ops.gen_array_ops import BatchToSpace
from tensorflow.python.ops.gen_array_ops import BatchToSpaceND
from tensorflow.python.ops.gen_array_ops import Bitcast
from tensorflow.python.ops.gen_array_ops import BroadcastArgs
from tensorflow.python.ops.gen_array_ops import BroadcastGradientArgs
from tensorflow.python.ops.gen_array_ops import BroadcastTo
from tensorflow.python.ops.gen_array_ops import CheckNumerics
from tensorflow.python.ops.gen_array_ops import CheckNumericsV2
from tensorflow.python.ops.gen_array_ops import Concat
from tensorflow.python.ops.gen_array_ops import ConcatOffset
from tensorflow.python.ops.gen_array_ops import ConcatV2
from tensorflow.python.ops.gen_array_ops import ConjugateTranspose
from tensorflow.python.ops.gen_array_ops import Const
from tensorflow.python.ops.gen_array_ops import DebugGradientIdentity
from tensorflow.python.ops.gen_array_ops import DebugGradientRefIdentity
from tensorflow.python.ops.gen_array_ops import DeepCopy
from tensorflow.python.ops.gen_array_ops import DepthToSpace
from tensorflow.python.ops.gen_array_ops import Dequantize
from tensorflow.python.ops.gen_array_ops import Diag
from tensorflow.python.ops.gen_array_ops import DiagPart
from tensorflow.python.ops.gen_array_ops import EditDistance
from tensorflow.python.ops.gen_array_ops import Empty
from tensorflow.python.ops.gen_array_ops import EnsureShape
from tensorflow.python.ops.gen_array_ops import ExpandDims
from tensorflow.python.ops.gen_array_ops import ExtractImagePatches
from tensorflow.python.ops.gen_array_ops import ExtractVolumePatches
from tensorflow.python.ops.gen_array_ops import FakeQuantWithMinMaxArgs
from tensorflow.python.ops.gen_array_ops import FakeQuantWithMinMaxArgsGradient
from tensorflow.python.ops.gen_array_ops import FakeQuantWithMinMaxVars
from tensorflow.python.ops.gen_array_ops import FakeQuantWithMinMaxVarsGradient
from tensorflow.python.ops.gen_array_ops import FakeQuantWithMinMaxVarsPerChannel
from tensorflow.python.ops.gen_array_ops import FakeQuantWithMinMaxVarsPerChannelGradient
from tensorflow.python.ops.gen_array_ops import Fill
from tensorflow.python.ops.gen_array_ops import Fingerprint
from tensorflow.python.ops.gen_array_ops import Gather
from tensorflow.python.ops.gen_array_ops import GatherNd
from tensorflow.python.ops.gen_array_ops import GatherV2
from tensorflow.python.ops.gen_array_ops import GuaranteeConst
from tensorflow.python.ops.gen_array_ops import Identity
from tensorflow.python.ops.gen_array_ops import IdentityN
from tensorflow.python.ops.gen_array_ops import ImmutableConst
from tensorflow.python.ops.gen_array_ops import InplaceAdd
from tensorflow.python.ops.gen_array_ops import InplaceSub
from tensorflow.python.ops.gen_array_ops import InplaceUpdate
from tensorflow.python.ops.gen_array_ops import InvertPermutation
from tensorflow.python.ops.gen_array_ops import ListDiff
from tensorflow.python.ops.gen_array_ops import LowerBound
from tensorflow.python.ops.gen_array_ops import MatrixBandPart
from tensorflow.python.ops.gen_array_ops import MatrixDiag
from tensorflow.python.ops.gen_array_ops import MatrixDiagPart
from tensorflow.python.ops.gen_array_ops import MatrixDiagPartV2
from tensorflow.python.ops.gen_array_ops import MatrixDiagPartV3
from tensorflow.python.ops.gen_array_ops import MatrixDiagV2
from tensorflow.python.ops.gen_array_ops import MatrixDiagV3
from tensorflow.python.ops.gen_array_ops import MatrixSetDiag
from tensorflow.python.ops.gen_array_ops import MatrixSetDiagV2
from tensorflow.python.ops.gen_array_ops import MatrixSetDiagV3
from tensorflow.python.ops.gen_array_ops import MirrorPad
from tensorflow.python.ops.gen_array_ops import MirrorPadGrad
from tensorflow.python.ops.gen_array_ops import OneHot
from tensorflow.python.ops.gen_array_ops import OnesLike
from tensorflow.python.ops.gen_array_ops import Pack
from tensorflow.python.ops.gen_array_ops import Pad
from tensorflow.python.ops.gen_array_ops import PadV2
from tensorflow.python.ops.gen_array_ops import ParallelConcat
from tensorflow.python.ops.gen_array_ops import Placeholder
from tensorflow.python.ops.gen_array_ops import PlaceholderV2
from tensorflow.python.ops.gen_array_ops import PlaceholderWithDefault
from tensorflow.python.ops.gen_array_ops import PreventGradient
from tensorflow.python.ops.gen_array_ops import QuantizeAndDequantize
from tensorflow.python.ops.gen_array_ops import QuantizeAndDequantizeV2
from tensorflow.python.ops.gen_array_ops import QuantizeAndDequantizeV3
from tensorflow.python.ops.gen_array_ops import QuantizeAndDequantizeV4
from tensorflow.python.ops.gen_array_ops import QuantizeAndDequantizeV4Grad
from tensorflow.python.ops.gen_array_ops import QuantizeV2
from tensorflow.python.ops.gen_array_ops import QuantizedConcat
from tensorflow.python.ops.gen_array_ops import QuantizedInstanceNorm
from tensorflow.python.ops.gen_array_ops import QuantizedReshape
from tensorflow.python.ops.gen_array_ops import Rank
from tensorflow.python.ops.gen_array_ops import RefIdentity
from tensorflow.python.ops.gen_array_ops import Reshape
from tensorflow.python.ops.gen_array_ops import ResourceStridedSliceAssign
from tensorflow.python.ops.gen_array_ops import Reverse
from tensorflow.python.ops.gen_array_ops import ReverseSequence
from tensorflow.python.ops.gen_array_ops import ReverseV2
from tensorflow.python.ops.gen_array_ops import ScatterNd
from tensorflow.python.ops.gen_array_ops import ScatterNdNonAliasingAdd
from tensorflow.python.ops.gen_array_ops import Shape
from tensorflow.python.ops.gen_array_ops import ShapeN
from tensorflow.python.ops.gen_array_ops import Size
from tensorflow.python.ops.gen_array_ops import Slice
from tensorflow.python.ops.gen_array_ops import Snapshot
from tensorflow.python.ops.gen_array_ops import SpaceToBatch
from tensorflow.python.ops.gen_array_ops import SpaceToBatchND
from tensorflow.python.ops.gen_array_ops import SpaceToDepth
from tensorflow.python.ops.gen_array_ops import Split
from tensorflow.python.ops.gen_array_ops import SplitV
from tensorflow.python.ops.gen_array_ops import Squeeze
from tensorflow.python.ops.gen_array_ops import StopGradient
from tensorflow.python.ops.gen_array_ops import StridedSlice
from tensorflow.python.ops.gen_array_ops import StridedSliceAssign
from tensorflow.python.ops.gen_array_ops import StridedSliceGrad
from tensorflow.python.ops.gen_array_ops import TensorScatterAdd
from tensorflow.python.ops.gen_array_ops import TensorScatterMax
from tensorflow.python.ops.gen_array_ops import TensorScatterMin
from tensorflow.python.ops.gen_array_ops import TensorScatterSub
from tensorflow.python.ops.gen_array_ops import TensorScatterUpdate
from tensorflow.python.ops.gen_array_ops import TensorStridedSliceUpdate
from tensorflow.python.ops.gen_array_ops import Tile
from tensorflow.python.ops.gen_array_ops import TileGrad
from tensorflow.python.ops.gen_array_ops import Transpose
from tensorflow.python.ops.gen_array_ops import Unique
from tensorflow.python.ops.gen_array_ops import UniqueV2
from tensorflow.python.ops.gen_array_ops import UniqueWithCounts
from tensorflow.python.ops.gen_array_ops import UniqueWithCountsV2
from tensorflow.python.ops.gen_array_ops import Unpack
from tensorflow.python.ops.gen_array_ops import UnravelIndex
from tensorflow.python.ops.gen_array_ops import UpperBound
from tensorflow.python.ops.gen_array_ops import Where
from tensorflow.python.ops.gen_array_ops import ZerosLike
from tensorflow.python.ops.gen_audio_ops import AudioSpectrogram
from tensorflow.python.ops.gen_audio_ops import DecodeWav
from tensorflow.python.ops.gen_audio_ops import EncodeWav
from tensorflow.python.ops.gen_audio_ops import Mfcc
from tensorflow.python.ops.gen_batch_ops import Batch
from tensorflow.python.ops.gen_batch_ops import BatchFunction
from tensorflow.python.ops.gen_batch_ops import Unbatch
from tensorflow.python.ops.gen_batch_ops import UnbatchGrad
from tensorflow.python.ops.gen_bitwise_ops import BitwiseAnd
from tensorflow.python.ops.gen_bitwise_ops import BitwiseOr
from tensorflow.python.ops.gen_bitwise_ops import BitwiseXor
from tensorflow.python.ops.gen_bitwise_ops import Invert
from tensorflow.python.ops.gen_bitwise_ops import LeftShift
from tensorflow.python.ops.gen_bitwise_ops import PopulationCount
from tensorflow.python.ops.gen_bitwise_ops import RightShift
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesAggregateStats
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesBucketize
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesCalculateBestFeatureSplit
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesCalculateBestFeatureSplitV2
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesCalculateBestGainsPerFeature
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesCenterBias
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesCreateEnsemble
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesCreateQuantileStreamResource
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesDeserializeEnsemble
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesEnsembleResourceHandleOp
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesExampleDebugOutputs
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesFlushQuantileSummaries
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesGetEnsembleStates
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesMakeQuantileSummaries
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesMakeStatsSummary
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesPredict
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesQuantileStreamResourceAddSummaries
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesQuantileStreamResourceDeserialize
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesQuantileStreamResourceFlush
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesQuantileStreamResourceGetBucketBoundaries
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesQuantileStreamResourceHandleOp
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesSerializeEnsemble
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesSparseAggregateStats
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesSparseCalculateBestFeatureSplit
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesTrainingPredict
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesUpdateEnsemble
from tensorflow.python.ops.gen_boosted_trees_ops import BoostedTreesUpdateEnsembleV2
from tensorflow.python.ops.gen_boosted_trees_ops import IsBoostedTreesEnsembleInitialized
from tensorflow.python.ops.gen_boosted_trees_ops import IsBoostedTreesQuantileStreamResourceInitialized
from tensorflow.python.ops.gen_candidate_sampling_ops import AllCandidateSampler
from tensorflow.python.ops.gen_candidate_sampling_ops import ComputeAccidentalHits
from tensorflow.python.ops.gen_candidate_sampling_ops import FixedUnigramCandidateSampler
from tensorflow.python.ops.gen_candidate_sampling_ops import LearnedUnigramCandidateSampler
from tensorflow.python.ops.gen_candidate_sampling_ops import LogUniformCandidateSampler
from tensorflow.python.ops.gen_candidate_sampling_ops import ThreadUnsafeUnigramCandidateSampler
from tensorflow.python.ops.gen_candidate_sampling_ops import UniformCandidateSampler
from tensorflow.python.ops.gen_checkpoint_ops import GenerateVocabRemapping
from tensorflow.python.ops.gen_checkpoint_ops import LoadAndRemapMatrix
from tensorflow.python.ops.gen_collective_ops import CollectiveAllToAllV3
from tensorflow.python.ops.gen_collective_ops import CollectiveAssignGroupV2
from tensorflow.python.ops.gen_collective_ops import CollectiveBcastRecv
from tensorflow.python.ops.gen_collective_ops import CollectiveBcastRecvV2
from tensorflow.python.ops.gen_collective_ops import CollectiveBcastSend
from tensorflow.python.ops.gen_collective_ops import CollectiveBcastSendV2
from tensorflow.python.ops.gen_collective_ops import CollectiveGather
from tensorflow.python.ops.gen_collective_ops import CollectiveGatherV2
from tensorflow.python.ops.gen_collective_ops import CollectiveInitializeCommunicator
from tensorflow.python.ops.gen_collective_ops import CollectiveReduce
from tensorflow.python.ops.gen_collective_ops import CollectiveReduceV2
from tensorflow.python.ops.gen_collective_ops import CollectiveReduceV3
from tensorflow.python.ops.gen_composite_tensor_ops import CompositeTensorVariantFromComponents
from tensorflow.python.ops.gen_composite_tensor_ops import CompositeTensorVariantToComponents
from tensorflow.python.ops.gen_control_flow_ops import Abort
from tensorflow.python.ops.gen_control_flow_ops import ControlTrigger
from tensorflow.python.ops.gen_control_flow_ops import Enter
from tensorflow.python.ops.gen_control_flow_ops import Exit
from tensorflow.python.ops.gen_control_flow_ops import LoopCond
from tensorflow.python.ops.gen_control_flow_ops import Merge
from tensorflow.python.ops.gen_control_flow_ops import NextIteration
from tensorflow.python.ops.gen_control_flow_ops import NoOp
from tensorflow.python.ops.gen_control_flow_ops import RefEnter
from tensorflow.python.ops.gen_control_flow_ops import RefExit
from tensorflow.python.ops.gen_control_flow_ops import RefMerge
from tensorflow.python.ops.gen_control_flow_ops import RefNextIteration
from tensorflow.python.ops.gen_control_flow_ops import RefSelect
from tensorflow.python.ops.gen_control_flow_ops import RefSwitch
from tensorflow.python.ops.gen_control_flow_ops import Switch
from tensorflow.python.ops.gen_count_ops import DenseCountSparseOutput
from tensorflow.python.ops.gen_count_ops import RaggedCountSparseOutput
from tensorflow.python.ops.gen_count_ops import SparseCountSparseOutput
from tensorflow.python.ops.gen_ctc_ops import CTCBeamSearchDecoder
from tensorflow.python.ops.gen_ctc_ops import CTCGreedyDecoder
from tensorflow.python.ops.gen_ctc_ops import CTCLoss
from tensorflow.python.ops.gen_ctc_ops import CTCLossV2
from tensorflow.python.ops.gen_cudnn_rnn_ops import CudnnRNN
from tensorflow.python.ops.gen_cudnn_rnn_ops import CudnnRNNBackprop
from tensorflow.python.ops.gen_cudnn_rnn_ops import CudnnRNNBackpropV2
from tensorflow.python.ops.gen_cudnn_rnn_ops import CudnnRNNBackpropV3
from tensorflow.python.ops.gen_cudnn_rnn_ops import CudnnRNNCanonicalToParams
from tensorflow.python.ops.gen_cudnn_rnn_ops import CudnnRNNCanonicalToParamsV2
from tensorflow.python.ops.gen_cudnn_rnn_ops import CudnnRNNParamsSize
from tensorflow.python.ops.gen_cudnn_rnn_ops import CudnnRNNParamsToCanonical
from tensorflow.python.ops.gen_cudnn_rnn_ops import CudnnRNNParamsToCanonicalV2
from tensorflow.python.ops.gen_cudnn_rnn_ops import CudnnRNNV2
from tensorflow.python.ops.gen_cudnn_rnn_ops import CudnnRNNV3
from tensorflow.python.ops.gen_data_flow_ops import AccumulatorApplyGradient
from tensorflow.python.ops.gen_data_flow_ops import AccumulatorNumAccumulated
from tensorflow.python.ops.gen_data_flow_ops import AccumulatorSetGlobalStep
from tensorflow.python.ops.gen_data_flow_ops import AccumulatorTakeGradient
from tensorflow.python.ops.gen_data_flow_ops import Barrier
from tensorflow.python.ops.gen_data_flow_ops import BarrierClose
from tensorflow.python.ops.gen_data_flow_ops import BarrierIncompleteSize
from tensorflow.python.ops.gen_data_flow_ops import BarrierInsertMany
from tensorflow.python.ops.gen_data_flow_ops import BarrierReadySize
from tensorflow.python.ops.gen_data_flow_ops import BarrierTakeMany
from tensorflow.python.ops.gen_data_flow_ops import ConditionalAccumulator
from tensorflow.python.ops.gen_data_flow_ops import DeleteSessionTensor
from tensorflow.python.ops.gen_data_flow_ops import DynamicPartition
from tensorflow.python.ops.gen_data_flow_ops import DynamicStitch
from tensorflow.python.ops.gen_data_flow_ops import FIFOQueue
from tensorflow.python.ops.gen_data_flow_ops import FIFOQueueV2
from tensorflow.python.ops.gen_data_flow_ops import FakeQueue
from tensorflow.python.ops.gen_data_flow_ops import GetSessionHandle
from tensorflow.python.ops.gen_data_flow_ops import GetSessionHandleV2
from tensorflow.python.ops.gen_data_flow_ops import GetSessionTensor
from tensorflow.python.ops.gen_data_flow_ops import MapClear
from tensorflow.python.ops.gen_data_flow_ops import MapIncompleteSize
from tensorflow.python.ops.gen_data_flow_ops import MapPeek
from tensorflow.python.ops.gen_data_flow_ops import MapSize
from tensorflow.python.ops.gen_data_flow_ops import MapStage
from tensorflow.python.ops.gen_data_flow_ops import MapUnstage
from tensorflow.python.ops.gen_data_flow_ops import MapUnstageNoKey
from tensorflow.python.ops.gen_data_flow_ops import OrderedMapClear
from tensorflow.python.ops.gen_data_flow_ops import OrderedMapIncompleteSize
from tensorflow.python.ops.gen_data_flow_ops import OrderedMapPeek
from tensorflow.python.ops.gen_data_flow_ops import OrderedMapSize
from tensorflow.python.ops.gen_data_flow_ops import OrderedMapStage
from tensorflow.python.ops.gen_data_flow_ops import OrderedMapUnstage
from tensorflow.python.ops.gen_data_flow_ops import OrderedMapUnstageNoKey
from tensorflow.python.ops.gen_data_flow_ops import PaddingFIFOQueue
from tensorflow.python.ops.gen_data_flow_ops import PaddingFIFOQueueV2
from tensorflow.python.ops.gen_data_flow_ops import ParallelDynamicStitch
from tensorflow.python.ops.gen_data_flow_ops import PriorityQueue
from tensorflow.python.ops.gen_data_flow_ops import PriorityQueueV2
from tensorflow.python.ops.gen_data_flow_ops import QueueClose
from tensorflow.python.ops.gen_data_flow_ops import QueueCloseV2
from tensorflow.python.ops.gen_data_flow_ops import QueueDequeue
from tensorflow.python.ops.gen_data_flow_ops import QueueDequeueMany
from tensorflow.python.ops.gen_data_flow_ops import QueueDequeueManyV2
from tensorflow.python.ops.gen_data_flow_ops import QueueDequeueUpTo
from tensorflow.python.ops.gen_data_flow_ops import QueueDequeueUpToV2
from tensorflow.python.ops.gen_data_flow_ops import QueueDequeueV2
from tensorflow.python.ops.gen_data_flow_ops import QueueEnqueue
from tensorflow.python.ops.gen_data_flow_ops import QueueEnqueueMany
from tensorflow.python.ops.gen_data_flow_ops import QueueEnqueueManyV2
from tensorflow.python.ops.gen_data_flow_ops import QueueEnqueueV2
from tensorflow.python.ops.gen_data_flow_ops import QueueIsClosed
from tensorflow.python.ops.gen_data_flow_ops import QueueIsClosedV2
from tensorflow.python.ops.gen_data_flow_ops import QueueSize
from tensorflow.python.ops.gen_data_flow_ops import QueueSizeV2
from tensorflow.python.ops.gen_data_flow_ops import RandomShuffleQueue
from tensorflow.python.ops.gen_data_flow_ops import RandomShuffleQueueV2
from tensorflow.python.ops.gen_data_flow_ops import RecordInput
from tensorflow.python.ops.gen_data_flow_ops import ResourceAccumulatorApplyGradient
from tensorflow.python.ops.gen_data_flow_ops import ResourceAccumulatorNumAccumulated
from tensorflow.python.ops.gen_data_flow_ops import ResourceAccumulatorSetGlobalStep
from tensorflow.python.ops.gen_data_flow_ops import ResourceAccumulatorTakeGradient
from tensorflow.python.ops.gen_data_flow_ops import ResourceConditionalAccumulator
from tensorflow.python.ops.gen_data_flow_ops import SparseAccumulatorApplyGradient
from tensorflow.python.ops.gen_data_flow_ops import SparseAccumulatorTakeGradient
from tensorflow.python.ops.gen_data_flow_ops import SparseConditionalAccumulator
from tensorflow.python.ops.gen_data_flow_ops import Stack
from tensorflow.python.ops.gen_data_flow_ops import StackClose
from tensorflow.python.ops.gen_data_flow_ops import StackCloseV2
from tensorflow.python.ops.gen_data_flow_ops import StackPop
from tensorflow.python.ops.gen_data_flow_ops import StackPopV2
from tensorflow.python.ops.gen_data_flow_ops import StackPush
from tensorflow.python.ops.gen_data_flow_ops import StackPushV2
from tensorflow.python.ops.gen_data_flow_ops import StackV2
from tensorflow.python.ops.gen_data_flow_ops import Stage
from tensorflow.python.ops.gen_data_flow_ops import StageClear
from tensorflow.python.ops.gen_data_flow_ops import StagePeek
from tensorflow.python.ops.gen_data_flow_ops import StageSize
from tensorflow.python.ops.gen_data_flow_ops import TensorArray
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayClose
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayCloseV2
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayCloseV3
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayConcat
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayConcatV2
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayConcatV3
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayGather
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayGatherV2
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayGatherV3
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayGrad
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayGradV2
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayGradV3
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayGradWithShape
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayPack
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayRead
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayReadV2
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayReadV3
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayScatter
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayScatterV2
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayScatterV3
from tensorflow.python.ops.gen_data_flow_ops import TensorArraySize
from tensorflow.python.ops.gen_data_flow_ops import TensorArraySizeV2
from tensorflow.python.ops.gen_data_flow_ops import TensorArraySizeV3
from tensorflow.python.ops.gen_data_flow_ops import TensorArraySplit
from tensorflow.python.ops.gen_data_flow_ops import TensorArraySplitV2
from tensorflow.python.ops.gen_data_flow_ops import TensorArraySplitV3
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayUnpack
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayV2
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayV3
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayWrite
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayWriteV2
from tensorflow.python.ops.gen_data_flow_ops import TensorArrayWriteV3
from tensorflow.python.ops.gen_data_flow_ops import Unstage
from tensorflow.python.ops.gen_dataset_ops import AnonymousIterator
from tensorflow.python.ops.gen_dataset_ops import AnonymousIteratorV2
from tensorflow.python.ops.gen_dataset_ops import AnonymousIteratorV3
from tensorflow.python.ops.gen_dataset_ops import AnonymousMemoryCache
from tensorflow.python.ops.gen_dataset_ops import AnonymousMultiDeviceIterator
from tensorflow.python.ops.gen_dataset_ops import AnonymousMultiDeviceIteratorV3
from tensorflow.python.ops.gen_dataset_ops import AnonymousRandomSeedGenerator
from tensorflow.python.ops.gen_dataset_ops import AnonymousSeedGenerator
from tensorflow.python.ops.gen_dataset_ops import BatchDataset
from tensorflow.python.ops.gen_dataset_ops import BatchDatasetV2
from tensorflow.python.ops.gen_dataset_ops import CacheDataset
from tensorflow.python.ops.gen_dataset_ops import CacheDatasetV2
from tensorflow.python.ops.gen_dataset_ops import ConcatenateDataset
from tensorflow.python.ops.gen_dataset_ops import DatasetCardinality
from tensorflow.python.ops.gen_dataset_ops import DatasetToGraph
from tensorflow.python.ops.gen_dataset_ops import DatasetToGraphV2
from tensorflow.python.ops.gen_dataset_ops import DatasetToSingleElement
from tensorflow.python.ops.gen_dataset_ops import DeleteIterator
from tensorflow.python.ops.gen_dataset_ops import DeleteMemoryCache
from tensorflow.python.ops.gen_dataset_ops import DeleteMultiDeviceIterator
from tensorflow.python.ops.gen_dataset_ops import DeleteRandomSeedGenerator
from tensorflow.python.ops.gen_dataset_ops import DeleteSeedGenerator
from tensorflow.python.ops.gen_dataset_ops import DeserializeIterator
from tensorflow.python.ops.gen_dataset_ops import DummyMemoryCache
from tensorflow.python.ops.gen_dataset_ops import DummySeedGenerator
from tensorflow.python.ops.gen_dataset_ops import FilterByLastComponentDataset
from tensorflow.python.ops.gen_dataset_ops import FilterDataset
from tensorflow.python.ops.gen_dataset_ops import FinalizeDataset
from tensorflow.python.ops.gen_dataset_ops import FixedLengthRecordDataset
from tensorflow.python.ops.gen_dataset_ops import FixedLengthRecordDatasetV2
from tensorflow.python.ops.gen_dataset_ops import FlatMapDataset
from tensorflow.python.ops.gen_dataset_ops import GeneratorDataset
from tensorflow.python.ops.gen_dataset_ops import GetOptions
from tensorflow.python.ops.gen_dataset_ops import InterleaveDataset
from tensorflow.python.ops.gen_dataset_ops import Iterator
from tensorflow.python.ops.gen_dataset_ops import IteratorFromStringHandle
from tensorflow.python.ops.gen_dataset_ops import IteratorFromStringHandleV2
from tensorflow.python.ops.gen_dataset_ops import IteratorGetNext
from tensorflow.python.ops.gen_dataset_ops import IteratorGetNextAsOptional
from tensorflow.python.ops.gen_dataset_ops import IteratorGetNextSync
from tensorflow.python.ops.gen_dataset_ops import IteratorToStringHandle
from tensorflow.python.ops.gen_dataset_ops import IteratorV2
from tensorflow.python.ops.gen_dataset_ops import MakeIterator
from tensorflow.python.ops.gen_dataset_ops import MapDataset
from tensorflow.python.ops.gen_dataset_ops import MapDefun
from tensorflow.python.ops.gen_dataset_ops import ModelDataset
from tensorflow.python.ops.gen_dataset_ops import MultiDeviceIterator
from tensorflow.python.ops.gen_dataset_ops import MultiDeviceIteratorFromStringHandle
from tensorflow.python.ops.gen_dataset_ops import MultiDeviceIteratorGetNextFromShard
from tensorflow.python.ops.gen_dataset_ops import MultiDeviceIteratorInit
from tensorflow.python.ops.gen_dataset_ops import MultiDeviceIteratorToStringHandle
from tensorflow.python.ops.gen_dataset_ops import OneShotIterator
from tensorflow.python.ops.gen_dataset_ops import OptimizeDataset
from tensorflow.python.ops.gen_dataset_ops import OptimizeDatasetV2
from tensorflow.python.ops.gen_dataset_ops import OptionalFromValue
from tensorflow.python.ops.gen_dataset_ops import OptionalGetValue
from tensorflow.python.ops.gen_dataset_ops import OptionalHasValue
from tensorflow.python.ops.gen_dataset_ops import OptionalNone
from tensorflow.python.ops.gen_dataset_ops import OptionsDataset
from tensorflow.python.ops.gen_dataset_ops import PaddedBatchDataset
from tensorflow.python.ops.gen_dataset_ops import PaddedBatchDatasetV2
from tensorflow.python.ops.gen_dataset_ops import ParallelBatchDataset
from tensorflow.python.ops.gen_dataset_ops import ParallelFilterDataset
from tensorflow.python.ops.gen_dataset_ops import ParallelInterleaveDatasetV2
from tensorflow.python.ops.gen_dataset_ops import ParallelInterleaveDatasetV3
from tensorflow.python.ops.gen_dataset_ops import ParallelInterleaveDatasetV4
from tensorflow.python.ops.gen_dataset_ops import ParallelMapDataset
from tensorflow.python.ops.gen_dataset_ops import ParallelMapDatasetV2
from tensorflow.python.ops.gen_dataset_ops import PrefetchDataset
from tensorflow.python.ops.gen_dataset_ops import RangeDataset
from tensorflow.python.ops.gen_dataset_ops import ReduceDataset
from tensorflow.python.ops.gen_dataset_ops import RepeatDataset
from tensorflow.python.ops.gen_dataset_ops import SerializeIterator
from tensorflow.python.ops.gen_dataset_ops import ShardDataset
from tensorflow.python.ops.gen_dataset_ops import ShuffleAndRepeatDataset
from tensorflow.python.ops.gen_dataset_ops import ShuffleAndRepeatDatasetV2
from tensorflow.python.ops.gen_dataset_ops import ShuffleDataset
from tensorflow.python.ops.gen_dataset_ops import ShuffleDatasetV2
from tensorflow.python.ops.gen_dataset_ops import ShuffleDatasetV3
from tensorflow.python.ops.gen_dataset_ops import SkipDataset
from tensorflow.python.ops.gen_dataset_ops import SparseTensorSliceDataset
from tensorflow.python.ops.gen_dataset_ops import TFRecordDataset
from tensorflow.python.ops.gen_dataset_ops import TakeDataset
from tensorflow.python.ops.gen_dataset_ops import TensorDataset
from tensorflow.python.ops.gen_dataset_ops import TensorSliceDataset
from tensorflow.python.ops.gen_dataset_ops import TextLineDataset
from tensorflow.python.ops.gen_dataset_ops import UnwrapDatasetVariant
from tensorflow.python.ops.gen_dataset_ops import WindowDataset
from tensorflow.python.ops.gen_dataset_ops import WindowOp
from tensorflow.python.ops.gen_dataset_ops import WrapDatasetVariant
from tensorflow.python.ops.gen_dataset_ops import ZipDataset
from tensorflow.python.ops.gen_debug_ops import Copy
from tensorflow.python.ops.gen_debug_ops import CopyHost
from tensorflow.python.ops.gen_debug_ops import DebugIdentity
from tensorflow.python.ops.gen_debug_ops import DebugIdentityV2
from tensorflow.python.ops.gen_debug_ops import DebugNanCount
from tensorflow.python.ops.gen_debug_ops import DebugNumericSummary
from tensorflow.python.ops.gen_debug_ops import DebugNumericSummaryV2
from tensorflow.python.ops.gen_decode_proto_ops import DecodeProtoV2
from tensorflow.python.ops.gen_encode_proto_ops import EncodeProto
from tensorflow.python.ops.gen_experimental_dataset_ops import AssertCardinalityDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import AssertNextDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import AssertPrevDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import AutoShardDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import BytesProducedStatsDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import CSVDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import CSVDatasetV2
from tensorflow.python.ops.gen_experimental_dataset_ops import ChooseFastestBranchDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ChooseFastestDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import CompressElement
from tensorflow.python.ops.gen_experimental_dataset_ops import ComputeBatchSize
from tensorflow.python.ops.gen_experimental_dataset_ops import DataServiceDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import DataServiceDatasetV2
from tensorflow.python.ops.gen_experimental_dataset_ops import DataServiceDatasetV3
from tensorflow.python.ops.gen_experimental_dataset_ops import DatasetFromGraph
from tensorflow.python.ops.gen_experimental_dataset_ops import DatasetToTFRecord
from tensorflow.python.ops.gen_experimental_dataset_ops import DenseToSparseBatchDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import DirectedInterleaveDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import DummyIterationCounter
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalAssertNextDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalAutoShardDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalBytesProducedStatsDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalCSVDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalChooseFastestDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalDatasetCardinality
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalDatasetToTFRecord
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalDenseToSparseBatchDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalDirectedInterleaveDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalGroupByReducerDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalGroupByWindowDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalIgnoreErrorsDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalIteratorGetDevice
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalLMDBDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalLatencyStatsDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalMapAndBatchDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalMapDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalMatchingFilesDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalMaxIntraOpParallelismDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalNonSerializableDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalParallelInterleaveDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalParseExampleDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalPrivateThreadPoolDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalRandomDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalRebatchDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalScanDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalSetStatsAggregatorDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalSleepDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalSlidingWindowDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalSqlDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalStatsAggregatorHandle
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalStatsAggregatorSummary
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalTakeWhileDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalThreadPoolDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalThreadPoolHandle
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalUnbatchDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ExperimentalUniqueDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import GetElementAtIndex
from tensorflow.python.ops.gen_experimental_dataset_ops import GroupByReducerDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import GroupByWindowDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import IgnoreErrorsDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import InitializeTableFromDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import IteratorGetDevice
from tensorflow.python.ops.gen_experimental_dataset_ops import LMDBDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import LatencyStatsDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import LegacyParallelInterleaveDatasetV2
from tensorflow.python.ops.gen_experimental_dataset_ops import LoadDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import MapAndBatchDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import MatchingFilesDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import MaxIntraOpParallelismDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import NonSerializableDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ParallelInterleaveDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ParseExampleDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ParseExampleDatasetV2
from tensorflow.python.ops.gen_experimental_dataset_ops import PrivateThreadPoolDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import RandomDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import RebatchDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import RebatchDatasetV2
from tensorflow.python.ops.gen_experimental_dataset_ops import RegisterDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import SamplingDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import SaveDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import SaveDatasetV2
from tensorflow.python.ops.gen_experimental_dataset_ops import ScanDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import SetStatsAggregatorDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import SleepDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import SlidingWindowDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import SnapshotDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import SnapshotDatasetReader
from tensorflow.python.ops.gen_experimental_dataset_ops import SnapshotDatasetV2
from tensorflow.python.ops.gen_experimental_dataset_ops import SnapshotNestedDatasetReader
from tensorflow.python.ops.gen_experimental_dataset_ops import SqlDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import StatsAggregatorHandle
from tensorflow.python.ops.gen_experimental_dataset_ops import StatsAggregatorHandleV2
from tensorflow.python.ops.gen_experimental_dataset_ops import StatsAggregatorSetSummaryWriter
from tensorflow.python.ops.gen_experimental_dataset_ops import StatsAggregatorSummary
from tensorflow.python.ops.gen_experimental_dataset_ops import TakeWhileDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ThreadPoolDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import ThreadPoolHandle
from tensorflow.python.ops.gen_experimental_dataset_ops import UnbatchDataset
from tensorflow.python.ops.gen_experimental_dataset_ops import UncompressElement
from tensorflow.python.ops.gen_experimental_dataset_ops import UniqueDataset
from tensorflow.python.ops.gen_functional_ops import Case
from tensorflow.python.ops.gen_functional_ops import DeviceIndex
from tensorflow.python.ops.gen_functional_ops import FakeParam
from tensorflow.python.ops.gen_functional_ops import For
from tensorflow.python.ops.gen_functional_ops import If
from tensorflow.python.ops.gen_functional_ops import PartitionedCall
from tensorflow.python.ops.gen_functional_ops import RemoteCall
from tensorflow.python.ops.gen_functional_ops import StatefulPartitionedCall
from tensorflow.python.ops.gen_functional_ops import StatelessCase
from tensorflow.python.ops.gen_functional_ops import StatelessIf
from tensorflow.python.ops.gen_functional_ops import StatelessWhile
from tensorflow.python.ops.gen_functional_ops import SymbolicGradient
from tensorflow.python.ops.gen_functional_ops import ToBool
from tensorflow.python.ops.gen_functional_ops import While
from tensorflow.python.ops.gen_image_ops import AdjustContrast
from tensorflow.python.ops.gen_image_ops import AdjustContrastv2
from tensorflow.python.ops.gen_image_ops import AdjustHue
from tensorflow.python.ops.gen_image_ops import AdjustSaturation
from tensorflow.python.ops.gen_image_ops import CombinedNonMaxSuppression
from tensorflow.python.ops.gen_image_ops import CropAndResize
from tensorflow.python.ops.gen_image_ops import CropAndResizeGradBoxes
from tensorflow.python.ops.gen_image_ops import CropAndResizeGradImage
from tensorflow.python.ops.gen_image_ops import DecodeAndCropJpeg
from tensorflow.python.ops.gen_image_ops import DecodeBmp
from tensorflow.python.ops.gen_image_ops import DecodeGif
from tensorflow.python.ops.gen_image_ops import DecodeImage
from tensorflow.python.ops.gen_image_ops import DecodeJpeg
from tensorflow.python.ops.gen_image_ops import DecodePng
from tensorflow.python.ops.gen_image_ops import DrawBoundingBoxes
from tensorflow.python.ops.gen_image_ops import DrawBoundingBoxesV2
from tensorflow.python.ops.gen_image_ops import EncodeJpeg
from tensorflow.python.ops.gen_image_ops import EncodeJpegVariableQuality
from tensorflow.python.ops.gen_image_ops import EncodePng
from tensorflow.python.ops.gen_image_ops import ExtractGlimpse
from tensorflow.python.ops.gen_image_ops import ExtractGlimpseV2
from tensorflow.python.ops.gen_image_ops import ExtractJpegShape
from tensorflow.python.ops.gen_image_ops import GenerateBoundingBoxProposals
from tensorflow.python.ops.gen_image_ops import HSVToRGB
from tensorflow.python.ops.gen_image_ops import ImageProjectiveTransformV2
from tensorflow.python.ops.gen_image_ops import ImageProjectiveTransformV3
from tensorflow.python.ops.gen_image_ops import NonMaxSuppression
from tensorflow.python.ops.gen_image_ops import NonMaxSuppressionV2
from tensorflow.python.ops.gen_image_ops import NonMaxSuppressionV3
from tensorflow.python.ops.gen_image_ops import NonMaxSuppressionV4
from tensorflow.python.ops.gen_image_ops import NonMaxSuppressionV5
from tensorflow.python.ops.gen_image_ops import NonMaxSuppressionWithOverlaps
from tensorflow.python.ops.gen_image_ops import QuantizedResizeBilinear
from tensorflow.python.ops.gen_image_ops import RGBToHSV
from tensorflow.python.ops.gen_image_ops import RandomCrop
from tensorflow.python.ops.gen_image_ops import ResizeArea
from tensorflow.python.ops.gen_image_ops import ResizeBicubic
from tensorflow.python.ops.gen_image_ops import ResizeBicubicGrad
from tensorflow.python.ops.gen_image_ops import ResizeBilinear
from tensorflow.python.ops.gen_image_ops import ResizeBilinearGrad
from tensorflow.python.ops.gen_image_ops import ResizeNearestNeighbor
from tensorflow.python.ops.gen_image_ops import ResizeNearestNeighborGrad
from tensorflow.python.ops.gen_image_ops import SampleDistortedBoundingBox
from tensorflow.python.ops.gen_image_ops import SampleDistortedBoundingBoxV2
from tensorflow.python.ops.gen_image_ops import ScaleAndTranslate
from tensorflow.python.ops.gen_image_ops import ScaleAndTranslateGrad
from tensorflow.python.ops.gen_image_ops import StatelessSampleDistortedBoundingBox
from tensorflow.python.ops.gen_io_ops import FixedLengthRecordReader
from tensorflow.python.ops.gen_io_ops import FixedLengthRecordReaderV2
from tensorflow.python.ops.gen_io_ops import IdentityReader
from tensorflow.python.ops.gen_io_ops import IdentityReaderV2
from tensorflow.python.ops.gen_io_ops import LMDBReader
from tensorflow.python.ops.gen_io_ops import MatchingFiles
from tensorflow.python.ops.gen_io_ops import MergeV2Checkpoints
from tensorflow.python.ops.gen_io_ops import ReadFile
from tensorflow.python.ops.gen_io_ops import ReaderNumRecordsProduced
from tensorflow.python.ops.gen_io_ops import ReaderNumRecordsProducedV2
from tensorflow.python.ops.gen_io_ops import ReaderNumWorkUnitsCompleted
from tensorflow.python.ops.gen_io_ops import ReaderNumWorkUnitsCompletedV2
from tensorflow.python.ops.gen_io_ops import ReaderRead
from tensorflow.python.ops.gen_io_ops import ReaderReadUpTo
from tensorflow.python.ops.gen_io_ops import ReaderReadUpToV2
from tensorflow.python.ops.gen_io_ops import ReaderReadV2
from tensorflow.python.ops.gen_io_ops import ReaderReset
from tensorflow.python.ops.gen_io_ops import ReaderResetV2
from tensorflow.python.ops.gen_io_ops import ReaderRestoreState
from tensorflow.python.ops.gen_io_ops import ReaderRestoreStateV2
from tensorflow.python.ops.gen_io_ops import ReaderSerializeState
from tensorflow.python.ops.gen_io_ops import ReaderSerializeStateV2
from tensorflow.python.ops.gen_io_ops import Restore
from tensorflow.python.ops.gen_io_ops import RestoreSlice
from tensorflow.python.ops.gen_io_ops import RestoreV2
from tensorflow.python.ops.gen_io_ops import Save
from tensorflow.python.ops.gen_io_ops import SaveSlices
from tensorflow.python.ops.gen_io_ops import SaveV2
from tensorflow.python.ops.gen_io_ops import ShardedFilename
from tensorflow.python.ops.gen_io_ops import ShardedFilespec
from tensorflow.python.ops.gen_io_ops import TFRecordReader
from tensorflow.python.ops.gen_io_ops import TFRecordReaderV2
from tensorflow.python.ops.gen_io_ops import TextLineReader
from tensorflow.python.ops.gen_io_ops import TextLineReaderV2
from tensorflow.python.ops.gen_io_ops import WholeFileReader
from tensorflow.python.ops.gen_io_ops import WholeFileReaderV2
from tensorflow.python.ops.gen_io_ops import WriteFile
from tensorflow.python.ops.gen_linalg_ops import BandedTriangularSolve
from tensorflow.python.ops.gen_linalg_ops import BatchCholesky
from tensorflow.python.ops.gen_linalg_ops import BatchCholeskyGrad
from tensorflow.python.ops.gen_linalg_ops import BatchMatrixDeterminant
from tensorflow.python.ops.gen_linalg_ops import BatchMatrixInverse
from tensorflow.python.ops.gen_linalg_ops import BatchMatrixSolve
from tensorflow.python.ops.gen_linalg_ops import BatchMatrixSolveLs
from tensorflow.python.ops.gen_linalg_ops import BatchMatrixTriangularSolve
from tensorflow.python.ops.gen_linalg_ops import BatchSelfAdjointEig
from tensorflow.python.ops.gen_linalg_ops import BatchSelfAdjointEigV2
from tensorflow.python.ops.gen_linalg_ops import BatchSvd
from tensorflow.python.ops.gen_linalg_ops import Cholesky
from tensorflow.python.ops.gen_linalg_ops import CholeskyGrad
from tensorflow.python.ops.gen_linalg_ops import Eig
from tensorflow.python.ops.gen_linalg_ops import Einsum
from tensorflow.python.ops.gen_linalg_ops import LogMatrixDeterminant
from tensorflow.python.ops.gen_linalg_ops import Lu
from tensorflow.python.ops.gen_linalg_ops import MatrixDeterminant
from tensorflow.python.ops.gen_linalg_ops import MatrixExponential
from tensorflow.python.ops.gen_linalg_ops import MatrixInverse
from tensorflow.python.ops.gen_linalg_ops import MatrixLogarithm
from tensorflow.python.ops.gen_linalg_ops import MatrixSolve
from tensorflow.python.ops.gen_linalg_ops import MatrixSolveLs
from tensorflow.python.ops.gen_linalg_ops import MatrixSquareRoot
from tensorflow.python.ops.gen_linalg_ops import MatrixTriangularSolve
from tensorflow.python.ops.gen_linalg_ops import Qr
from tensorflow.python.ops.gen_linalg_ops import SelfAdjointEig
from tensorflow.python.ops.gen_linalg_ops import SelfAdjointEigV2
from tensorflow.python.ops.gen_linalg_ops import Svd
from tensorflow.python.ops.gen_linalg_ops import TridiagonalMatMul
from tensorflow.python.ops.gen_linalg_ops import TridiagonalSolve
from tensorflow.python.ops.gen_list_ops import EmptyTensorList
from tensorflow.python.ops.gen_list_ops import TensorListConcat
from tensorflow.python.ops.gen_list_ops import TensorListConcatLists
from tensorflow.python.ops.gen_list_ops import TensorListConcatV2
from tensorflow.python.ops.gen_list_ops import TensorListElementShape
from tensorflow.python.ops.gen_list_ops import TensorListFromTensor
from tensorflow.python.ops.gen_list_ops import TensorListGather
from tensorflow.python.ops.gen_list_ops import TensorListGetItem
from tensorflow.python.ops.gen_list_ops import TensorListLength
from tensorflow.python.ops.gen_list_ops import TensorListPopBack
from tensorflow.python.ops.gen_list_ops import TensorListPushBack
from tensorflow.python.ops.gen_list_ops import TensorListPushBackBatch
from tensorflow.python.ops.gen_list_ops import TensorListReserve
from tensorflow.python.ops.gen_list_ops import TensorListResize
from tensorflow.python.ops.gen_list_ops import TensorListScatter
from tensorflow.python.ops.gen_list_ops import TensorListScatterIntoExistingList
from tensorflow.python.ops.gen_list_ops import TensorListScatterV2
from tensorflow.python.ops.gen_list_ops import TensorListSetItem
from tensorflow.python.ops.gen_list_ops import TensorListSplit
from tensorflow.python.ops.gen_list_ops import TensorListStack
from tensorflow.python.ops.gen_logging_ops import Assert
from tensorflow.python.ops.gen_logging_ops import AudioSummary
from tensorflow.python.ops.gen_logging_ops import AudioSummaryV2
from tensorflow.python.ops.gen_logging_ops import HistogramSummary
from tensorflow.python.ops.gen_logging_ops import ImageSummary
from tensorflow.python.ops.gen_logging_ops import MergeSummary
from tensorflow.python.ops.gen_logging_ops import Print
from tensorflow.python.ops.gen_logging_ops import PrintV2
from tensorflow.python.ops.gen_logging_ops import ScalarSummary
from tensorflow.python.ops.gen_logging_ops import TensorSummary
from tensorflow.python.ops.gen_logging_ops import TensorSummaryV2
from tensorflow.python.ops.gen_logging_ops import Timestamp
from tensorflow.python.ops.gen_lookup_ops import AnonymousHashTable
from tensorflow.python.ops.gen_lookup_ops import AnonymousMutableDenseHashTable
from tensorflow.python.ops.gen_lookup_ops import AnonymousMutableHashTable
from tensorflow.python.ops.gen_lookup_ops import AnonymousMutableHashTableOfTensors
from tensorflow.python.ops.gen_lookup_ops import HashTable
from tensorflow.python.ops.gen_lookup_ops import HashTableV2
from tensorflow.python.ops.gen_lookup_ops import InitializeTable
from tensorflow.python.ops.gen_lookup_ops import InitializeTableFromTextFile
from tensorflow.python.ops.gen_lookup_ops import InitializeTableFromTextFileV2
from tensorflow.python.ops.gen_lookup_ops import InitializeTableV2
from tensorflow.python.ops.gen_lookup_ops import LookupTableExport
from tensorflow.python.ops.gen_lookup_ops import LookupTableExportV2
from tensorflow.python.ops.gen_lookup_ops import LookupTableFind
from tensorflow.python.ops.gen_lookup_ops import LookupTableFindV2
from tensorflow.python.ops.gen_lookup_ops import LookupTableImport
from tensorflow.python.ops.gen_lookup_ops import LookupTableImportV2
from tensorflow.python.ops.gen_lookup_ops import LookupTableInsert
from tensorflow.python.ops.gen_lookup_ops import LookupTableInsertV2
from tensorflow.python.ops.gen_lookup_ops import LookupTableRemoveV2
from tensorflow.python.ops.gen_lookup_ops import LookupTableSize
from tensorflow.python.ops.gen_lookup_ops import LookupTableSizeV2
from tensorflow.python.ops.gen_lookup_ops import MutableDenseHashTable
from tensorflow.python.ops.gen_lookup_ops import MutableDenseHashTableV2
from tensorflow.python.ops.gen_lookup_ops import MutableHashTable
from tensorflow.python.ops.gen_lookup_ops import MutableHashTableOfTensors
from tensorflow.python.ops.gen_lookup_ops import MutableHashTableOfTensorsV2
from tensorflow.python.ops.gen_lookup_ops import MutableHashTableV2
from tensorflow.python.ops.gen_manip_ops import Roll
from tensorflow.python.ops.gen_math_ops import Abs
from tensorflow.python.ops.gen_math_ops import AccumulateNV2
from tensorflow.python.ops.gen_math_ops import Acos
from tensorflow.python.ops.gen_math_ops import Acosh
from tensorflow.python.ops.gen_math_ops import Add
from tensorflow.python.ops.gen_math_ops import AddN
from tensorflow.python.ops.gen_math_ops import AddV2
from tensorflow.python.ops.gen_math_ops import All
from tensorflow.python.ops.gen_math_ops import Angle
from tensorflow.python.ops.gen_math_ops import Any
from tensorflow.python.ops.gen_math_ops import ApproximateEqual
from tensorflow.python.ops.gen_math_ops import ArgMax
from tensorflow.python.ops.gen_math_ops import ArgMin
from tensorflow.python.ops.gen_math_ops import Asin
from tensorflow.python.ops.gen_math_ops import Asinh
from tensorflow.python.ops.gen_math_ops import Atan
from tensorflow.python.ops.gen_math_ops import Atan2
from tensorflow.python.ops.gen_math_ops import Atanh
from tensorflow.python.ops.gen_math_ops import BatchMatMul
from tensorflow.python.ops.gen_math_ops import BatchMatMulV2
from tensorflow.python.ops.gen_math_ops import BatchMatMulV3
from tensorflow.python.ops.gen_math_ops import Betainc
from tensorflow.python.ops.gen_math_ops import Bincount
from tensorflow.python.ops.gen_math_ops import Bucketize
from tensorflow.python.ops.gen_math_ops import Cast
from tensorflow.python.ops.gen_math_ops import Ceil
from tensorflow.python.ops.gen_math_ops import ClipByValue
from tensorflow.python.ops.gen_math_ops import Complex
from tensorflow.python.ops.gen_math_ops import ComplexAbs
from tensorflow.python.ops.gen_math_ops import Conj
from tensorflow.python.ops.gen_math_ops import Cos
from tensorflow.python.ops.gen_math_ops import Cosh
from tensorflow.python.ops.gen_math_ops import Cross
from tensorflow.python.ops.gen_math_ops import Cumprod
from tensorflow.python.ops.gen_math_ops import Cumsum
from tensorflow.python.ops.gen_math_ops import CumulativeLogsumexp
from tensorflow.python.ops.gen_math_ops import DenseBincount
from tensorflow.python.ops.gen_math_ops import Digamma
from tensorflow.python.ops.gen_math_ops import Div
from tensorflow.python.ops.gen_math_ops import DivNoNan
from tensorflow.python.ops.gen_math_ops import Equal
from tensorflow.python.ops.gen_math_ops import Erf
from tensorflow.python.ops.gen_math_ops import Erfc
from tensorflow.python.ops.gen_math_ops import Erfinv
from tensorflow.python.ops.gen_math_ops import EuclideanNorm
from tensorflow.python.ops.gen_math_ops import Exp
from tensorflow.python.ops.gen_math_ops import Expm1
from tensorflow.python.ops.gen_math_ops import Floor
from tensorflow.python.ops.gen_math_ops import FloorDiv
from tensorflow.python.ops.gen_math_ops import FloorMod
from tensorflow.python.ops.gen_math_ops import Greater
from tensorflow.python.ops.gen_math_ops import GreaterEqual
from tensorflow.python.ops.gen_math_ops import HistogramFixedWidth
from tensorflow.python.ops.gen_math_ops import Igamma
from tensorflow.python.ops.gen_math_ops import IgammaGradA
from tensorflow.python.ops.gen_math_ops import Igammac
from tensorflow.python.ops.gen_math_ops import Imag
from tensorflow.python.ops.gen_math_ops import Inv
from tensorflow.python.ops.gen_math_ops import InvGrad
from tensorflow.python.ops.gen_math_ops import IsFinite
from tensorflow.python.ops.gen_math_ops import IsInf
from tensorflow.python.ops.gen_math_ops import IsNan
from tensorflow.python.ops.gen_math_ops import Less
from tensorflow.python.ops.gen_math_ops import LessEqual
from tensorflow.python.ops.gen_math_ops import Lgamma
from tensorflow.python.ops.gen_math_ops import LinSpace
from tensorflow.python.ops.gen_math_ops import Log
from tensorflow.python.ops.gen_math_ops import Log1p
from tensorflow.python.ops.gen_math_ops import LogicalAnd
from tensorflow.python.ops.gen_math_ops import LogicalNot
from tensorflow.python.ops.gen_math_ops import LogicalOr
from tensorflow.python.ops.gen_math_ops import MatMul
from tensorflow.python.ops.gen_math_ops import Max
from tensorflow.python.ops.gen_math_ops import Maximum
from tensorflow.python.ops.gen_math_ops import Mean
from tensorflow.python.ops.gen_math_ops import Min
from tensorflow.python.ops.gen_math_ops import Minimum
from tensorflow.python.ops.gen_math_ops import Mod
from tensorflow.python.ops.gen_math_ops import Mul
from tensorflow.python.ops.gen_math_ops import MulNoNan
from tensorflow.python.ops.gen_math_ops import Ndtri
from tensorflow.python.ops.gen_math_ops import Neg
from tensorflow.python.ops.gen_math_ops import NextAfter
from tensorflow.python.ops.gen_math_ops import NotEqual
from tensorflow.python.ops.gen_math_ops import Polygamma
from tensorflow.python.ops.gen_math_ops import Pow
from tensorflow.python.ops.gen_math_ops import Prod
from tensorflow.python.ops.gen_math_ops import QuantizeDownAndShrinkRange
from tensorflow.python.ops.gen_math_ops import QuantizedAdd
from tensorflow.python.ops.gen_math_ops import QuantizedMatMul
from tensorflow.python.ops.gen_math_ops import QuantizedMul
from tensorflow.python.ops.gen_math_ops import RaggedBincount
from tensorflow.python.ops.gen_math_ops import Range
from tensorflow.python.ops.gen_math_ops import Real
from tensorflow.python.ops.gen_math_ops import RealDiv
from tensorflow.python.ops.gen_math_ops import Reciprocal
from tensorflow.python.ops.gen_math_ops import ReciprocalGrad
from tensorflow.python.ops.gen_math_ops import RequantizationRange
from tensorflow.python.ops.gen_math_ops import RequantizationRangePerChannel
from tensorflow.python.ops.gen_math_ops import Requantize
from tensorflow.python.ops.gen_math_ops import RequantizePerChannel
from tensorflow.python.ops.gen_math_ops import Rint
from tensorflow.python.ops.gen_math_ops import Round
from tensorflow.python.ops.gen_math_ops import Rsqrt
from tensorflow.python.ops.gen_math_ops import RsqrtGrad
from tensorflow.python.ops.gen_math_ops import SegmentMax
from tensorflow.python.ops.gen_math_ops import SegmentMean
from tensorflow.python.ops.gen_math_ops import SegmentMin
from tensorflow.python.ops.gen_math_ops import SegmentProd
from tensorflow.python.ops.gen_math_ops import SegmentSum
from tensorflow.python.ops.gen_math_ops import Select
from tensorflow.python.ops.gen_math_ops import SelectV2
from tensorflow.python.ops.gen_math_ops import Sigmoid
from tensorflow.python.ops.gen_math_ops import SigmoidGrad
from tensorflow.python.ops.gen_math_ops import Sign
from tensorflow.python.ops.gen_math_ops import Sin
from tensorflow.python.ops.gen_math_ops import Sinh
from tensorflow.python.ops.gen_math_ops import SobolSample
from tensorflow.python.ops.gen_math_ops import SparseBincount
from tensorflow.python.ops.gen_math_ops import SparseMatMul
from tensorflow.python.ops.gen_math_ops import SparseSegmentMean
from tensorflow.python.ops.gen_math_ops import SparseSegmentMeanGrad
from tensorflow.python.ops.gen_math_ops import SparseSegmentMeanWithNumSegments
from tensorflow.python.ops.gen_math_ops import SparseSegmentSqrtN
from tensorflow.python.ops.gen_math_ops import SparseSegmentSqrtNGrad
from tensorflow.python.ops.gen_math_ops import SparseSegmentSqrtNWithNumSegments
from tensorflow.python.ops.gen_math_ops import SparseSegmentSum
from tensorflow.python.ops.gen_math_ops import SparseSegmentSumGrad
from tensorflow.python.ops.gen_math_ops import SparseSegmentSumWithNumSegments
from tensorflow.python.ops.gen_math_ops import Sqrt
from tensorflow.python.ops.gen_math_ops import SqrtGrad
from tensorflow.python.ops.gen_math_ops import Square
from tensorflow.python.ops.gen_math_ops import SquaredDifference
from tensorflow.python.ops.gen_math_ops import Sub
from tensorflow.python.ops.gen_math_ops import Sum
from tensorflow.python.ops.gen_math_ops import Tan
from tensorflow.python.ops.gen_math_ops import Tanh
from tensorflow.python.ops.gen_math_ops import TanhGrad
from tensorflow.python.ops.gen_math_ops import TruncateDiv
from tensorflow.python.ops.gen_math_ops import TruncateMod
from tensorflow.python.ops.gen_math_ops import UnsortedSegmentMax
from tensorflow.python.ops.gen_math_ops import UnsortedSegmentMin
from tensorflow.python.ops.gen_math_ops import UnsortedSegmentProd
from tensorflow.python.ops.gen_math_ops import UnsortedSegmentSum
from tensorflow.python.ops.gen_math_ops import Xdivy
from tensorflow.python.ops.gen_math_ops import Xlog1py
from tensorflow.python.ops.gen_math_ops import Xlogy
from tensorflow.python.ops.gen_math_ops import Zeta
from tensorflow.python.ops.gen_nccl_ops import NcclAllReduce
from tensorflow.python.ops.gen_nccl_ops import NcclBroadcast
from tensorflow.python.ops.gen_nccl_ops import NcclReduce
from tensorflow.python.ops.gen_nn_ops import AvgPool
from tensorflow.python.ops.gen_nn_ops import AvgPool3D
from tensorflow.python.ops.gen_nn_ops import AvgPool3DGrad
from tensorflow.python.ops.gen_nn_ops import AvgPoolGrad
from tensorflow.python.ops.gen_nn_ops import BatchNormWithGlobalNormalization
from tensorflow.python.ops.gen_nn_ops import BatchNormWithGlobalNormalizationGrad
from tensorflow.python.ops.gen_nn_ops import BiasAdd
from tensorflow.python.ops.gen_nn_ops import BiasAddGrad
from tensorflow.python.ops.gen_nn_ops import BiasAddV1
from tensorflow.python.ops.gen_nn_ops import Conv2D
from tensorflow.python.ops.gen_nn_ops import Conv2DBackpropFilter
from tensorflow.python.ops.gen_nn_ops import Conv2DBackpropInput
from tensorflow.python.ops.gen_nn_ops import Conv3D
from tensorflow.python.ops.gen_nn_ops import Conv3DBackpropFilter
from tensorflow.python.ops.gen_nn_ops import Conv3DBackpropFilterV2
from tensorflow.python.ops.gen_nn_ops import Conv3DBackpropInput
from tensorflow.python.ops.gen_nn_ops import Conv3DBackpropInputV2
from tensorflow.python.ops.gen_nn_ops import DataFormatDimMap
from tensorflow.python.ops.gen_nn_ops import DataFormatVecPermute
from tensorflow.python.ops.gen_nn_ops import DepthwiseConv2dNative
from tensorflow.python.ops.gen_nn_ops import DepthwiseConv2dNativeBackpropFilter
from tensorflow.python.ops.gen_nn_ops import DepthwiseConv2dNativeBackpropInput
from tensorflow.python.ops.gen_nn_ops import Dilation2D
from tensorflow.python.ops.gen_nn_ops import Dilation2DBackpropFilter
from tensorflow.python.ops.gen_nn_ops import Dilation2DBackpropInput
from tensorflow.python.ops.gen_nn_ops import Elu
from tensorflow.python.ops.gen_nn_ops import EluGrad
from tensorflow.python.ops.gen_nn_ops import FractionalAvgPool
from tensorflow.python.ops.gen_nn_ops import FractionalAvgPoolGrad
from tensorflow.python.ops.gen_nn_ops import FractionalMaxPool
from tensorflow.python.ops.gen_nn_ops import FractionalMaxPoolGrad
from tensorflow.python.ops.gen_nn_ops import FusedBatchNorm
from tensorflow.python.ops.gen_nn_ops import FusedBatchNormGrad
from tensorflow.python.ops.gen_nn_ops import FusedBatchNormGradV2
from tensorflow.python.ops.gen_nn_ops import FusedBatchNormGradV3
from tensorflow.python.ops.gen_nn_ops import FusedBatchNormV2
from tensorflow.python.ops.gen_nn_ops import FusedBatchNormV3
from tensorflow.python.ops.gen_nn_ops import FusedPadConv2D
from tensorflow.python.ops.gen_nn_ops import FusedResizeAndPadConv2D
from tensorflow.python.ops.gen_nn_ops import InTopK
from tensorflow.python.ops.gen_nn_ops import InTopKV2
from tensorflow.python.ops.gen_nn_ops import IsotonicRegression
from tensorflow.python.ops.gen_nn_ops import L2Loss
from tensorflow.python.ops.gen_nn_ops import LRN
from tensorflow.python.ops.gen_nn_ops import LRNGrad
from tensorflow.python.ops.gen_nn_ops import LeakyRelu
from tensorflow.python.ops.gen_nn_ops import LeakyReluGrad
from tensorflow.python.ops.gen_nn_ops import LogSoftmax
from tensorflow.python.ops.gen_nn_ops import MaxPool
from tensorflow.python.ops.gen_nn_ops import MaxPool3D
from tensorflow.python.ops.gen_nn_ops import MaxPool3DGrad
from tensorflow.python.ops.gen_nn_ops import MaxPool3DGradGrad
from tensorflow.python.ops.gen_nn_ops import MaxPoolGrad
from tensorflow.python.ops.gen_nn_ops import MaxPoolGradGrad
from tensorflow.python.ops.gen_nn_ops import MaxPoolGradGradV2
from tensorflow.python.ops.gen_nn_ops import MaxPoolGradGradWithArgmax
from tensorflow.python.ops.gen_nn_ops import MaxPoolGradV2
from tensorflow.python.ops.gen_nn_ops import MaxPoolGradWithArgmax
from tensorflow.python.ops.gen_nn_ops import MaxPoolV2
from tensorflow.python.ops.gen_nn_ops import MaxPoolWithArgmax
from tensorflow.python.ops.gen_nn_ops import NthElement
from tensorflow.python.ops.gen_nn_ops import QuantizedAvgPool
from tensorflow.python.ops.gen_nn_ops import QuantizedBatchNormWithGlobalNormalization
from tensorflow.python.ops.gen_nn_ops import QuantizedBiasAdd
from tensorflow.python.ops.gen_nn_ops import QuantizedConv2D
from tensorflow.python.ops.gen_nn_ops import QuantizedConv2DAndRelu
from tensorflow.python.ops.gen_nn_ops import QuantizedConv2DAndReluAndRequantize
from tensorflow.python.ops.gen_nn_ops import QuantizedConv2DAndRequantize
from tensorflow.python.ops.gen_nn_ops import QuantizedConv2DPerChannel
from tensorflow.python.ops.gen_nn_ops import QuantizedConv2DWithBias
from tensorflow.python.ops.gen_nn_ops import QuantizedConv2DWithBiasAndRelu
from tensorflow.python.ops.gen_nn_ops import QuantizedConv2DWithBiasAndReluAndRequantize
from tensorflow.python.ops.gen_nn_ops import QuantizedConv2DWithBiasAndRequantize
from tensorflow.python.ops.gen_nn_ops import QuantizedConv2DWithBiasSignedSumAndReluAndRequantize
from tensorflow.python.ops.gen_nn_ops import QuantizedConv2DWithBiasSumAndRelu
from tensorflow.python.ops.gen_nn_ops import QuantizedConv2DWithBiasSumAndReluAndRequantize
from tensorflow.python.ops.gen_nn_ops import QuantizedDepthwiseConv2D
from tensorflow.python.ops.gen_nn_ops import QuantizedDepthwiseConv2DWithBias
from tensorflow.python.ops.gen_nn_ops import QuantizedDepthwiseConv2DWithBiasAndRelu
from tensorflow.python.ops.gen_nn_ops import QuantizedDepthwiseConv2DWithBiasAndReluAndRequantize
from tensorflow.python.ops.gen_nn_ops import QuantizedMatMulWithBias
from tensorflow.python.ops.gen_nn_ops import QuantizedMatMulWithBiasAndDequantize
from tensorflow.python.ops.gen_nn_ops import QuantizedMatMulWithBiasAndRelu
from tensorflow.python.ops.gen_nn_ops import QuantizedMatMulWithBiasAndReluAndRequantize
from tensorflow.python.ops.gen_nn_ops import QuantizedMatMulWithBiasAndRequantize
from tensorflow.python.ops.gen_nn_ops import QuantizedMaxPool
from tensorflow.python.ops.gen_nn_ops import QuantizedRelu
from tensorflow.python.ops.gen_nn_ops import QuantizedRelu6
from tensorflow.python.ops.gen_nn_ops import QuantizedReluX
from tensorflow.python.ops.gen_nn_ops import Relu
from tensorflow.python.ops.gen_nn_ops import Relu6
from tensorflow.python.ops.gen_nn_ops import Relu6Grad
from tensorflow.python.ops.gen_nn_ops import ReluGrad
from tensorflow.python.ops.gen_nn_ops import Selu
from tensorflow.python.ops.gen_nn_ops import SeluGrad
from tensorflow.python.ops.gen_nn_ops import Softmax
from tensorflow.python.ops.gen_nn_ops import SoftmaxCrossEntropyWithLogits
from tensorflow.python.ops.gen_nn_ops import Softplus
from tensorflow.python.ops.gen_nn_ops import SoftplusGrad
from tensorflow.python.ops.gen_nn_ops import Softsign
from tensorflow.python.ops.gen_nn_ops import SoftsignGrad
from tensorflow.python.ops.gen_nn_ops import SparseSoftmaxCrossEntropyWithLogits
from tensorflow.python.ops.gen_nn_ops import TopK
from tensorflow.python.ops.gen_nn_ops import TopKV2
from tensorflow.python.ops.gen_parsing_ops import DecodeCSV
from tensorflow.python.ops.gen_parsing_ops import DecodeCompressed
from tensorflow.python.ops.gen_parsing_ops import DecodeJSONExample
from tensorflow.python.ops.gen_parsing_ops import DecodePaddedRaw
from tensorflow.python.ops.gen_parsing_ops import DecodeRaw
from tensorflow.python.ops.gen_parsing_ops import ParseExample
from tensorflow.python.ops.gen_parsing_ops import ParseExampleV2
from tensorflow.python.ops.gen_parsing_ops import ParseSequenceExample
from tensorflow.python.ops.gen_parsing_ops import ParseSequenceExampleV2
from tensorflow.python.ops.gen_parsing_ops import ParseSingleExample
from tensorflow.python.ops.gen_parsing_ops import ParseSingleSequenceExample
from tensorflow.python.ops.gen_parsing_ops import ParseTensor
from tensorflow.python.ops.gen_parsing_ops import SerializeTensor
from tensorflow.python.ops.gen_parsing_ops import StringToNumber
from tensorflow.python.ops.gen_ragged_array_ops import RaggedCross
from tensorflow.python.ops.gen_ragged_array_ops import RaggedGather
from tensorflow.python.ops.gen_ragged_conversion_ops import RaggedTensorFromVariant
from tensorflow.python.ops.gen_ragged_conversion_ops import RaggedTensorToSparse
from tensorflow.python.ops.gen_ragged_conversion_ops import RaggedTensorToTensor
from tensorflow.python.ops.gen_ragged_conversion_ops import RaggedTensorToVariant
from tensorflow.python.ops.gen_ragged_conversion_ops import RaggedTensorToVariantGradient
from tensorflow.python.ops.gen_ragged_math_ops import RaggedRange
from tensorflow.python.ops.gen_random_index_shuffle_ops import RandomIndexShuffle
from tensorflow.python.ops.gen_random_ops import Multinomial
from tensorflow.python.ops.gen_random_ops import ParameterizedTruncatedNormal
from tensorflow.python.ops.gen_random_ops import RandomGamma
from tensorflow.python.ops.gen_random_ops import RandomGammaGrad
from tensorflow.python.ops.gen_random_ops import RandomPoisson
from tensorflow.python.ops.gen_random_ops import RandomPoissonV2
from tensorflow.python.ops.gen_random_ops import RandomShuffle
from tensorflow.python.ops.gen_random_ops import RandomStandardNormal
from tensorflow.python.ops.gen_random_ops import RandomUniform
from tensorflow.python.ops.gen_random_ops import RandomUniformInt
from tensorflow.python.ops.gen_random_ops import TruncatedNormal
from tensorflow.python.ops.gen_resource_variable_ops import AssignAddVariableOp
from tensorflow.python.ops.gen_resource_variable_ops import AssignSubVariableOp
from tensorflow.python.ops.gen_resource_variable_ops import AssignVariableOp
from tensorflow.python.ops.gen_resource_variable_ops import ConsumeMutexLock
from tensorflow.python.ops.gen_resource_variable_ops import DestroyResourceOp
from tensorflow.python.ops.gen_resource_variable_ops import MutexLock
from tensorflow.python.ops.gen_resource_variable_ops import MutexV2
from tensorflow.python.ops.gen_resource_variable_ops import ReadVariableOp
from tensorflow.python.ops.gen_resource_variable_ops import ResourceGather
from tensorflow.python.ops.gen_resource_variable_ops import ResourceGatherNd
from tensorflow.python.ops.gen_resource_variable_ops import ResourceScatterAdd
from tensorflow.python.ops.gen_resource_variable_ops import ResourceScatterDiv
from tensorflow.python.ops.gen_resource_variable_ops import ResourceScatterMax
from tensorflow.python.ops.gen_resource_variable_ops import ResourceScatterMin
from tensorflow.python.ops.gen_resource_variable_ops import ResourceScatterMul
from tensorflow.python.ops.gen_resource_variable_ops import ResourceScatterSub
from tensorflow.python.ops.gen_resource_variable_ops import ResourceScatterUpdate
from tensorflow.python.ops.gen_resource_variable_ops import VarHandleOp
from tensorflow.python.ops.gen_resource_variable_ops import VarIsInitializedOp
from tensorflow.python.ops.gen_resource_variable_ops import VariableShape
from tensorflow.python.ops.gen_rnn_ops import BlockLSTM
from tensorflow.python.ops.gen_rnn_ops import BlockLSTMGrad
from tensorflow.python.ops.gen_rnn_ops import BlockLSTMGradV2
from tensorflow.python.ops.gen_rnn_ops import BlockLSTMV2
from tensorflow.python.ops.gen_rnn_ops import GRUBlockCell
from tensorflow.python.ops.gen_rnn_ops import GRUBlockCellGrad
from tensorflow.python.ops.gen_rnn_ops import LSTMBlockCell
from tensorflow.python.ops.gen_rnn_ops import LSTMBlockCellGrad
from tensorflow.python.ops.gen_script_ops import EagerPyFunc
from tensorflow.python.ops.gen_script_ops import PyFunc
from tensorflow.python.ops.gen_script_ops import PyFuncStateless
from tensorflow.python.ops.gen_sdca_ops import SdcaFprint
from tensorflow.python.ops.gen_sdca_ops import SdcaOptimizer
from tensorflow.python.ops.gen_sdca_ops import SdcaOptimizerV2
from tensorflow.python.ops.gen_sdca_ops import SdcaShrinkL1
from tensorflow.python.ops.gen_sendrecv_ops import Recv
from tensorflow.python.ops.gen_sendrecv_ops import Send
from tensorflow.python.ops.gen_set_ops import DenseToDenseSetOperation
from tensorflow.python.ops.gen_set_ops import DenseToSparseSetOperation
from tensorflow.python.ops.gen_set_ops import SetSize
from tensorflow.python.ops.gen_set_ops import SparseToSparseSetOperation
from tensorflow.python.ops.gen_sparse_ops import AddManySparseToTensorsMap
from tensorflow.python.ops.gen_sparse_ops import AddSparseToTensorsMap
from tensorflow.python.ops.gen_sparse_ops import DeserializeManySparse
from tensorflow.python.ops.gen_sparse_ops import DeserializeSparse
from tensorflow.python.ops.gen_sparse_ops import SerializeManySparse
from tensorflow.python.ops.gen_sparse_ops import SerializeSparse
from tensorflow.python.ops.gen_sparse_ops import SparseAdd
from tensorflow.python.ops.gen_sparse_ops import SparseAddGrad
from tensorflow.python.ops.gen_sparse_ops import SparseConcat
from tensorflow.python.ops.gen_sparse_ops import SparseCross
from tensorflow.python.ops.gen_sparse_ops import SparseCrossHashed
from tensorflow.python.ops.gen_sparse_ops import SparseCrossV2
from tensorflow.python.ops.gen_sparse_ops import SparseDenseCwiseAdd
from tensorflow.python.ops.gen_sparse_ops import SparseDenseCwiseDiv
from tensorflow.python.ops.gen_sparse_ops import SparseDenseCwiseMul
from tensorflow.python.ops.gen_sparse_ops import SparseFillEmptyRows
from tensorflow.python.ops.gen_sparse_ops import SparseFillEmptyRowsGrad
from tensorflow.python.ops.gen_sparse_ops import SparseReduceMax
from tensorflow.python.ops.gen_sparse_ops import SparseReduceMaxSparse
from tensorflow.python.ops.gen_sparse_ops import SparseReduceSum
from tensorflow.python.ops.gen_sparse_ops import SparseReduceSumSparse
from tensorflow.python.ops.gen_sparse_ops import SparseReorder
from tensorflow.python.ops.gen_sparse_ops import SparseReshape
from tensorflow.python.ops.gen_sparse_ops import SparseSlice
from tensorflow.python.ops.gen_sparse_ops import SparseSliceGrad
from tensorflow.python.ops.gen_sparse_ops import SparseSoftmax
from tensorflow.python.ops.gen_sparse_ops import SparseSparseMaximum
from tensorflow.python.ops.gen_sparse_ops import SparseSparseMinimum
from tensorflow.python.ops.gen_sparse_ops import SparseSplit
from tensorflow.python.ops.gen_sparse_ops import SparseTensorDenseAdd
from tensorflow.python.ops.gen_sparse_ops import SparseTensorDenseMatMul
from tensorflow.python.ops.gen_sparse_ops import SparseToDense
from tensorflow.python.ops.gen_sparse_ops import TakeManySparseFromTensorsMap
from tensorflow.python.ops.gen_special_math_ops import BesselI0
from tensorflow.python.ops.gen_special_math_ops import BesselI0e
from tensorflow.python.ops.gen_special_math_ops import BesselI1
from tensorflow.python.ops.gen_special_math_ops import BesselI1e
from tensorflow.python.ops.gen_special_math_ops import BesselJ0
from tensorflow.python.ops.gen_special_math_ops import BesselJ1
from tensorflow.python.ops.gen_special_math_ops import BesselK0
from tensorflow.python.ops.gen_special_math_ops import BesselK0e
from tensorflow.python.ops.gen_special_math_ops import BesselK1
from tensorflow.python.ops.gen_special_math_ops import BesselK1e
from tensorflow.python.ops.gen_special_math_ops import BesselY0
from tensorflow.python.ops.gen_special_math_ops import BesselY1
from tensorflow.python.ops.gen_special_math_ops import Dawsn
from tensorflow.python.ops.gen_special_math_ops import Expint
from tensorflow.python.ops.gen_special_math_ops import FresnelCos
from tensorflow.python.ops.gen_special_math_ops import FresnelSin
from tensorflow.python.ops.gen_special_math_ops import Spence
from tensorflow.python.ops.gen_spectral_ops import BatchFFT
from tensorflow.python.ops.gen_spectral_ops import BatchFFT2D
from tensorflow.python.ops.gen_spectral_ops import BatchFFT3D
from tensorflow.python.ops.gen_spectral_ops import BatchIFFT
from tensorflow.python.ops.gen_spectral_ops import BatchIFFT2D
from tensorflow.python.ops.gen_spectral_ops import BatchIFFT3D
from tensorflow.python.ops.gen_spectral_ops import FFT
from tensorflow.python.ops.gen_spectral_ops import FFT2D
from tensorflow.python.ops.gen_spectral_ops import FFT3D
from tensorflow.python.ops.gen_spectral_ops import IFFT
from tensorflow.python.ops.gen_spectral_ops import IFFT2D
from tensorflow.python.ops.gen_spectral_ops import IFFT3D
from tensorflow.python.ops.gen_spectral_ops import IRFFT
from tensorflow.python.ops.gen_spectral_ops import IRFFT2D
from tensorflow.python.ops.gen_spectral_ops import IRFFT3D
from tensorflow.python.ops.gen_spectral_ops import RFFT
from tensorflow.python.ops.gen_spectral_ops import RFFT2D
from tensorflow.python.ops.gen_spectral_ops import RFFT3D
from tensorflow.python.ops.gen_state_ops import Assign
from tensorflow.python.ops.gen_state_ops import AssignAdd
from tensorflow.python.ops.gen_state_ops import AssignSub
from tensorflow.python.ops.gen_state_ops import CountUpTo
from tensorflow.python.ops.gen_state_ops import DestroyTemporaryVariable
from tensorflow.python.ops.gen_state_ops import IsVariableInitialized
from tensorflow.python.ops.gen_state_ops import ResourceCountUpTo
from tensorflow.python.ops.gen_state_ops import ResourceScatterNdAdd
from tensorflow.python.ops.gen_state_ops import ResourceScatterNdMax
from tensorflow.python.ops.gen_state_ops import ResourceScatterNdMin
from tensorflow.python.ops.gen_state_ops import ResourceScatterNdSub
from tensorflow.python.ops.gen_state_ops import ResourceScatterNdUpdate
from tensorflow.python.ops.gen_state_ops import ScatterAdd
from tensorflow.python.ops.gen_state_ops import ScatterDiv
from tensorflow.python.ops.gen_state_ops import ScatterMax
from tensorflow.python.ops.gen_state_ops import ScatterMin
from tensorflow.python.ops.gen_state_ops import ScatterMul
from tensorflow.python.ops.gen_state_ops import ScatterNdAdd
from tensorflow.python.ops.gen_state_ops import ScatterNdMax
from tensorflow.python.ops.gen_state_ops import ScatterNdMin
from tensorflow.python.ops.gen_state_ops import ScatterNdSub
from tensorflow.python.ops.gen_state_ops import ScatterNdUpdate
from tensorflow.python.ops.gen_state_ops import ScatterSub
from tensorflow.python.ops.gen_state_ops import ScatterUpdate
from tensorflow.python.ops.gen_state_ops import TemporaryVariable
from tensorflow.python.ops.gen_state_ops import Variable
from tensorflow.python.ops.gen_state_ops import VariableV2
from tensorflow.python.ops.gen_stateful_random_ops import NonDeterministicInts
from tensorflow.python.ops.gen_stateful_random_ops import RngReadAndSkip
from tensorflow.python.ops.gen_stateful_random_ops import RngSkip
from tensorflow.python.ops.gen_stateful_random_ops import StatefulRandomBinomial
from tensorflow.python.ops.gen_stateful_random_ops import StatefulStandardNormal
from tensorflow.python.ops.gen_stateful_random_ops import StatefulStandardNormalV2
from tensorflow.python.ops.gen_stateful_random_ops import StatefulTruncatedNormal
from tensorflow.python.ops.gen_stateful_random_ops import StatefulUniform
from tensorflow.python.ops.gen_stateful_random_ops import StatefulUniformFullInt
from tensorflow.python.ops.gen_stateful_random_ops import StatefulUniformInt
from tensorflow.python.ops.gen_stateless_random_ops import StatelessMultinomial
from tensorflow.python.ops.gen_stateless_random_ops import StatelessParameterizedTruncatedNormal
from tensorflow.python.ops.gen_stateless_random_ops import StatelessRandomBinomial
from tensorflow.python.ops.gen_stateless_random_ops import StatelessRandomGammaV2
from tensorflow.python.ops.gen_stateless_random_ops import StatelessRandomNormal
from tensorflow.python.ops.gen_stateless_random_ops import StatelessRandomPoisson
from tensorflow.python.ops.gen_stateless_random_ops import StatelessRandomUniform
from tensorflow.python.ops.gen_stateless_random_ops import StatelessRandomUniformFullInt
from tensorflow.python.ops.gen_stateless_random_ops import StatelessRandomUniformInt
from tensorflow.python.ops.gen_stateless_random_ops import StatelessTruncatedNormal
from tensorflow.python.ops.gen_stateless_random_ops_v2 import StatelessRandomGetAlg
from tensorflow.python.ops.gen_stateless_random_ops_v2 import StatelessRandomGetKeyCounter
from tensorflow.python.ops.gen_stateless_random_ops_v2 import StatelessRandomGetKeyCounterAlg
from tensorflow.python.ops.gen_stateless_random_ops_v2 import StatelessRandomNormalV2
from tensorflow.python.ops.gen_stateless_random_ops_v2 import StatelessRandomUniformFullIntV2
from tensorflow.python.ops.gen_stateless_random_ops_v2 import StatelessRandomUniformIntV2
from tensorflow.python.ops.gen_stateless_random_ops_v2 import StatelessRandomUniformV2
from tensorflow.python.ops.gen_stateless_random_ops_v2 import StatelessTruncatedNormalV2
from tensorflow.python.ops.gen_string_ops import AsString
from tensorflow.python.ops.gen_string_ops import DecodeBase64
from tensorflow.python.ops.gen_string_ops import EncodeBase64
from tensorflow.python.ops.gen_string_ops import ReduceJoin
from tensorflow.python.ops.gen_string_ops import RegexFullMatch
from tensorflow.python.ops.gen_string_ops import RegexReplace
from tensorflow.python.ops.gen_string_ops import StaticRegexFullMatch
from tensorflow.python.ops.gen_string_ops import StaticRegexReplace
from tensorflow.python.ops.gen_string_ops import StringFormat
from tensorflow.python.ops.gen_string_ops import StringJoin
from tensorflow.python.ops.gen_string_ops import StringLength
from tensorflow.python.ops.gen_string_ops import StringLower
from tensorflow.python.ops.gen_string_ops import StringNGrams
from tensorflow.python.ops.gen_string_ops import StringSplit
from tensorflow.python.ops.gen_string_ops import StringSplitV2
from tensorflow.python.ops.gen_string_ops import StringStrip
from tensorflow.python.ops.gen_string_ops import StringToHashBucket
from tensorflow.python.ops.gen_string_ops import StringToHashBucketFast
from tensorflow.python.ops.gen_string_ops import StringToHashBucketStrong
from tensorflow.python.ops.gen_string_ops import StringUpper
from tensorflow.python.ops.gen_string_ops import Substr
from tensorflow.python.ops.gen_string_ops import UnicodeDecode
from tensorflow.python.ops.gen_string_ops import UnicodeDecodeWithOffsets
from tensorflow.python.ops.gen_string_ops import UnicodeEncode
from tensorflow.python.ops.gen_string_ops import UnicodeScript
from tensorflow.python.ops.gen_string_ops import UnicodeTranscode
from tensorflow.python.ops.gen_string_ops import UnsortedSegmentJoin
from tensorflow.python.ops.gen_summary_ops import CloseSummaryWriter
from tensorflow.python.ops.gen_summary_ops import CreateSummaryDbWriter
from tensorflow.python.ops.gen_summary_ops import CreateSummaryFileWriter
from tensorflow.python.ops.gen_summary_ops import FlushSummaryWriter
from tensorflow.python.ops.gen_summary_ops import ImportEvent
from tensorflow.python.ops.gen_summary_ops import SummaryWriter
from tensorflow.python.ops.gen_summary_ops import WriteAudioSummary
from tensorflow.python.ops.gen_summary_ops import WriteGraphSummary
from tensorflow.python.ops.gen_summary_ops import WriteHistogramSummary
from tensorflow.python.ops.gen_summary_ops import WriteImageSummary
from tensorflow.python.ops.gen_summary_ops import WriteRawProtoSummary
from tensorflow.python.ops.gen_summary_ops import WriteScalarSummary
from tensorflow.python.ops.gen_summary_ops import WriteSummary
from tensorflow.python.ops.gen_tpu_ops import AllToAll
from tensorflow.python.ops.gen_tpu_ops import AssignVariableXlaConcatND
from tensorflow.python.ops.gen_tpu_ops import CollectivePermute
from tensorflow.python.ops.gen_tpu_ops import ConfigureDistributedTPU
from tensorflow.python.ops.gen_tpu_ops import ConfigureTPUEmbedding
from tensorflow.python.ops.gen_tpu_ops import CrossReplicaSum
from tensorflow.python.ops.gen_tpu_ops import DynamicEnqueueTPUEmbeddingArbitraryTensorBatch
from tensorflow.python.ops.gen_tpu_ops import EnqueueTPUEmbeddingArbitraryTensorBatch
from tensorflow.python.ops.gen_tpu_ops import EnqueueTPUEmbeddingIntegerBatch
from tensorflow.python.ops.gen_tpu_ops import EnqueueTPUEmbeddingRaggedTensorBatch
from tensorflow.python.ops.gen_tpu_ops import EnqueueTPUEmbeddingSparseBatch
from tensorflow.python.ops.gen_tpu_ops import EnqueueTPUEmbeddingSparseTensorBatch
from tensorflow.python.ops.gen_tpu_ops import InfeedDequeue
from tensorflow.python.ops.gen_tpu_ops import InfeedDequeueTuple
from tensorflow.python.ops.gen_tpu_ops import InfeedEnqueue
from tensorflow.python.ops.gen_tpu_ops import InfeedEnqueuePrelinearizedBuffer
from tensorflow.python.ops.gen_tpu_ops import InfeedEnqueueTuple
from tensorflow.python.ops.gen_tpu_ops import IsTPUEmbeddingInitialized
from tensorflow.python.ops.gen_tpu_ops import LoadTPUEmbeddingADAMParameters
from tensorflow.python.ops.gen_tpu_ops import LoadTPUEmbeddingAdadeltaParameters
from tensorflow.python.ops.gen_tpu_ops import LoadTPUEmbeddingAdagradMomentumParameters
from tensorflow.python.ops.gen_tpu_ops import LoadTPUEmbeddingAdagradParameters
from tensorflow.python.ops.gen_tpu_ops import LoadTPUEmbeddingCenteredRMSPropParameters
from tensorflow.python.ops.gen_tpu_ops import LoadTPUEmbeddingFTRLParameters
from tensorflow.python.ops.gen_tpu_ops import LoadTPUEmbeddingFrequencyEstimatorParameters
from tensorflow.python.ops.gen_tpu_ops import LoadTPUEmbeddingMDLAdagradLightParameters
from tensorflow.python.ops.gen_tpu_ops import LoadTPUEmbeddingMomentumParameters
from tensorflow.python.ops.gen_tpu_ops import LoadTPUEmbeddingProximalAdagradParameters
from tensorflow.python.ops.gen_tpu_ops import LoadTPUEmbeddingProximalYogiParameters
from tensorflow.python.ops.gen_tpu_ops import LoadTPUEmbeddingRMSPropParameters
from tensorflow.python.ops.gen_tpu_ops import LoadTPUEmbeddingStochasticGradientDescentParameters
from tensorflow.python.ops.gen_tpu_ops import OutfeedDequeue
from tensorflow.python.ops.gen_tpu_ops import OutfeedDequeueTuple
from tensorflow.python.ops.gen_tpu_ops import OutfeedDequeueTupleV2
from tensorflow.python.ops.gen_tpu_ops import OutfeedDequeueV2
from tensorflow.python.ops.gen_tpu_ops import OutfeedEnqueue
from tensorflow.python.ops.gen_tpu_ops import OutfeedEnqueueTuple
from tensorflow.python.ops.gen_tpu_ops import Prelinearize
from tensorflow.python.ops.gen_tpu_ops import PrelinearizeTuple
from tensorflow.python.ops.gen_tpu_ops import ReadVariableXlaSplitND
from tensorflow.python.ops.gen_tpu_ops import RecvTPUEmbeddingActivations
from tensorflow.python.ops.gen_tpu_ops import RetrieveTPUEmbeddingADAMParameters
from tensorflow.python.ops.gen_tpu_ops import RetrieveTPUEmbeddingAdadeltaParameters
from tensorflow.python.ops.gen_tpu_ops import RetrieveTPUEmbeddingAdagradMomentumParameters
from tensorflow.python.ops.gen_tpu_ops import RetrieveTPUEmbeddingAdagradParameters
from tensorflow.python.ops.gen_tpu_ops import RetrieveTPUEmbeddingCenteredRMSPropParameters
from tensorflow.python.ops.gen_tpu_ops import RetrieveTPUEmbeddingFTRLParameters
from tensorflow.python.ops.gen_tpu_ops import RetrieveTPUEmbeddingFrequencyEstimatorParameters
from tensorflow.python.ops.gen_tpu_ops import RetrieveTPUEmbeddingMDLAdagradLightParameters
from tensorflow.python.ops.gen_tpu_ops import RetrieveTPUEmbeddingMomentumParameters
from tensorflow.python.ops.gen_tpu_ops import RetrieveTPUEmbeddingProximalAdagradParameters
from tensorflow.python.ops.gen_tpu_ops import RetrieveTPUEmbeddingProximalYogiParameters
from tensorflow.python.ops.gen_tpu_ops import RetrieveTPUEmbeddingRMSPropParameters
from tensorflow.python.ops.gen_tpu_ops import RetrieveTPUEmbeddingStochasticGradientDescentParameters
from tensorflow.python.ops.gen_tpu_ops import SendTPUEmbeddingGradients
from tensorflow.python.ops.gen_tpu_ops import ShutdownDistributedTPU
from tensorflow.python.ops.gen_tpu_ops import TPUCompilationResult
from tensorflow.python.ops.gen_tpu_ops import TPUEmbeddingActivations
from tensorflow.python.ops.gen_tpu_ops import TPUOrdinalSelector
from tensorflow.python.ops.gen_tpu_ops import TPUPartitionedCall
from tensorflow.python.ops.gen_tpu_ops import TPUReplicateMetadata
from tensorflow.python.ops.gen_tpu_ops import TPUReplicatedInput
from tensorflow.python.ops.gen_tpu_ops import TPUReplicatedOutput
from tensorflow.python.ops.gen_tpu_ops import WorkerHeartbeat
from tensorflow.python.ops.gen_tpu_ops import XlaConcatND
from tensorflow.python.ops.gen_tpu_ops import XlaSplitND
from tensorflow.python.ops.gen_tpu_partition_ops import TPUPartitionedInput
from tensorflow.python.ops.gen_tpu_partition_ops import TPUPartitionedOutput
from tensorflow.python.ops.gen_training_ops import ApplyAdaMax
from tensorflow.python.ops.gen_training_ops import ApplyAdadelta
from tensorflow.python.ops.gen_training_ops import ApplyAdagrad
from tensorflow.python.ops.gen_training_ops import ApplyAdagradDA
from tensorflow.python.ops.gen_training_ops import ApplyAdagradV2
from tensorflow.python.ops.gen_training_ops import ApplyAdam
from tensorflow.python.ops.gen_training_ops import ApplyAddSign
from tensorflow.python.ops.gen_training_ops import ApplyCenteredRMSProp
from tensorflow.python.ops.gen_training_ops import ApplyFtrl
from tensorflow.python.ops.gen_training_ops import ApplyFtrlV2
from tensorflow.python.ops.gen_training_ops import ApplyGradientDescent
from tensorflow.python.ops.gen_training_ops import ApplyMomentum
from tensorflow.python.ops.gen_training_ops import ApplyPowerSign
from tensorflow.python.ops.gen_training_ops import ApplyProximalAdagrad
from tensorflow.python.ops.gen_training_ops import ApplyProximalGradientDescent
from tensorflow.python.ops.gen_training_ops import ApplyRMSProp
from tensorflow.python.ops.gen_training_ops import ResourceApplyAdaMax
from tensorflow.python.ops.gen_training_ops import ResourceApplyAdadelta
from tensorflow.python.ops.gen_training_ops import ResourceApplyAdagrad
from tensorflow.python.ops.gen_training_ops import ResourceApplyAdagradDA
from tensorflow.python.ops.gen_training_ops import ResourceApplyAdagradV2
from tensorflow.python.ops.gen_training_ops import ResourceApplyAdam
from tensorflow.python.ops.gen_training_ops import ResourceApplyAdamWithAmsgrad
from tensorflow.python.ops.gen_training_ops import ResourceApplyAddSign
from tensorflow.python.ops.gen_training_ops import ResourceApplyCenteredRMSProp
from tensorflow.python.ops.gen_training_ops import ResourceApplyFtrl
from tensorflow.python.ops.gen_training_ops import ResourceApplyFtrlV2
from tensorflow.python.ops.gen_training_ops import ResourceApplyGradientDescent
from tensorflow.python.ops.gen_training_ops import ResourceApplyKerasMomentum
from tensorflow.python.ops.gen_training_ops import ResourceApplyMomentum
from tensorflow.python.ops.gen_training_ops import ResourceApplyPowerSign
from tensorflow.python.ops.gen_training_ops import ResourceApplyProximalAdagrad
from tensorflow.python.ops.gen_training_ops import ResourceApplyProximalGradientDescent
from tensorflow.python.ops.gen_training_ops import ResourceApplyRMSProp
from tensorflow.python.ops.gen_training_ops import ResourceSparseApplyAdadelta
from tensorflow.python.ops.gen_training_ops import ResourceSparseApplyAdagrad
from tensorflow.python.ops.gen_training_ops import ResourceSparseApplyAdagradDA
from tensorflow.python.ops.gen_training_ops import ResourceSparseApplyAdagradV2
from tensorflow.python.ops.gen_training_ops import ResourceSparseApplyCenteredRMSProp
from tensorflow.python.ops.gen_training_ops import ResourceSparseApplyFtrl
from tensorflow.python.ops.gen_training_ops import ResourceSparseApplyFtrlV2
from tensorflow.python.ops.gen_training_ops import ResourceSparseApplyKerasMomentum
from tensorflow.python.ops.gen_training_ops import ResourceSparseApplyMomentum
from tensorflow.python.ops.gen_training_ops import ResourceSparseApplyProximalAdagrad
from tensorflow.python.ops.gen_training_ops import ResourceSparseApplyProximalGradientDescent
from tensorflow.python.ops.gen_training_ops import ResourceSparseApplyRMSProp
from tensorflow.python.ops.gen_training_ops import SparseApplyAdadelta
from tensorflow.python.ops.gen_training_ops import SparseApplyAdagrad
from tensorflow.python.ops.gen_training_ops import SparseApplyAdagradDA
from tensorflow.python.ops.gen_training_ops import SparseApplyAdagradV2
from tensorflow.python.ops.gen_training_ops import SparseApplyCenteredRMSProp
from tensorflow.python.ops.gen_training_ops import SparseApplyFtrl
from tensorflow.python.ops.gen_training_ops import SparseApplyFtrlV2
from tensorflow.python.ops.gen_training_ops import SparseApplyMomentum
from tensorflow.python.ops.gen_training_ops import SparseApplyProximalAdagrad
from tensorflow.python.ops.gen_training_ops import SparseApplyProximalGradientDescent
from tensorflow.python.ops.gen_training_ops import SparseApplyRMSProp
from tensorflow.python.ops.linalg.sparse.gen_sparse_csr_matrix_ops import CSRSparseMatrixComponents
from tensorflow.python.ops.linalg.sparse.gen_sparse_csr_matrix_ops import CSRSparseMatrixToDense
from tensorflow.python.ops.linalg.sparse.gen_sparse_csr_matrix_ops import CSRSparseMatrixToSparseTensor
from tensorflow.python.ops.linalg.sparse.gen_sparse_csr_matrix_ops import DenseToCSRSparseMatrix
from tensorflow.python.ops.linalg.sparse.gen_sparse_csr_matrix_ops import SparseMatrixAdd
from tensorflow.python.ops.linalg.sparse.gen_sparse_csr_matrix_ops import SparseMatrixMatMul
from tensorflow.python.ops.linalg.sparse.gen_sparse_csr_matrix_ops import SparseMatrixMul
from tensorflow.python.ops.linalg.sparse.gen_sparse_csr_matrix_ops import SparseMatrixNNZ
from tensorflow.python.ops.linalg.sparse.gen_sparse_csr_matrix_ops import SparseMatrixOrderingAMD
from tensorflow.python.ops.linalg.sparse.gen_sparse_csr_matrix_ops import SparseMatrixSoftmax
from tensorflow.python.ops.linalg.sparse.gen_sparse_csr_matrix_ops import SparseMatrixSoftmaxGrad
from tensorflow.python.ops.linalg.sparse.gen_sparse_csr_matrix_ops import SparseMatrixSparseCholesky
from tensorflow.python.ops.linalg.sparse.gen_sparse_csr_matrix_ops import SparseMatrixSparseMatMul
from tensorflow.python.ops.linalg.sparse.gen_sparse_csr_matrix_ops import SparseMatrixTranspose
from tensorflow.python.ops.linalg.sparse.gen_sparse_csr_matrix_ops import SparseMatrixZeros
from tensorflow.python.ops.linalg.sparse.gen_sparse_csr_matrix_ops import SparseTensorToCSRSparseMatrix
from tensorflow.python.user_ops.ops.gen_user_ops import Fact