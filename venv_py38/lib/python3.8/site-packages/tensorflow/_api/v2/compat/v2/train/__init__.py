# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Support for training models.

See the [Training](https://tensorflow.org/api_guides/python/train) guide.

"""

import sys as _sys

from . import experimental
from tensorflow.python.eager.remote import ServerDef
from tensorflow.python.training.checkpoint_management import CheckpointManager
from tensorflow.python.training.checkpoint_management import get_checkpoint_state
from tensorflow.python.training.checkpoint_management import latest_checkpoint
from tensorflow.python.training.checkpoint_utils import checkpoints_iterator
from tensorflow.python.training.checkpoint_utils import list_variables
from tensorflow.python.training.checkpoint_utils import load_checkpoint
from tensorflow.python.training.checkpoint_utils import load_variable
from tensorflow.python.training.coordinator import Coordinator
from tensorflow.python.training.moving_averages import ExponentialMovingAverage
from tensorflow.python.training.saving.checkpoint_options import CheckpointOptions
from tensorflow.python.training.server_lib import ClusterSpec
from tensorflow.python.training.tracking.util import Checkpoint
from tensorflow.python.training.training import BytesList
from tensorflow.python.training.training import ClusterDef
from tensorflow.python.training.training import Example
from tensorflow.python.training.training import Feature
from tensorflow.python.training.training import FeatureList
from tensorflow.python.training.training import FeatureLists
from tensorflow.python.training.training import Features
from tensorflow.python.training.training import FloatList
from tensorflow.python.training.training import Int64List
from tensorflow.python.training.training import JobDef
from tensorflow.python.training.training import SequenceExample