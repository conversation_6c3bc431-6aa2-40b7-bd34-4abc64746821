# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.__internal__.distribute.combinations namespace.
"""

import sys as _sys

from tensorflow.python.distribute.combinations import generate
from tensorflow.python.distribute.strategy_combinations import central_storage_strategy_with_gpu_and_cpu
from tensorflow.python.distribute.strategy_combinations import central_storage_strategy_with_two_gpus
from tensorflow.python.distribute.strategy_combinations import cloud_tpu_strategy
from tensorflow.python.distribute.strategy_combinations import default_strategy
from tensorflow.python.distribute.strategy_combinations import mirrored_strategy_with_cpu_1_and_2
from tensorflow.python.distribute.strategy_combinations import mirrored_strategy_with_gpu_and_cpu
from tensorflow.python.distribute.strategy_combinations import mirrored_strategy_with_one_cpu
from tensorflow.python.distribute.strategy_combinations import mirrored_strategy_with_one_gpu
from tensorflow.python.distribute.strategy_combinations import mirrored_strategy_with_two_gpus
from tensorflow.python.distribute.strategy_combinations import mirrored_strategy_with_two_gpus_no_merge_call
from tensorflow.python.distribute.strategy_combinations import multi_worker_mirrored_2x1_cpu
from tensorflow.python.distribute.strategy_combinations import multi_worker_mirrored_2x1_gpu
from tensorflow.python.distribute.strategy_combinations import multi_worker_mirrored_2x2_gpu
from tensorflow.python.distribute.strategy_combinations import multi_worker_mirrored_2x2_gpu_no_merge_call
from tensorflow.python.distribute.strategy_combinations import one_device_strategy
from tensorflow.python.distribute.strategy_combinations import one_device_strategy_gpu
from tensorflow.python.distribute.strategy_combinations import parameter_server_strategy_1worker_2ps_1gpu
from tensorflow.python.distribute.strategy_combinations import parameter_server_strategy_1worker_2ps_cpu
from tensorflow.python.distribute.strategy_combinations import parameter_server_strategy_3worker_2ps_1gpu
from tensorflow.python.distribute.strategy_combinations import parameter_server_strategy_3worker_2ps_cpu
from tensorflow.python.distribute.strategy_combinations import tpu_strategy
from tensorflow.python.distribute.strategy_combinations import tpu_strategy_one_core
from tensorflow.python.distribute.strategy_combinations import tpu_strategy_packed_var