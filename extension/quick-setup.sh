#!/bin/bash
# No Music Extension - Quick Setup Script

echo "🎵 No Music Chrome Extension - Quick Setup"
echo "=========================================="

# Check if we're in the right directory
if [ ! -f "manifest.json" ]; then
    echo "❌ Error: Please run this script from the extension directory"
    echo "Usage: cd extension && ./quick-setup.sh"
    exit 1
fi

echo "📁 Checking extension files..."

# Required files
required_files=(
    "manifest.json"
    "popup.html"
    "popup.js"
    "content.js"
    "background.js"
    "styles.css"
    "content.css"
)

missing_files=()

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file (missing)"
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -gt 0 ]; then
    echo ""
    echo "❌ Missing required files. Please ensure all files are present."
    exit 1
fi

echo ""
echo "🎨 Checking icons..."

# Create icons directory if it doesn't exist
if [ ! -d "icons" ]; then
    echo "📁 Creating icons directory..."
    mkdir icons
fi

# Check for icon files
icon_files=("icon16.png" "icon32.png" "icon48.png" "icon128.png")
missing_icons=()

for icon in "${icon_files[@]}"; do
    if [ -f "icons/$icon" ]; then
        echo "✅ icons/$icon"
    else
        echo "⚠️  icons/$icon (missing)"
        missing_icons+=("$icon")
    fi
done

if [ ${#missing_icons[@]} -gt 0 ]; then
    echo ""
    echo "⚠️  Some icons are missing. To create them:"
    echo "1. Open create_icons.html in your browser"
    echo "2. Click on each icon to download"
    echo "3. Save them in the icons/ directory"
    echo ""
    echo "Do you want to open the icon creator now? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        if command -v xdg-open > /dev/null; then
            xdg-open create_icons.html
        elif command -v open > /dev/null; then
            open create_icons.html
        else
            echo "Please open create_icons.html manually in your browser"
        fi
    fi
fi

echo ""
echo "🔧 Checking server status..."

# Check if server is running
if curl -s "http://127.0.0.1:3333/health" > /dev/null 2>&1; then
    echo "✅ No Music server is running on port 3333"
else
    echo "❌ No Music server is not running"
    echo ""
    echo "To start the server:"
    echo "1. Go to the main project directory"
    echo "2. Run: source venv_py38/bin/activate"
    echo "3. Run: python start.py"
    echo ""
    echo "Do you want to check the main project directory? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        if [ -f "../start.py" ]; then
            echo "✅ Found start.py in parent directory"
            echo "Run: cd .. && source venv_py38/bin/activate && python start.py"
        else
            echo "❌ start.py not found in parent directory"
            echo "Please navigate to the correct project directory"
        fi
    fi
fi

echo ""
echo "📋 Installation Instructions:"
echo "1. Open Chrome and go to chrome://extensions/"
echo "2. Enable 'Developer mode' (top right toggle)"
echo "3. Click 'Load unpacked'"
echo "4. Select this extension directory"
echo "5. The extension should appear in your toolbar"

echo ""
echo "🧪 Testing Instructions:"
echo "1. Make sure the No Music server is running"
echo "2. Go to any YouTube video"
echo "3. Click the extension icon"
echo "4. Click 'إزالة الموسيقى' to test"

echo ""
echo "📚 Documentation:"
echo "- README.md: Complete documentation"
echo "- INSTALL.md: Detailed installation guide"

echo ""
if [ ${#missing_icons[@]} -eq 0 ] && curl -s "http://127.0.0.1:3333/health" > /dev/null 2>&1; then
    echo "🎉 Setup complete! Your extension is ready to install."
else
    echo "⚠️  Setup incomplete. Please address the issues above."
fi

echo ""
echo "Happy music-free YouTube watching! 🎧"
