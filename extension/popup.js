// No Music Extension - Popup Script
class NoMusicPopup {
    constructor() {
        this.serverUrl = 'http://127.0.0.1:3333';
        this.currentTab = null;
        this.currentVideo = null;
        this.processedVideos = new Map();
        
        this.initializeElements();
        this.loadSettings();
        this.loadProcessedVideos();
        this.checkCurrentTab();
        this.checkServerStatus();
        this.setupEventListeners();
        
        // Auto-refresh server status every 30 seconds
        setInterval(() => this.checkServerStatus(), 30000);
    }
    
    initializeElements() {
        // Main elements
        this.processBtn = document.getElementById('processBtn');
        this.playProcessedBtn = document.getElementById('playProcessedBtn');
        this.processingStatus = document.getElementById('processingStatus');
        this.statusText = document.getElementById('statusText');
        this.progressFill = document.getElementById('progressFill');
        
        // Video info elements
        this.videoTitle = document.getElementById('videoTitle');
        this.videoChannel = document.getElementById('videoChannel');
        this.videoThumbnail = document.getElementById('videoThumbnail');
        
        // Server status elements
        this.serverStatus = document.getElementById('serverStatus');
        this.statusIndicator = document.getElementById('statusIndicator');
        this.statusDot = this.statusIndicator.querySelector('.status-dot');
        this.serverStatusText = this.statusIndicator.querySelector('span:last-child');
        
        // History elements
        this.historyList = document.getElementById('historyList');
        this.emptyState = document.getElementById('emptyState');
        
        // Settings elements
        this.autoPlayProcessed = document.getElementById('autoPlayProcessed');
        this.showNotifications = document.getElementById('showNotifications');
        
        // Control buttons
        this.refreshServerBtn = document.getElementById('refreshServerBtn');
        this.clearHistoryBtn = document.getElementById('clearHistoryBtn');
    }
    
    setupEventListeners() {
        // Main action buttons
        this.processBtn.addEventListener('click', () => this.processCurrentVideo());
        this.playProcessedBtn.addEventListener('click', () => this.playProcessedAudio());
        
        // Control buttons
        this.refreshServerBtn.addEventListener('click', () => this.checkServerStatus());
        this.clearHistoryBtn.addEventListener('click', () => this.clearHistory());
        
        // Settings
        this.autoPlayProcessed.addEventListener('change', () => this.saveSettings());
        this.showNotifications.addEventListener('change', () => this.saveSettings());
        
        // Listen for messages from content script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
        });
    }
    
    async checkCurrentTab() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            this.currentTab = tab;
            
            if (this.isYouTubeVideo(tab.url)) {
                // Get video info from content script
                chrome.tabs.sendMessage(tab.id, { action: 'getVideoInfo' }, (response) => {
                    if (response && response.videoInfo) {
                        this.updateVideoInfo(response.videoInfo);
                        this.checkIfVideoProcessed(response.videoInfo.videoId);
                    }
                });
            } else {
                this.updateVideoInfo(null);
            }
        } catch (error) {
            console.error('Error checking current tab:', error);
            this.updateVideoInfo(null);
        }
    }
    
    isYouTubeVideo(url) {
        return url && (url.includes('youtube.com/watch') || url.includes('youtu.be/'));
    }
    
    updateVideoInfo(videoInfo) {
        if (videoInfo) {
            this.currentVideo = videoInfo;
            this.videoTitle.textContent = videoInfo.title || 'فيديو YouTube';
            this.videoChannel.textContent = videoInfo.channel || 'قناة غير معروفة';
            
            if (videoInfo.thumbnail) {
                this.videoThumbnail.style.backgroundImage = `url(${videoInfo.thumbnail})`;
            }
            
            this.processBtn.disabled = false;
        } else {
            this.currentVideo = null;
            this.videoTitle.textContent = 'لا يوجد فيديو مفتوح';
            this.videoChannel.textContent = 'يرجى فتح فيديو YouTube';
            this.videoThumbnail.style.backgroundImage = '';
            this.processBtn.disabled = true;
            this.playProcessedBtn.style.display = 'none';
        }
    }
    
    async checkServerStatus() {
        try {
            const response = await fetch(`${this.serverUrl}/health`, {
                method: 'GET',
                timeout: 5000
            });
            
            if (response.ok) {
                this.updateServerStatus(true, 'الخادم متصل');
            } else {
                this.updateServerStatus(false, 'خطأ في الخادم');
            }
        } catch (error) {
            this.updateServerStatus(false, 'الخادم غير متصل');
        }
    }
    
    updateServerStatus(online, message) {
        this.statusDot.className = `status-dot ${online ? 'online' : 'offline'}`;
        this.serverStatusText.textContent = message;
        
        // Disable process button if server is offline
        if (!online && this.processBtn) {
            this.processBtn.disabled = true;
        } else if (online && this.currentVideo) {
            this.processBtn.disabled = false;
        }
    }
    
    async processCurrentVideo() {
        if (!this.currentVideo) return;
        
        try {
            this.showProcessingStatus(true);
            this.updateProcessingProgress(0, 'بدء المعالجة...');
            
            // Send request to server
            const response = await fetch(`${this.serverUrl}/process`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    url: this.currentVideo.url
                })
            });
            
            if (!response.ok) {
                throw new Error(`Server error: ${response.status}`);
            }
            
            const result = await response.json();
            this.updateProcessingProgress(25, 'تم إنشاء المهمة...');
            
            // Start polling for task status
            this.pollTaskStatus(result.task_id);
            
        } catch (error) {
            console.error('Error processing video:', error);
            this.showProcessingStatus(false);
            this.showNotification('حدث خطأ أثناء معالجة الفيديو', 'error');
        }
    }
    
    async pollTaskStatus(taskId) {
        const pollInterval = setInterval(async () => {
            try {
                const response = await fetch(`${this.serverUrl}/tasks/${taskId}`);
                if (!response.ok) {
                    throw new Error('Failed to get task status');
                }
                
                const task = await response.json();
                
                switch (task.status) {
                    case 'processing':
                        this.updateProcessingProgress(50, 'جاري معالجة الصوت...');
                        break;
                    case 'completed':
                        clearInterval(pollInterval);
                        this.updateProcessingProgress(100, 'تمت المعالجة بنجاح!');
                        setTimeout(() => {
                            this.onProcessingComplete(task);
                        }, 1000);
                        break;
                    case 'failed':
                        clearInterval(pollInterval);
                        this.showProcessingStatus(false);
                        this.showNotification('فشلت معالجة الفيديو', 'error');
                        break;
                }
            } catch (error) {
                clearInterval(pollInterval);
                this.showProcessingStatus(false);
                this.showNotification('خطأ في متابعة حالة المعالجة', 'error');
            }
        }, 2000);
        
        // Stop polling after 5 minutes
        setTimeout(() => {
            clearInterval(pollInterval);
            if (this.processingStatus.style.display !== 'none') {
                this.showProcessingStatus(false);
                this.showNotification('انتهت مهلة المعالجة', 'warning');
            }
        }, 300000);
    }
    
    onProcessingComplete(task) {
        this.showProcessingStatus(false);
        
        // Save processed video info
        const videoData = {
            videoId: this.currentVideo.videoId,
            title: this.currentVideo.title,
            channel: this.currentVideo.channel,
            thumbnail: this.currentVideo.thumbnail,
            processedAt: new Date().toISOString(),
            taskId: task.id,
            outputPath: task.output_folder
        };
        
        this.processedVideos.set(this.currentVideo.videoId, videoData);
        this.saveProcessedVideos();
        this.updateHistoryList();
        
        // Show play button
        this.playProcessedBtn.style.display = 'block';
        
        // Show success notification
        this.showNotification('تمت إزالة الموسيقى بنجاح! 🎧', 'success');
        
        // Auto-play if enabled
        if (this.autoPlayProcessed.checked) {
            setTimeout(() => this.playProcessedAudio(), 1000);
        }
    }
    
    showProcessingStatus(show) {
        this.processingStatus.style.display = show ? 'block' : 'none';
        this.processBtn.disabled = show;
    }
    
    updateProcessingProgress(percent, message) {
        this.progressFill.style.width = `${percent}%`;
        this.statusText.textContent = message;
    }
    
    checkIfVideoProcessed(videoId) {
        if (this.processedVideos.has(videoId)) {
            this.playProcessedBtn.style.display = 'block';
        } else {
            this.playProcessedBtn.style.display = 'none';
        }
    }
    
    playProcessedAudio() {
        if (!this.currentVideo) return;
        
        // Send message to content script to play processed audio
        chrome.tabs.sendMessage(this.currentTab.id, {
            action: 'playProcessedAudio',
            videoId: this.currentVideo.videoId
        });
        
        this.showNotification('تم تشغيل الصوت المعالج 🎧', 'success');
    }
    
    loadProcessedVideos() {
        chrome.storage.local.get(['processedVideos'], (result) => {
            if (result.processedVideos) {
                this.processedVideos = new Map(Object.entries(result.processedVideos));
                this.updateHistoryList();
            }
        });
    }
    
    saveProcessedVideos() {
        const videosObj = Object.fromEntries(this.processedVideos);
        chrome.storage.local.set({ processedVideos: videosObj });
    }
    
    updateHistoryList() {
        const videos = Array.from(this.processedVideos.values())
            .sort((a, b) => new Date(b.processedAt) - new Date(a.processedAt));
        
        if (videos.length === 0) {
            this.emptyState.style.display = 'block';
            return;
        }
        
        this.emptyState.style.display = 'none';
        
        // Clear existing items except empty state
        const existingItems = this.historyList.querySelectorAll('.history-item');
        existingItems.forEach(item => item.remove());
        
        videos.forEach(video => {
            const item = this.createHistoryItem(video);
            this.historyList.appendChild(item);
        });
    }
    
    createHistoryItem(video) {
        const item = document.createElement('div');
        item.className = 'history-item';
        
        item.innerHTML = `
            <div class="video-title" title="${video.title}">${video.title}</div>
            <div class="actions">
                <button class="btn-icon-small play-btn" title="تشغيل الصوت المعالج">🎧</button>
                <button class="btn-icon-small download-btn" title="تحميل">📥</button>
                <button class="btn-icon-small delete-btn" title="حذف">🗑️</button>
            </div>
        `;
        
        // Add event listeners
        const playBtn = item.querySelector('.play-btn');
        const downloadBtn = item.querySelector('.download-btn');
        const deleteBtn = item.querySelector('.delete-btn');
        
        playBtn.addEventListener('click', () => {
            chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'playProcessedAudio',
                videoId: video.videoId
            });
        });
        
        downloadBtn.addEventListener('click', () => {
            // TODO: Implement download functionality
            this.showNotification('ميزة التحميل قيد التطوير', 'warning');
        });
        
        deleteBtn.addEventListener('click', () => {
            this.processedVideos.delete(video.videoId);
            this.saveProcessedVideos();
            this.updateHistoryList();
            this.showNotification('تم حذف الفيديو من السجل', 'success');
        });
        
        return item;
    }
    
    clearHistory() {
        if (confirm('هل تريد مسح جميع الفيديوهات المعالجة؟')) {
            this.processedVideos.clear();
            this.saveProcessedVideos();
            this.updateHistoryList();
            this.showNotification('تم مسح السجل', 'success');
        }
    }
    
    loadSettings() {
        chrome.storage.sync.get(['autoPlayProcessed', 'showNotifications'], (result) => {
            this.autoPlayProcessed.checked = result.autoPlayProcessed !== false;
            this.showNotifications.checked = result.showNotifications !== false;
        });
    }
    
    saveSettings() {
        chrome.storage.sync.set({
            autoPlayProcessed: this.autoPlayProcessed.checked,
            showNotifications: this.showNotifications.checked
        });
    }
    
    showNotification(message, type = 'success') {
        if (!this.showNotifications.checked) return;
        
        // Send notification to content script
        if (this.currentTab) {
            chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'showNotification',
                message: message,
                type: type
            });
        }
    }
    
    handleMessage(message, sender, sendResponse) {
        switch (message.action) {
            case 'videoChanged':
                this.updateVideoInfo(message.videoInfo);
                this.checkIfVideoProcessed(message.videoInfo.videoId);
                break;
            case 'processingUpdate':
                this.updateProcessingProgress(message.progress, message.status);
                break;
        }
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new NoMusicPopup();
});
