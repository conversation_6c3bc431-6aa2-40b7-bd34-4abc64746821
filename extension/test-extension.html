<!DOCTYPE html>
<html>
<head>
    <title>Test No Music Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🎵 No Music Extension - Test Page</h1>
    
    <div class="test-section">
        <h3>1. Server Connection Test</h3>
        <button class="btn-primary" onclick="testServerHealth()">Test Server Health</button>
        <button class="btn-primary" onclick="testServerTasks()">Test Get Tasks</button>
        <div id="serverResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Process Video Test</h3>
        <input type="text" id="videoUrl" placeholder="YouTube URL" style="width: 400px; padding: 8px;">
        <button class="btn-success" onclick="testProcessVideo()">Test Process Video</button>
        <div id="processResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Extension Communication Test</h3>
        <button class="btn-warning" onclick="testExtensionInstalled()">Check Extension</button>
        <button class="btn-warning" onclick="testContentScript()">Test Content Script</button>
        <div id="extensionResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. Storage Test</h3>
        <button class="btn-primary" onclick="testStorage()">Test Chrome Storage</button>
        <button class="btn-danger" onclick="clearStorage()">Clear Storage</button>
        <div id="storageResult" class="result"></div>
    </div>

    <script>
        const SERVER_URL = 'http://127.0.0.1:3333';
        
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.innerHTML = message;
        }
        
        async function testServerHealth() {
            try {
                const response = await fetch(`${SERVER_URL}/health`);
                const data = await response.json();
                showResult('serverResult', `✅ Server is healthy: ${JSON.stringify(data)}`, 'success');
            } catch (error) {
                showResult('serverResult', `❌ Server error: ${error.message}`, 'error');
            }
        }
        
        async function testServerTasks() {
            try {
                const response = await fetch(`${SERVER_URL}/tasks`);
                const data = await response.json();
                showResult('serverResult', `✅ Tasks: ${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                showResult('serverResult', `❌ Tasks error: ${error.message}`, 'error');
            }
        }
        
        async function testProcessVideo() {
            const url = document.getElementById('videoUrl').value || 'https://youtube.com/watch?v=dQw4w9WgXcQ';
            
            try {
                showResult('processResult', '🔄 Sending process request...', 'info');
                
                const response = await fetch(`${SERVER_URL}/process`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        url: url,
                        title: 'Test Video'
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                showResult('processResult', `✅ Process started: ${JSON.stringify(data, null, 2)}`, 'success');
                
                // Start polling for task status
                if (data.task_id) {
                    pollTaskStatus(data.task_id);
                }
                
            } catch (error) {
                showResult('processResult', `❌ Process error: ${error.message}`, 'error');
            }
        }
        
        async function pollTaskStatus(taskId) {
            try {
                const response = await fetch(`${SERVER_URL}/tasks/${taskId}`);
                const task = await response.json();
                
                showResult('processResult', 
                    `🔄 Task ${taskId}: ${task.status}\n${JSON.stringify(task, null, 2)}`, 
                    task.status === 'completed' ? 'success' : 
                    task.status === 'failed' ? 'error' : 'info'
                );
                
                if (task.status === 'processing' || task.status === 'pending') {
                    setTimeout(() => pollTaskStatus(taskId), 2000);
                }
                
            } catch (error) {
                showResult('processResult', `❌ Polling error: ${error.message}`, 'error');
            }
        }
        
        function testExtensionInstalled() {
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
                showResult('extensionResult', `✅ Extension installed: ${chrome.runtime.id}`, 'success');
            } else {
                showResult('extensionResult', '❌ Extension not detected', 'error');
            }
        }
        
        function testContentScript() {
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({action: 'test'}, (response) => {
                    if (chrome.runtime.lastError) {
                        showResult('extensionResult', `❌ Content script error: ${chrome.runtime.lastError.message}`, 'error');
                    } else {
                        showResult('extensionResult', `✅ Content script response: ${JSON.stringify(response)}`, 'success');
                    }
                });
            } else {
                showResult('extensionResult', '❌ Chrome extension API not available', 'error');
            }
        }
        
        function testStorage() {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.local.get(null, (items) => {
                    showResult('storageResult', `✅ Storage contents: ${JSON.stringify(items, null, 2)}`, 'success');
                });
            } else {
                showResult('storageResult', '❌ Chrome storage API not available', 'error');
            }
        }
        
        function clearStorage() {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.local.clear(() => {
                    chrome.storage.sync.clear(() => {
                        showResult('storageResult', '✅ Storage cleared', 'success');
                    });
                });
            } else {
                showResult('storageResult', '❌ Chrome storage API not available', 'error');
            }
        }
        
        // Auto-test server on load
        window.addEventListener('load', () => {
            testServerHealth();
        });
    </script>
</body>
</html>
