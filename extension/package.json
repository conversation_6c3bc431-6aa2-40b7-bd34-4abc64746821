{"name": "no-music-chrome-extension", "version": "1.0.0", "description": "Chrome extension for removing music from YouTube videos using AI", "main": "background.js", "scripts": {"build": "echo 'Building extension...' && npm run validate", "validate": "echo 'Validating manifest...' && node -e \"JSON.parse(require('fs').readFileSync('manifest.json', 'utf8')); console.log('✅ manifest.json is valid')\"", "package": "zip -r ../no-music-extension-v1.0.zip * -x '*.md' 'create_icons.html' 'package.json' 'quick-setup.sh' 'node_modules/*'", "setup": "./quick-setup.sh", "icons": "echo 'Open create_icons.html in your browser to generate icons'"}, "keywords": ["youtube", "music", "audio", "chrome-extension", "ai", "spleeter"], "author": "No Music Desktop Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-repo/no-music-desktop"}, "bugs": {"url": "https://github.com/your-repo/no-music-desktop/issues"}, "homepage": "https://github.com/your-repo/no-music-desktop#readme", "devDependencies": {}, "dependencies": {}, "extensionInfo": {"manifestVersion": 3, "targetBrowser": "Chrome 88+", "permissions": ["tabs", "storage", "activeTab", "scripting"], "hostPermissions": ["https://*.youtube.com/*", "https://*.youtu.be/*", "http://localhost:3333/*", "http://127.0.0.1:3333/*"], "contentScripts": ["content.js"], "backgroundScript": "background.js", "popup": "popup.html"}}