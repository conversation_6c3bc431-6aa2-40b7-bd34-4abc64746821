# دليل تثبيت إضافة No Music

## خطوات التثبيت السريع

### 1. إنشاء الأيقونات
```bash
# افتح ملف إنشاء الأيقونات
cd extension
open create_icons.html  # أو افتحه في المتصفح
```

1. انقر على كل أيقونة لتحميلها
2. احفظ الأيقونات في مجلد `icons/` بالأسماء التالية:
   - `icon16.png`
   - `icon32.png` 
   - `icon48.png`
   - `icon128.png`

### 2. تثبيت الإضافة في Chrome

#### الطريقة الأولى: Developer Mode
1. افتح Chrome
2. اذهب إلى `chrome://extensions/`
3. فعّل "Developer mode" (الزاوية العلوية اليمنى)
4. انقر "Load unpacked"
5. اختر مجلد `extension/`

#### الطريقة الثانية: إنشاء حزمة CRX
```bash
# في مجلد extension
zip -r no-music-extension.zip *
# ثم غيّر الامتداد من .zip إلى .crx
```

### 3. التحقق من التثبيت
1. يجب أن تظهر أيقونة الإضافة في شريط الأدوات
2. انقر على الأيقونة للتأكد من ظهور الواجهة
3. اذهب إلى فيديو YouTube واختبر الإضافة

## متطلبات ما قبل التثبيت

### تشغيل الخادم المحلي
```bash
# في مجلد المشروع الرئيسي
source venv_py38/bin/activate
python start.py
```

تأكد من أن الخادم يعمل على `http://127.0.0.1:3333`

### فحص الملفات المطلوبة
```bash
ls extension/
# يجب أن تشاهد:
# manifest.json
# popup.html
# popup.js  
# content.js
# background.js
# styles.css
# content.css
# README.md
# INSTALL.md
# create_icons.html
# icons/ (بعد إنشاء الأيقونات)
```

## استكشاف مشاكل التثبيت

### مشكلة: الإضافة لا تظهر
**الحل:**
1. تأكد من تفعيل Developer mode
2. تحقق من وجود جميع الملفات
3. راجع أخطاء التحميل في `chrome://extensions/`

### مشكلة: أخطاء في manifest.json
**الحل:**
1. تحقق من صحة JSON syntax
2. تأكد من وجود جميع الصلاحيات المطلوبة
3. راجع إصدار Manifest (يجب أن يكون 3)

### مشكلة: الأيقونات لا تظهر
**الحل:**
1. تأكد من إنشاء جميع أحجام الأيقونات
2. تحقق من مسارات الأيقونات في manifest.json
3. تأكد من صيغة PNG للأيقونات

### مشكلة: Content Script لا يعمل
**الحل:**
1. تحقق من صلاحيات المواقع في manifest.json
2. تأكد من تطابق patterns مع YouTube
3. راجع Console في DevTools

## اختبار الإضافة

### اختبار أساسي
1. اذهب إلى `https://youtube.com/watch?v=dQw4w9WgXcQ`
2. انقر على أيقونة الإضافة
3. يجب أن تظهر معلومات الفيديو
4. انقر "إزالة الموسيقى" (تأكد من تشغيل الخادم)

### اختبار متقدم
```javascript
// في Console المتصفح على صفحة YouTube
chrome.runtime.sendMessage({action: 'getVideoInfo'}, console.log);
```

### فحص التخزين
```javascript
// فحص الإعدادات
chrome.storage.sync.get(null, console.log);

// فحص الفيديوهات المعالجة  
chrome.storage.local.get(['processedVideos'], console.log);
```

## تحديث الإضافة

### تحديث ملفات الإضافة
1. عدّل الملفات المطلوبة
2. اذهب إلى `chrome://extensions/`
3. انقر زر "Reload" تحت الإضافة

### تحديث الإصدار
1. عدّل `version` في `manifest.json`
2. أعد تحميل الإضافة
3. تحقق من رقم الإصدار الجديد

## نشر الإضافة (اختياري)

### إنشاء حزمة للنشر
```bash
# إنشاء ملف مضغوط
cd extension
zip -r ../no-music-extension-v1.0.zip * -x "*.md" "create_icons.html"
```

### متطلبات Chrome Web Store
1. حساب مطور في Chrome Web Store
2. دفع رسوم التسجيل ($5)
3. مراجعة سياسات المتجر
4. إضافة وصف ولقطات شاشة

## الأمان والخصوصية

### الصلاحيات المطلوبة
- `tabs`: للوصول لمعلومات التبويبات
- `storage`: لحفظ الإعدادات والسجل
- `activeTab`: للتفاعل مع التبويب النشط
- `scripting`: لحقن Content Scripts

### البيانات المحفوظة
- **محلياً**: قائمة الفيديوهات المعالجة
- **متزامن**: إعدادات المستخدم
- **لا يتم إرسال**: أي بيانات لخوادم خارجية

## الدعم الفني

### ملفات السجل
```bash
# سجلات الخادم
tail -f logs/app.log

# سجلات المتصفح
# افتح DevTools > Console
```

### معلومات التشخيص
```javascript
// معلومات الإضافة
chrome.runtime.getManifest();

// حالة الخادم
fetch('http://127.0.0.1:3333/health').then(r => r.json()).then(console.log);
```

### الإبلاغ عن المشاكل
عند الإبلاغ عن مشكلة، يرجى تضمين:
1. إصدار Chrome
2. إصدار الإضافة  
3. رسائل الخطأ من Console
4. خطوات إعادة إنتاج المشكلة

---

**نصيحة**: احتفظ بنسخة احتياطية من مجلد `extension/` قبل إجراء أي تعديلات.
