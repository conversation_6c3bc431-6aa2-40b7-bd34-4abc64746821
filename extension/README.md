# No Music - Chrome Extension

إضافة Chrome احترافية لإزالة الموسيقى من فيديوهات YouTube باستخدام الذكاء الاصطناعي.

## المميزات

### 🎯 الوظائف الأساسية
- **إزالة الموسيقى**: استخدام تقنية Spleeter لفصل الصوت
- **تشغيل متزامن**: مزامنة الصوت المعالج مع الفيديو
- **واجهة عربية**: دعم كامل للغة العربية مع RTL
- **حفظ السجل**: تذكر الفيديوهات المعالجة مسبقاً

### 🖥️ واجهة المستخدم
- **Popup احترافي**: واجهة جميلة ومتجاوبة
- **إشعارات ذكية**: تنبيهات للمستخدم حول حالة المعالجة
- **تحكم كامل**: إيقاف/تشغيل الصوت المعالج
- **سجل المعالجة**: قائمة بجميع الفيديوهات المعالجة

## التثبيت

### 1. تحضير الملفات
```bash
# تأكد من وجود جميع الملفات في مجلد extension/
ls extension/
# يجب أن تشاهد:
# manifest.json, popup.html, popup.js, content.js, background.js, styles.css, content.css
```

### 2. إنشاء الأيقونات
1. افتح `extension/create_icons.html` في المتصفح
2. انقر على كل أيقونة لتحميلها
3. احفظ الأيقونات في مجلد `extension/icons/`

### 3. تثبيت الإضافة في Chrome
1. افتح Chrome واذهب إلى `chrome://extensions/`
2. فعّل "Developer mode" في الزاوية العلوية اليمنى
3. انقر على "Load unpacked"
4. اختر مجلد `extension/`
5. ستظهر الإضافة في قائمة الإضافات

## الاستخدام

### 🚀 البدء السريع
1. **تشغيل الخادم**: تأكد من تشغيل خادم No Music Desktop
   ```bash
   python start.py
   ```

2. **فتح فيديو YouTube**: اذهب إلى أي فيديو على YouTube

3. **استخدام الإضافة**: انقر على أيقونة الإضافة واضغط "إزالة الموسيقى"

### 📋 الميزات التفصيلية

#### معالجة الفيديوهات
- انقر على "إزالة الموسيقى" لبدء المعالجة
- ستظهر طبقة معالجة فوق الفيديو
- يتم إيقاف الفيديو مؤقتاً أثناء المعالجة
- بعد الانتهاء، يتم تشغيل الصوت المعالج تلقائياً

#### التحكم في الصوت
- **مزامنة تلقائية**: الصوت المعالج يتبع الفيديو
- **تحكم في الصوت**: استخدم أزرار YouTube العادية
- **إيقاف/تشغيل**: يمكن إيقاف الصوت المعالج في أي وقت

#### السجل والإعدادات
- **سجل المعالجة**: جميع الفيديوهات المعالجة محفوظة
- **تشغيل تلقائي**: تفعيل/إلغاء التشغيل التلقائي للفيديوهات المعالجة
- **الإشعارات**: تفعيل/إلغاء الإشعارات

## متطلبات النظام

### الخادم المحلي
- **No Music Desktop**: يجب تشغيل الخادم على `http://127.0.0.1:3333`
- **Python 3.8+**: مع جميع المكتبات المطلوبة
- **Spleeter**: لمعالجة الصوت

### المتصفح
- **Chrome 88+**: أو أي متصفح يدعم Manifest V3
- **اتصال إنترنت**: لتحميل فيديوهات YouTube

## هيكل الملفات

```
extension/
├── manifest.json          # إعدادات الإضافة
├── popup.html             # واجهة المستخدم
├── popup.js               # منطق الواجهة
├── content.js             # التحكم في YouTube
├── background.js          # خدمة الخلفية
├── styles.css             # تصميم الواجهة
├── content.css            # تصميم طبقة المعالجة
├── create_icons.html      # أداة إنشاء الأيقونات
├── icons/                 # أيقونات الإضافة
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md              # هذا الملف
```

## API الخادم

### نقاط النهاية المطلوبة
```
POST /process              # معالجة فيديو جديد
GET  /tasks/{id}           # حالة المهمة
GET  /health               # فحص حالة الخادم
```

### مثال على الطلب
```javascript
// معالجة فيديو
fetch('http://127.0.0.1:3333/process', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        url: 'https://youtube.com/watch?v=VIDEO_ID'
    })
});
```

## استكشاف الأخطاء

### مشاكل شائعة

#### الخادم غير متصل
- تأكد من تشغيل `python start.py`
- تحقق من أن الخادم يعمل على المنفذ 3333
- تأكد من عدم حجب Firewall للاتصال

#### الإضافة لا تعمل
- تحقق من وجود جميع الملفات
- تأكد من تفعيل Developer mode
- راجع Console في Chrome DevTools

#### مشاكل الصوت
- تأكد من دعم المتصفح لـ Web Audio API
- تحقق من إعدادات الصوت في Chrome
- جرب إعادة تحميل الصفحة

### سجلات التشخيص
```javascript
// في Console المتصفح
chrome.storage.local.get(['processedVideos'], console.log);
chrome.storage.sync.get(['autoPlayProcessed'], console.log);
```

## التطوير

### إضافة ميزات جديدة
1. عدّل الملفات المناسبة
2. أعد تحميل الإضافة في `chrome://extensions/`
3. اختبر الميزة الجديدة

### تصحيح الأخطاء
- استخدم Chrome DevTools
- تحقق من Background Script logs
- راجع Content Script console

## الترخيص

هذه الإضافة جزء من مشروع No Music Desktop وتخضع لنفس الترخيص.

## الدعم

للحصول على المساعدة:
1. تحقق من سجلات الخادم
2. راجع Console المتصفح
3. تأكد من تحديث جميع المكونات

---

**No Music Extension v1.0** - إزالة الموسيقى من YouTube بذكاء اصطناعي
