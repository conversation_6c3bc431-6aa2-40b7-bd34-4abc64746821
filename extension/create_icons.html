<!DOCTYPE html>
<html>
<head>
    <title>No Music Extension Icons</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .icon-container {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        .icon {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 5px;
        }
        canvas {
            display: block;
        }
    </style>
</head>
<body>
    <h1>No Music Extension Icons</h1>
    <p>Right-click on each icon and save as PNG</p>
    
    <div class="icon-container">
        <div class="icon">
            <canvas id="icon16" width="16" height="16"></canvas>
        </div>
        <div>16x16</div>
    </div>
    
    <div class="icon-container">
        <div class="icon">
            <canvas id="icon32" width="32" height="32"></canvas>
        </div>
        <div>32x32</div>
    </div>
    
    <div class="icon-container">
        <div class="icon">
            <canvas id="icon48" width="48" height="48"></canvas>
        </div>
        <div>48x48</div>
    </div>
    
    <div class="icon-container">
        <div class="icon">
            <canvas id="icon128" width="128" height="128"></canvas>
        </div>
        <div>128x128</div>
    </div>

    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const center = size / 2;
            const radius = size * 0.4;
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Music note with slash
            ctx.strokeStyle = 'white';
            ctx.fillStyle = 'white';
            ctx.lineWidth = size * 0.08;
            ctx.lineCap = 'round';
            
            // Music note
            const noteSize = size * 0.3;
            const noteX = center - noteSize * 0.2;
            const noteY = center + noteSize * 0.2;
            
            // Note stem
            ctx.beginPath();
            ctx.moveTo(noteX + noteSize * 0.7, noteY);
            ctx.lineTo(noteX + noteSize * 0.7, noteY - noteSize * 1.2);
            ctx.stroke();
            
            // Note head
            ctx.beginPath();
            ctx.ellipse(noteX, noteY, noteSize * 0.3, noteSize * 0.2, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // Slash (prohibition sign)
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = size * 0.12;
            ctx.beginPath();
            ctx.moveTo(center - radius * 0.7, center - radius * 0.7);
            ctx.lineTo(center + radius * 0.7, center + radius * 0.7);
            ctx.stroke();
            
            // Circle border
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = size * 0.08;
            ctx.beginPath();
            ctx.arc(center, center, radius, 0, 2 * Math.PI);
            ctx.stroke();
        }
        
        // Draw all icon sizes
        drawIcon(document.getElementById('icon16'), 16);
        drawIcon(document.getElementById('icon32'), 32);
        drawIcon(document.getElementById('icon48'), 48);
        drawIcon(document.getElementById('icon128'), 128);
        
        // Function to download canvas as PNG
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Add click handlers for easy download
        document.getElementById('icon16').onclick = () => downloadCanvas(document.getElementById('icon16'), 'icon16.png');
        document.getElementById('icon32').onclick = () => downloadCanvas(document.getElementById('icon32'), 'icon32.png');
        document.getElementById('icon48').onclick = () => downloadCanvas(document.getElementById('icon48'), 'icon48.png');
        document.getElementById('icon128').onclick = () => downloadCanvas(document.getElementById('icon128'), 'icon128.png');
    </script>
</body>
</html>
