// No Music Extension - Content Script
class NoMusicYouTube {
    constructor() {
        this.currentVideoId = null;
        this.currentVideoInfo = null;
        this.processedAudio = null;
        this.audioContext = null;
        this.audioSource = null;
        this.gainNode = null;
        this.isProcessedAudioPlaying = false;
        this.overlay = null;
        this.notification = null;
        this.audioIndicator = null;
        
        this.init();
    }
    
    init() {
        // Wait for YouTube to load
        this.waitForYouTube(() => {
            this.setupVideoDetection();
            this.setupAudioContext();
            this.checkAutoPlay();
        });
        
        // Listen for messages from popup
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
        });
        
        // Listen for navigation changes
        this.setupNavigationListener();
    }
    
    waitForYouTube(callback) {
        const checkYouTube = () => {
            const video = document.querySelector('video');
            const player = document.querySelector('#movie_player');
            
            if (video && player) {
                callback();
            } else {
                setTimeout(checkYouTube, 1000);
            }
        };
        
        checkYouTube();
    }
    
    setupVideoDetection() {
        // Detect video changes
        const observer = new MutationObserver(() => {
            this.detectVideoChange();
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // Initial detection
        this.detectVideoChange();
    }
    
    detectVideoChange() {
        const videoId = this.extractVideoId();
        
        if (videoId && videoId !== this.currentVideoId) {
            this.currentVideoId = videoId;
            this.currentVideoInfo = this.getVideoInfo();
            
            // Reset processed audio state
            this.stopProcessedAudio();
            this.hideAudioIndicator();
            
            // Notify popup about video change
            chrome.runtime.sendMessage({
                action: 'videoChanged',
                videoInfo: this.currentVideoInfo
            });
            
            // Check if this video was processed before
            this.checkProcessedVideo();
        }
    }
    
    extractVideoId() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('v');
    }
    
    getVideoInfo() {
        const video = document.querySelector('video');
        const titleElement = document.querySelector('h1.ytd-watch-metadata yt-formatted-string');
        const channelElement = document.querySelector('#owner #channel-name a');
        
        return {
            videoId: this.currentVideoId,
            url: window.location.href,
            title: titleElement?.textContent?.trim() || 'YouTube Video',
            channel: channelElement?.textContent?.trim() || 'Unknown Channel',
            thumbnail: this.getVideoThumbnail(),
            duration: video?.duration || 0
        };
    }
    
    getVideoThumbnail() {
        // Try to get high quality thumbnail
        const videoId = this.currentVideoId;
        if (videoId) {
            return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
        }
        return null;
    }
    
    setupAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.gainNode = this.audioContext.createGain();
            this.gainNode.connect(this.audioContext.destination);
        } catch (error) {
            console.error('Failed to setup audio context:', error);
        }
    }
    
    setupNavigationListener() {
        // Listen for YouTube navigation
        let lastUrl = location.href;
        new MutationObserver(() => {
            const url = location.href;
            if (url !== lastUrl) {
                lastUrl = url;
                setTimeout(() => this.detectVideoChange(), 1000);
            }
        }).observe(document, { subtree: true, childList: true });
    }
    
    async checkProcessedVideo() {
        // Check if current video is in processed videos
        const result = await chrome.storage.local.get(['processedVideos']);
        const processedVideos = result.processedVideos || {};
        
        if (processedVideos[this.currentVideoId]) {
            // Check settings for auto-play
            const settings = await chrome.storage.sync.get(['autoPlayProcessed']);
            if (settings.autoPlayProcessed !== false) {
                setTimeout(() => {
                    this.playProcessedAudio(this.currentVideoId);
                    this.showNotification('تم تشغيل الفيديو بالصوت المعالج 🎧', 'success');
                }, 2000);
            }
        }
    }
    
    async playProcessedAudio(videoId) {
        try {
            // Get processed video info
            const result = await chrome.storage.local.get(['processedVideos']);
            const processedVideos = result.processedVideos || {};
            const videoData = processedVideos[videoId || this.currentVideoId];
            
            if (!videoData) {
                this.showNotification('لم يتم العثور على الصوت المعالج', 'error');
                return;
            }
            
            // Mute original video
            const video = document.querySelector('video');
            if (video) {
                video.muted = true;
            }
            
            // Load and play processed audio
            await this.loadProcessedAudio(videoData);
            this.showAudioIndicator();
            this.isProcessedAudioPlaying = true;
            
        } catch (error) {
            console.error('Error playing processed audio:', error);
            this.showNotification('خطأ في تشغيل الصوت المعالج', 'error');
        }
    }
    
    async loadProcessedAudio(videoData) {
        try {
            // For now, we'll simulate loading the processed audio
            // In a real implementation, you would fetch the actual audio file
            // from the server or local storage
            
            this.showNotification('جاري تحميل الصوت المعالج...', 'info');
            
            // Simulate audio loading
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Setup audio synchronization with video
            this.setupAudioSync();
            
            this.showNotification('تم تحميل الصوت المعالج بنجاح', 'success');
            
        } catch (error) {
            throw new Error('Failed to load processed audio: ' + error.message);
        }
    }
    
    setupAudioSync() {
        const video = document.querySelector('video');
        if (!video) return;
        
        // Sync processed audio with video playback
        const syncAudio = () => {
            if (this.isProcessedAudioPlaying && this.processedAudio) {
                // Sync audio time with video time
                const timeDiff = Math.abs(this.processedAudio.currentTime - video.currentTime);
                if (timeDiff > 0.1) {
                    this.processedAudio.currentTime = video.currentTime;
                }
            }
        };
        
        // Listen for video events
        video.addEventListener('play', () => {
            if (this.isProcessedAudioPlaying && this.processedAudio) {
                this.processedAudio.play();
            }
        });
        
        video.addEventListener('pause', () => {
            if (this.isProcessedAudioPlaying && this.processedAudio) {
                this.processedAudio.pause();
            }
        });
        
        video.addEventListener('seeked', syncAudio);
        video.addEventListener('timeupdate', syncAudio);
        
        // Volume control for processed audio
        video.addEventListener('volumechange', () => {
            if (this.isProcessedAudioPlaying && this.gainNode) {
                this.gainNode.gain.value = video.muted ? 0 : video.volume;
            }
        });
    }
    
    stopProcessedAudio() {
        if (this.processedAudio) {
            this.processedAudio.pause();
            this.processedAudio = null;
        }
        
        if (this.audioSource) {
            this.audioSource.disconnect();
            this.audioSource = null;
        }
        
        this.isProcessedAudioPlaying = false;
        this.hideAudioIndicator();
        
        // Unmute original video
        const video = document.querySelector('video');
        if (video) {
            video.muted = false;
        }
    }
    
    showProcessingOverlay() {
        this.hideProcessingOverlay(); // Remove existing overlay
        
        const player = document.querySelector('#movie_player');
        if (!player) return;
        
        this.overlay = document.createElement('div');
        this.overlay.className = 'no-music-overlay';
        this.overlay.innerHTML = `
            <div class="overlay-content">
                <div class="overlay-spinner"></div>
                <div class="overlay-title">جاري إزالة الموسيقى</div>
                <div class="overlay-subtitle">يرجى الانتظار...</div>
                <div class="overlay-progress">
                    <div class="overlay-progress-fill" id="overlayProgressFill"></div>
                </div>
                <div class="overlay-status" id="overlayStatus">بدء المعالجة...</div>
            </div>
        `;
        
        player.appendChild(this.overlay);
        
        // Pause video
        const video = document.querySelector('video');
        if (video && !video.paused) {
            video.pause();
        }
    }
    
    hideProcessingOverlay() {
        if (this.overlay) {
            this.overlay.remove();
            this.overlay = null;
        }
    }
    
    updateProcessingProgress(progress, status) {
        if (this.overlay) {
            const progressFill = this.overlay.querySelector('#overlayProgressFill');
            const statusElement = this.overlay.querySelector('#overlayStatus');
            
            if (progressFill) {
                progressFill.style.width = `${progress}%`;
            }
            
            if (statusElement) {
                statusElement.textContent = status;
            }
        }
    }
    
    showAudioIndicator() {
        this.hideAudioIndicator();
        
        const player = document.querySelector('#movie_player');
        if (!player) return;
        
        this.audioIndicator = document.createElement('div');
        this.audioIndicator.className = 'no-music-audio-indicator';
        this.audioIndicator.innerHTML = `
            <span class="audio-indicator-icon">🎧</span>
            <span>صوت معالج</span>
        `;
        
        player.appendChild(this.audioIndicator);
    }
    
    hideAudioIndicator() {
        if (this.audioIndicator) {
            this.audioIndicator.remove();
            this.audioIndicator = null;
        }
    }
    
    showNotification(message, type = 'success') {
        // Remove existing notification
        if (this.notification) {
            this.notification.remove();
        }
        
        this.notification = document.createElement('div');
        this.notification.className = `no-music-notification ${type}`;
        
        const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
        
        this.notification.innerHTML = `
            <span class="notification-icon">${icon}</span>
            <span class="notification-text">${message}</span>
            <button class="notification-close">×</button>
        `;
        
        document.body.appendChild(this.notification);
        
        // Show notification
        setTimeout(() => {
            this.notification.classList.add('show');
        }, 100);
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            this.hideNotification();
        }, 5000);
        
        // Close button
        this.notification.querySelector('.notification-close').addEventListener('click', () => {
            this.hideNotification();
        });
    }
    
    hideNotification() {
        if (this.notification) {
            this.notification.classList.remove('show');
            setTimeout(() => {
                if (this.notification) {
                    this.notification.remove();
                    this.notification = null;
                }
            }, 300);
        }
    }
    
    handleMessage(message, sender, sendResponse) {
        switch (message.action) {
            case 'getVideoInfo':
                sendResponse({ videoInfo: this.currentVideoInfo });
                break;
                
            case 'playProcessedAudio':
                this.playProcessedAudio(message.videoId);
                break;
                
            case 'stopProcessedAudio':
                this.stopProcessedAudio();
                break;
                
            case 'showProcessingOverlay':
                this.showProcessingOverlay();
                break;
                
            case 'hideProcessingOverlay':
                this.hideProcessingOverlay();
                break;
                
            case 'updateProcessingProgress':
                this.updateProcessingProgress(message.progress, message.status);
                break;
                
            case 'showNotification':
                this.showNotification(message.message, message.type);
                break;
        }
    }
}

// Initialize when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new NoMusicYouTube();
    });
} else {
    new NoMusicYouTube();
}
