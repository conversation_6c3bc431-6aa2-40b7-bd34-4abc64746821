// No Music Extension - Background Script (Service Worker)
class NoMusicBackground {
    constructor() {
        this.serverUrl = 'http://127.0.0.1:3333';
        this.activeTasks = new Map();
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Handle extension installation
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstallation(details);
        });
        
        // Handle messages from content scripts and popup
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });
        
        // Handle tab updates
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdate(tabId, changeInfo, tab);
        });
        
        // Handle tab activation
        chrome.tabs.onActivated.addListener((activeInfo) => {
            this.handleTabActivation(activeInfo);
        });
        
        // Periodic server health check
        this.startHealthCheck();
    }
    
    handleInstallation(details) {
        if (details.reason === 'install') {
            // First time installation
            this.showWelcomeNotification();
            this.initializeStorage();
        } else if (details.reason === 'update') {
            // Extension update
            this.handleUpdate(details.previousVersion);
        }
    }
    
    showWelcomeNotification() {
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon48.png',
            title: 'No Music Extension',
            message: 'تم تثبيت الإضافة بنجاح! يمكنك الآن إزالة الموسيقى من فيديوهات YouTube.'
        });
    }
    
    initializeStorage() {
        // Initialize default settings
        chrome.storage.sync.set({
            autoPlayProcessed: true,
            showNotifications: true,
            serverUrl: this.serverUrl
        });
        
        // Initialize local storage
        chrome.storage.local.set({
            processedVideos: {},
            processingQueue: []
        });
    }
    
    handleUpdate(previousVersion) {
        console.log(`Extension updated from ${previousVersion} to ${chrome.runtime.getManifest().version}`);
        // Handle any migration logic here
    }
    
    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'processVideo':
                    await this.processVideo(message.videoInfo, sender.tab);
                    break;
                    
                case 'getServerStatus':
                    const status = await this.checkServerStatus();
                    sendResponse({ status });
                    break;
                    
                case 'getProcessedVideos':
                    const videos = await this.getProcessedVideos();
                    sendResponse({ videos });
                    break;
                    
                case 'downloadProcessedAudio':
                    await this.downloadProcessedAudio(message.videoId);
                    break;
                    
                case 'clearProcessedVideos':
                    await this.clearProcessedVideos();
                    sendResponse({ success: true });
                    break;
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ error: error.message });
        }
    }
    
    handleTabUpdate(tabId, changeInfo, tab) {
        // Check if it's a YouTube video page
        if (changeInfo.status === 'complete' && this.isYouTubeVideo(tab.url)) {
            // Inject content script if needed
            this.ensureContentScript(tabId);
        }
    }
    
    handleTabActivation(activeInfo) {
        // Update badge or perform other actions when tab is activated
        chrome.tabs.get(activeInfo.tabId, (tab) => {
            if (this.isYouTubeVideo(tab.url)) {
                this.updateBadge(activeInfo.tabId);
            }
        });
    }
    
    isYouTubeVideo(url) {
        return url && (url.includes('youtube.com/watch') || url.includes('youtu.be/'));
    }
    
    async ensureContentScript(tabId) {
        try {
            // Check if content script is already injected
            const results = await chrome.scripting.executeScript({
                target: { tabId },
                func: () => window.noMusicExtensionLoaded
            });
            
            if (!results[0]?.result) {
                // Inject content script
                await chrome.scripting.executeScript({
                    target: { tabId },
                    files: ['content.js']
                });
                
                await chrome.scripting.insertCSS({
                    target: { tabId },
                    files: ['content.css']
                });
            }
        } catch (error) {
            console.error('Error injecting content script:', error);
        }
    }
    
    async processVideo(videoInfo, tab) {
        try {
            // Show processing overlay
            chrome.tabs.sendMessage(tab.id, {
                action: 'showProcessingOverlay'
            });
            
            // Send request to server
            const response = await fetch(`${this.serverUrl}/process`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    url: videoInfo.url,
                    title: videoInfo.title
                })
            });
            
            if (!response.ok) {
                throw new Error(`Server error: ${response.status}`);
            }
            
            const result = await response.json();
            
            // Store task info
            this.activeTasks.set(result.task_id, {
                videoInfo,
                tabId: tab.id,
                startTime: Date.now()
            });
            
            // Start polling task status
            this.pollTaskStatus(result.task_id);
            
            return result;
            
        } catch (error) {
            // Hide processing overlay
            chrome.tabs.sendMessage(tab.id, {
                action: 'hideProcessingOverlay'
            });
            
            // Show error notification
            chrome.tabs.sendMessage(tab.id, {
                action: 'showNotification',
                message: 'خطأ في الاتصال بالخادم',
                type: 'error'
            });
            
            throw error;
        }
    }
    
    async pollTaskStatus(taskId) {
        const pollInterval = setInterval(async () => {
            try {
                const response = await fetch(`${this.serverUrl}/tasks/${taskId}`);
                if (!response.ok) {
                    throw new Error('Failed to get task status');
                }
                
                const task = await response.json();
                const taskInfo = this.activeTasks.get(taskId);
                
                if (!taskInfo) {
                    clearInterval(pollInterval);
                    return;
                }
                
                // Update progress
                let progress = 25;
                let status = 'جاري المعالجة...';
                
                switch (task.status) {
                    case 'pending':
                        progress = 25;
                        status = 'في قائمة الانتظار...';
                        break;
                    case 'processing':
                        progress = 50;
                        status = 'جاري معالجة الصوت...';
                        break;
                    case 'completed':
                        progress = 100;
                        status = 'تمت المعالجة بنجاح!';
                        clearInterval(pollInterval);
                        await this.onTaskCompleted(taskId, task);
                        break;
                    case 'failed':
                        clearInterval(pollInterval);
                        await this.onTaskFailed(taskId, task);
                        break;
                }
                
                // Send progress update
                chrome.tabs.sendMessage(taskInfo.tabId, {
                    action: 'updateProcessingProgress',
                    progress,
                    status
                });
                
            } catch (error) {
                console.error('Error polling task status:', error);
                clearInterval(pollInterval);
                this.onTaskFailed(taskId, { error: error.message });
            }
        }, 2000);
        
        // Stop polling after 10 minutes
        setTimeout(() => {
            clearInterval(pollInterval);
            const taskInfo = this.activeTasks.get(taskId);
            if (taskInfo) {
                chrome.tabs.sendMessage(taskInfo.tabId, {
                    action: 'hideProcessingOverlay'
                });
                chrome.tabs.sendMessage(taskInfo.tabId, {
                    action: 'showNotification',
                    message: 'انتهت مهلة المعالجة',
                    type: 'warning'
                });
            }
            this.activeTasks.delete(taskId);
        }, 600000);
    }
    
    async onTaskCompleted(taskId, task) {
        const taskInfo = this.activeTasks.get(taskId);
        if (!taskInfo) return;
        
        // Save processed video info
        const videoData = {
            videoId: taskInfo.videoInfo.videoId,
            title: taskInfo.videoInfo.title,
            channel: taskInfo.videoInfo.channel,
            thumbnail: taskInfo.videoInfo.thumbnail,
            processedAt: new Date().toISOString(),
            taskId: task.id,
            outputPath: task.output_folder || task.output_dir
        };
        
        // Update storage
        const result = await chrome.storage.local.get(['processedVideos']);
        const processedVideos = result.processedVideos || {};
        processedVideos[taskInfo.videoInfo.videoId] = videoData;
        await chrome.storage.local.set({ processedVideos });
        
        // Hide processing overlay
        chrome.tabs.sendMessage(taskInfo.tabId, {
            action: 'hideProcessingOverlay'
        });
        
        // Show success notification
        chrome.tabs.sendMessage(taskInfo.tabId, {
            action: 'showNotification',
            message: 'تمت إزالة الموسيقى بنجاح! 🎧',
            type: 'success'
        });
        
        // Update badge
        this.updateBadge(taskInfo.tabId, 'success');
        
        // Clean up
        this.activeTasks.delete(taskId);
    }
    
    async onTaskFailed(taskId, task) {
        const taskInfo = this.activeTasks.get(taskId);
        if (!taskInfo) return;
        
        // Hide processing overlay
        chrome.tabs.sendMessage(taskInfo.tabId, {
            action: 'hideProcessingOverlay'
        });
        
        // Show error notification
        chrome.tabs.sendMessage(taskInfo.tabId, {
            action: 'showNotification',
            message: 'فشلت معالجة الفيديو',
            type: 'error'
        });
        
        // Update badge
        this.updateBadge(taskInfo.tabId, 'error');
        
        // Clean up
        this.activeTasks.delete(taskId);
    }
    
    async checkServerStatus() {
        try {
            const response = await fetch(`${this.serverUrl}/health`, {
                method: 'GET',
                signal: AbortSignal.timeout(5000)
            });
            
            return {
                online: response.ok,
                status: response.ok ? 'connected' : 'error'
            };
        } catch (error) {
            return {
                online: false,
                status: 'offline',
                error: error.message
            };
        }
    }
    
    async getProcessedVideos() {
        const result = await chrome.storage.local.get(['processedVideos']);
        return result.processedVideos || {};
    }
    
    async clearProcessedVideos() {
        await chrome.storage.local.set({ processedVideos: {} });
    }
    
    updateBadge(tabId, status = null) {
        if (status === 'success') {
            chrome.action.setBadgeText({ text: '✓', tabId });
            chrome.action.setBadgeBackgroundColor({ color: '#27ae60', tabId });
        } else if (status === 'error') {
            chrome.action.setBadgeText({ text: '✗', tabId });
            chrome.action.setBadgeBackgroundColor({ color: '#e74c3c', tabId });
        } else {
            chrome.action.setBadgeText({ text: '', tabId });
        }
    }
    
    startHealthCheck() {
        // Check server health every 60 seconds
        setInterval(async () => {
            const status = await this.checkServerStatus();
            // You can store this status and use it for notifications
        }, 60000);
    }
}

// Initialize background script
new NoMusicBackground();
