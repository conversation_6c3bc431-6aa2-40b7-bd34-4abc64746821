/* No Music Extension - Content Styles */

/* Processing Overlay */
.no-music-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    color: white;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    border-radius: 8px;
    backdrop-filter: blur(5px);
}

.no-music-overlay.hidden {
    display: none;
}

.overlay-content {
    text-align: center;
    padding: 30px;
}

.overlay-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #fff;
    border-radius: 50%;
    animation: overlay-spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes overlay-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.overlay-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #fff;
}

.overlay-subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin-bottom: 20px;
}

.overlay-progress {
    width: 200px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    overflow: hidden;
    margin: 0 auto;
}

.overlay-progress-fill {
    height: 100%;
    background: #27ae60;
    border-radius: 3px;
    transition: width 0.3s ease;
    width: 0%;
}

.overlay-status {
    font-size: 14px;
    margin-top: 15px;
    opacity: 0.8;
}

/* Success Notification */
.no-music-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #27ae60;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 10000;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 10px;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.no-music-notification.show {
    transform: translateX(0);
}

.no-music-notification.error {
    background: #e74c3c;
}

.no-music-notification.warning {
    background: #f39c12;
}

.notification-icon {
    font-size: 18px;
}

.notification-text {
    flex: 1;
}

.notification-close {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 0;
    margin-left: 10px;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.notification-close:hover {
    opacity: 1;
}

/* Audio Controls Indicator */
.no-music-audio-indicator {
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    z-index: 1000;
    backdrop-filter: blur(5px);
    transition: opacity 0.3s ease;
}

.no-music-audio-indicator.hidden {
    opacity: 0;
    pointer-events: none;
}

.audio-indicator-icon {
    font-size: 14px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

/* Volume Control Override */
.no-music-volume-control {
    position: absolute;
    bottom: 60px;
    left: 10px;
    background: rgba(0, 0, 0, 0.8);
    padding: 10px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 1000;
    color: white;
    font-size: 12px;
}

.volume-slider {
    width: 80px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    position: relative;
    cursor: pointer;
}

.volume-slider-fill {
    height: 100%;
    background: #27ae60;
    border-radius: 2px;
    transition: width 0.1s ease;
}

.volume-slider-thumb {
    position: absolute;
    top: -4px;
    width: 12px;
    height: 12px;
    background: #27ae60;
    border-radius: 50%;
    cursor: pointer;
    transition: transform 0.1s ease;
}

.volume-slider-thumb:hover {
    transform: scale(1.2);
}

/* Hide original YouTube audio controls when processed audio is active */
.no-music-active .ytp-volume-area {
    opacity: 0.3;
    pointer-events: none;
}

/* Processing button in YouTube controls */
.no-music-control-btn {
    background: none;
    border: none;
    color: white;
    font-size: 14px;
    padding: 8px;
    cursor: pointer;
    border-radius: 4px;
    transition: background 0.2s;
    margin: 0 4px;
}

.no-music-control-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.no-music-control-btn.processing {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .no-music-overlay {
        padding: 20px;
    }
    
    .overlay-title {
        font-size: 20px;
    }
    
    .overlay-subtitle {
        font-size: 14px;
    }
    
    .overlay-progress {
        width: 150px;
    }
    
    .no-music-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        transform: translateY(-100%);
    }
    
    .no-music-notification.show {
        transform: translateY(0);
    }
}
