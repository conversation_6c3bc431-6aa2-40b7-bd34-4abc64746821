/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    direction: rtl;
    width: 380px;
    min-height: 500px;
}

.container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Header */
.header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 5px;
}

.logo .icon {
    font-size: 24px;
}

.logo h1 {
    font-size: 20px;
    font-weight: bold;
}

.subtitle {
    font-size: 12px;
    opacity: 0.9;
}

/* Current Video Section */
.current-video {
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.video-info {
    display: flex;
    gap: 12px;
    margin-bottom: 15px;
}

.video-thumbnail {
    width: 60px;
    height: 45px;
    background: #f0f0f0;
    border-radius: 6px;
    background-size: cover;
    background-position: center;
    flex-shrink: 0;
}

.video-details h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-details p {
    font-size: 12px;
    color: #666;
}

/* Buttons */
.action-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #27ae60;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #229954;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #3498db;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #2980b9;
    transform: translateY(-1px);
}

.btn-icon-small {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background 0.2s;
}

.btn-icon-small:hover {
    background: #f0f0f0;
}

/* Processing Status */
.processing-status {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: 15px;
}

.spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e0e0e0;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 10px;
}

.progress-fill {
    height: 100%;
    background: #3498db;
    border-radius: 3px;
    transition: width 0.3s ease;
    width: 0%;
}

/* Server Status */
.server-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #95a5a6;
    transition: background 0.3s;
}

.status-dot.online {
    background: #27ae60;
}

.status-dot.offline {
    background: #e74c3c;
}

/* History Section */
.history-section {
    padding: 20px;
    max-height: 200px;
    overflow-y: auto;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.section-header h3 {
    font-size: 14px;
    font-weight: 600;
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.history-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    transition: background 0.2s;
}

.history-item:hover {
    background: #e9ecef;
}

.history-item .video-title {
    flex: 1;
    font-size: 12px;
    font-weight: 500;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.history-item .actions {
    display: flex;
    gap: 5px;
}

.empty-state {
    text-align: center;
    padding: 30px 20px;
    color: #666;
}

.empty-state .empty-icon {
    font-size: 24px;
    margin-bottom: 10px;
    display: block;
}

/* Settings */
.settings-section {
    padding: 20px;
    border-top: 1px solid #eee;
    background: #f8f9fa;
}

.setting-item {
    margin-bottom: 12px;
}

.setting-item label {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 12px;
    cursor: pointer;
}

.setting-item input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 16px;
    height: 16px;
    border: 2px solid #ddd;
    border-radius: 3px;
    position: relative;
    transition: all 0.2s;
}

.setting-item input[type="checkbox"]:checked + .checkmark {
    background: #3498db;
    border-color: #3498db;
}

.setting-item input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Footer */
.footer {
    padding: 15px 20px;
    background: #2c3e50;
    color: white;
    text-align: center;
    font-size: 11px;
}

.footer p {
    margin-bottom: 8px;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.footer-links a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.2s;
}

.footer-links a:hover {
    color: white;
}

/* Scrollbar */
.history-section::-webkit-scrollbar {
    width: 4px;
}

.history-section::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.history-section::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.history-section::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
