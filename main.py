#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
No Music Desktop Application
============================

برنامج سطح مكتب لإزالة الموسيقى من فيديوهات YouTube
يعمل كأداة مستقلة وكخادم API لإضافة المتصفح

المطور: Assistant
الإصدار: 1.0.0
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_environment():
    """Setup the application environment"""
    # Create necessary directories
    directories = [
        "data/temp",
        "data/processed", 
        "logs",
        "gui",
        "server",
        "processing",
        "tasks"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    # Setup basic logging
    log_file = os.path.join("logs", "app.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("=" * 50)
    logger.info("No Music Desktop Application Starting...")
    logger.info("=" * 50)
    
    return logger

def check_dependencies():
    """Check if all required dependencies are installed"""
    # Core packages (required)
    core_packages = ['fastapi', 'uvicorn', 'yt_dlp', 'requests']
    # Optional packages (for full functionality)
    optional_packages = ['PyQt5', 'spleeter', 'tensorflow', 'librosa', 'soundfile']

    missing_core = []
    missing_optional = []

    # Check core packages
    for package in core_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_core.append(package)

    # Check optional packages
    for package in optional_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_optional.append(package)

    if missing_core:
        print("❌ المكتبات الأساسية التالية مفقودة:")
        for package in missing_core:
            print(f"   - {package}")
        print("\nيرجى تثبيت المكتبات الأساسية باستخدام:")
        print("python3 -m pip install fastapi uvicorn yt-dlp requests aiofiles python-multipart pydantic")
        return False

    if missing_optional:
        print("⚠️ المكتبات الاختيارية التالية مفقودة:")
        for package in missing_optional:
            print(f"   - {package}")
        print("\nللحصول على الوظائف الكاملة، قم بتثبيت:")
        if 'PyQt5' in missing_optional:
            print("python3 -m pip install PyQt5  # للواجهة الرسومية")
        if any(p in missing_optional for p in ['spleeter', 'tensorflow', 'librosa', 'soundfile']):
            print("python3 -m pip install librosa soundfile tensorflow spleeter  # لمعالجة الصوت")
        print("\nيمكنك تشغيل الخادم فقط باستخدام:")
        print("python3 run_server_only.py")
        print()

    print("✅ المكتبات الأساسية متوفرة")
    return True

def main():
    """Main application entry point"""
    try:
        # Setup environment
        logger = setup_environment()

        # Check dependencies
        if not check_dependencies():
            sys.exit(1)

        # Use the unified startup script
        logger.info("Starting unified startup script...")
        print("🚀 تشغيل النظام الموحد...")

        # Import and run startup script
        from start import main as start_main
        return start_main()

    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف البرنامج بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        logging.exception("Unexpected error occurred")
        return 1

if __name__ == "__main__":
    sys.exit(main())
