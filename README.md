# No Music Desktop - إزالة الموسيقى من الفيديوهات

برنامج سطح مكتب احترافي لإزالة الموسيقى من فيديوهات YouTube باستخدام تقنية الذكاء الاصطناعي.

## المميزات

### 🎯 الوظائف الأساسية
- **معالجة محلية**: تحميل ومعالجة الصوت بالكامل على جهازك
- **فصل الصوت**: استخدام Spleeter لفصل الصوت إلى vocals/accompaniment
- **واجهة عربية**: دعم كامل للغة العربية مع RTL
- **خادم API**: يعمل كخادم محلي لإضافة المتصفح

### 🌐 واجهة المستخدم
- **واجهة ويب حديثة**: تصميم عربي جميل مع دعم RTL
- **معالجة الفيديوهات**: إدخال روابط YouTube ومتابعة المهام
- **تحديث تلقائي**: متابعة حالة المهام في الوقت الفعلي
- **متجاوبة**: تعمل على جميع الأجهزة والمتصفحات

### 🔧 المواصفات التقنية
- **Python 3.8+**
- **واجهة ويب** بدلاً من PyQt5 (لتجنب مشاكل التوافق)
- **FastAPI** للخادم المحلي
- **yt-dlp** لتحميل الصوت من YouTube
- **Spleeter** لفصل الصوت
- **TensorFlow** لمعالجة الذكاء الاصطناعي

## التثبيت

### 1. متطلبات النظام
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3.8 python3-pip ffmpeg

# Windows
# تثبيت Python 3.8+ من python.org
# تثبيت FFmpeg من ffmpeg.org

# macOS
brew install python@3.8 ffmpeg
```

### 2. تثبيت المكتبات
```bash
# استنساخ المشروع
git clone <repository-url>
cd no-music-desktop

# تثبيت المكتبات المطلوبة
pip install -r requirements.txt
```

### 3. تشغيل البرنامج
```bash
# الطريقة الأولى: تشغيل كامل (خادم + واجهة ويب)
python main.py

# الطريقة الثانية: تشغيل الخادم فقط
python run_server_only.py

# الطريقة الثالثة: إنشاء واجهة الويب فقط
python run_web_gui.py
```

## الاستخدام

### 🌐 الاستخدام عبر واجهة الويب
1. شغل البرنامج: `python main.py`
2. ستفتح واجهة الويب تلقائياً في المتصفح
3. أدخل رابط YouTube في الحقل المخصص
4. اضغط "🚀 معالجة الفيديو"
5. تابع حالة المعالجة من خلال "🔄 تحديث الحالة"
6. ستجد النتائج في مجلد `data/processed`

### 🔗 استخدام API مباشرة
```bash
# تشغيل الخادم
python run_server_only.py

# إرسال طلب معالجة
curl -X POST "http://127.0.0.1:3333/process" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://youtube.com/watch?v=VIDEO_ID"}'

# التحقق من المهام
curl "http://127.0.0.1:3333/tasks"
```

## هيكل المشروع

```
no-music-desktop/
├── main.py                 # نقطة التشغيل الرئيسية
├── run_server_only.py      # تشغيل الخادم فقط
├── run_web_gui.py          # إنشاء واجهة الويب
├── web_gui.html           # ملف واجهة الويب
├── requirements.txt        # المكتبات المطلوبة
├── README.md              # هذا الملف
├── server/                # خادم FastAPI
│   ├── __init__.py
│   └── api.py
├── processing/            # معالجة الصوت
│   ├── __init__.py
│   ├── downloader.py      # تحميل من YouTube
│   └── processor.py       # معالجة بـ Spleeter
├── tasks/                 # إدارة المهام
│   ├── __init__.py
│   └── task_manager.py
├── data/                  # البيانات
│   ├── temp/             # ملفات مؤقتة
│   ├── processed/        # المخرجات النهائية
│   ├── tasks.json        # قاعدة بيانات المهام
│   └── settings.json     # إعدادات البرنامج
├── logs/                  # ملفات السجلات
└── venv_py38/             # البيئة الافتراضية
    └── app.log
```

## API الخادم

### نقاط النهاية (Endpoints)

#### `POST /process`
معالجة رابط YouTube جديد
```json
{
  "url": "https://youtube.com/watch?v=...",
  "title": "عنوان الفيديو (اختياري)"
}
```

#### `GET /tasks`
الحصول على جميع المهام

#### `GET /tasks/{task_id}`
الحصول على مهمة محددة

#### `DELETE /tasks/{task_id}`
حذف مهمة محددة

### المنفذ الافتراضي
الخادم يعمل على المنفذ `3333` افتراضياً

## استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في تثبيت Spleeter
```bash
# تأكد من تثبيت TensorFlow أولاً
pip install tensorflow==2.13.1
pip install spleeter==2.4.0
```

#### خطأ في FFmpeg
```bash
# Ubuntu/Debian
sudo apt install ffmpeg

# Windows: تحميل من ffmpeg.org وإضافة إلى PATH
# macOS
brew install ffmpeg
```

#### مشاكل في PyQt5
```bash
# Ubuntu/Debian
sudo apt install python3-pyqt5

# Windows/macOS
pip install PyQt5==5.15.10
```

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

للحصول على الدعم:
- افتح Issue في GitHub
- راجع ملفات السجلات في مجلد `logs/`
- تأكد من تثبيت جميع المتطلبات بشكل صحيح
