import logging
import async<PERSON>
from typing import Optional
from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
import threading

# Try to import PyQt5, make it optional
try:
    from PyQt5.QtCore import QObject, pyqtSignal
    PYQT5_AVAILABLE = True
except ImportError:
    PYQT5_AVAILABLE = False
    # Create dummy classes for when PyQt5 is not available
    class QObject:
        def __init__(self):
            pass

    def pyqtSignal(*args, **kwargs):
        def dummy_signal(*args, **kwargs):
            pass
        return dummy_signal

from tasks.task_manager import TaskManager, TaskStatus
from processing.downloader import YouTubeDownloader
from processing.processor import AudioProcessor

class ProcessRequest(BaseModel):
    url: str
    title: Optional[str] = None

class ProcessResponse(BaseModel):
    task_id: str
    message: str
    status: str
    output_dir: Optional[str] = None

class APIServer(QObject):
    # Signals for GUI communication
    log_message = pyqtSignal(str)
    server_status_changed = pyqtSignal(bool)  # True for running, False for stopped
    
    def __init__(self, task_manager: TaskManager, port: int = 3333):
        super().__init__()
        self.task_manager = task_manager
        self.port = port
        self.app = FastAPI(title="No Music Desktop API", version="1.0.0")
        self.server = None
        self.server_thread = None
        self.is_running = False
        
        # Initialize processors
        self.downloader = YouTubeDownloader()
        self.processor = AudioProcessor()
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Setup CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # In production, specify exact origins
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Setup routes
        self._setup_routes()

    def emit_signal(self, signal_name: str, *args):
        """Emit signal if PyQt5 is available"""
        if PYQT5_AVAILABLE:
            signal = getattr(self, signal_name, None)
            if signal:
                signal.emit(*args)
    
    def _setup_routes(self):
        @self.app.get("/")
        async def root():
            return {"message": "No Music Desktop API Server", "status": "running"}
        
        @self.app.get("/health")
        async def health_check():
            return {"status": "healthy", "tasks_count": len(self.task_manager.get_all_tasks())}
        
        @self.app.post("/process", response_model=ProcessResponse)
        async def process_youtube_url(request: ProcessRequest, background_tasks: BackgroundTasks):
            self.logger.info(f"Received process request for URL: {request.url}")
            try:
                # Validate URL
                self.logger.info("Validating URL...")
                if not self.downloader.validate_url(request.url):
                    self.logger.error("Invalid YouTube URL")
                    raise HTTPException(status_code=400, detail="رابط YouTube غير صحيح")

                # Get video info
                self.logger.info("Getting video info...")
                video_info = self.downloader.get_video_info(request.url)
                if not video_info:
                    self.logger.error("Failed to get video info")
                    raise HTTPException(status_code=400, detail="فشل في الحصول على معلومات الفيديو")

                # Create task
                title = request.title or video_info.get('title', 'Unknown')
                self.logger.info(f"Creating task for: {title}")
                task_id = self.task_manager.add_task(request.url, title)
                self.logger.info(f"Task created with ID: {task_id}")

                # Start background processing
                self.logger.info(f"Starting background processing for task: {task_id}")
                background_tasks.add_task(self._process_task_async, task_id)

                # Log the request
                log_msg = f"تم استقبال طلب معالجة: {title}"
                self.emit_signal('log_message', log_msg)
                self.logger.info(log_msg)

                return ProcessResponse(
                    task_id=task_id,
                    message="تم إضافة المهمة بنجاح",
                    status="pending",
                    output_dir=self.processor.output_dir
                )

            except HTTPException:
                raise
            except Exception as e:
                error_msg = f"خطأ في معالجة الطلب: {str(e)}"
                self.logger.exception("Exception in process_youtube_url")
                raise HTTPException(status_code=500, detail=error_msg)
        
        @self.app.get("/tasks")
        async def get_all_tasks():
            tasks = self.task_manager.get_all_tasks()
            return [task.to_dict() for task in tasks]
        
        @self.app.get("/tasks/{task_id}")
        async def get_task(task_id: str):
            task = self.task_manager.get_task(task_id)
            if not task:
                raise HTTPException(status_code=404, detail="المهمة غير موجودة")
            return task.to_dict()
        
        @self.app.delete("/tasks/{task_id}")
        async def delete_task(task_id: str):
            task = self.task_manager.get_task(task_id)
            if not task:
                raise HTTPException(status_code=404, detail="المهمة غير موجودة")
            
            self.task_manager.remove_task(task_id)
            return {"message": "تم حذف المهمة بنجاح"}
    
    async def _process_task_async(self, task_id: str):
        """Process a task asynchronously"""
        try:
            task = self.task_manager.get_task(task_id)
            if not task:
                self.logger.error(f"Task {task_id} not found")
                return

            self.logger.info(f"Starting processing for task {task_id}: {task.title}")

            # Update status to processing
            self.task_manager.update_task_status(task_id, TaskStatus.PROCESSING)

            # Download audio
            self.logger.info(f"Downloading audio for task {task_id}")
            success, audio_file, error_msg = self.downloader.download_audio(task.url, task_id)
            if not success:
                self.logger.error(f"Download failed for task {task_id}: {error_msg}")
                self.task_manager.update_task_status(task_id, TaskStatus.FAILED, error_msg)
                return

            self.logger.info(f"Download successful for task {task_id}: {audio_file}")

            # Process audio
            self.logger.info(f"Processing audio for task {task_id}")
            success, vocals_file, error_msg = self.processor.process_audio(audio_file, task_id, task.title)
            if not success:
                self.logger.error(f"Audio processing failed for task {task_id}: {error_msg}")
                self.task_manager.update_task_status(task_id, TaskStatus.FAILED, error_msg)
                return

            self.logger.info(f"Audio processing successful for task {task_id}: {vocals_file}")

            # Update task with results
            import os
            output_folder = os.path.dirname(vocals_file)
            self.task_manager.update_task_output(task_id, output_folder, vocals_file)
            self.task_manager.update_task_status(task_id, TaskStatus.COMPLETED)

            # Cleanup temporary files
            self.downloader.cleanup_temp_files(task_id)

            log_msg = f"تمت معالجة المهمة بنجاح: {task.title}"
            self.emit_signal('log_message', log_msg)
            self.logger.info(log_msg)

        except Exception as e:
            error_msg = f"خطأ في معالجة المهمة: {str(e)}"
            self.logger.exception(f"Exception in task processing {task_id}")
            self.task_manager.update_task_status(task_id, TaskStatus.FAILED, error_msg)
            self.logger.error(error_msg)
    
    def start_server(self):
        """Start the API server in a separate thread"""
        if self.is_running:
            return
        
        def run_server():
            try:
                config = uvicorn.Config(
                    self.app,
                    host="127.0.0.1",
                    port=self.port,
                    log_level="info"
                )
                self.server = uvicorn.Server(config)
                self.server.run()
            except Exception as e:
                error_msg = f"خطأ في تشغيل الخادم: {str(e)}"
                self.logger.error(error_msg)
                self.emit_signal('log_message', error_msg)
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        self.is_running = True
        self.emit_signal('server_status_changed', True)

        log_msg = f"تم تشغيل الخادم على المنفذ {self.port}"
        self.emit_signal('log_message', log_msg)
        self.logger.info(log_msg)
    
    def stop_server(self):
        """Stop the API server"""
        if not self.is_running:
            return
        
        if self.server:
            self.server.should_exit = True
        
        self.is_running = False
        self.emit_signal('server_status_changed', False)

        log_msg = "تم إيقاف الخادم"
        self.emit_signal('log_message', log_msg)
        self.logger.info(log_msg)
